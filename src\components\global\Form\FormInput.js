import React from 'react';
import styled, { useTheme } from 'styled-components';
import PropTypes from 'prop-types';
import { isFunctionEmpty } from '@utils/functions';

import CopyText from '@components/global/Icons/CopyText';

import FormFieldContainer from './FormFieldContainer';
import Form<PERSON><PERSON><PERSON>abel from './FormFieldLabel';
import Clickable from '../Clickable';

const FormInputContainer = styled.div`
  display: flex;
  align-items: center;
  height: 32px;
`;

const FormInputContent = styled.input`
  cursor: ${({ onClick }) => {
    return isFunctionEmpty(onClick) ? '' : 'pointer';
  }};
  width: 100%;
  min-height: 32px;
  background: ${({ readOnly, theme }) => (readOnly ? theme.secondary[200] : theme.secondary[100])};
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  ${({ allowCopy }) => (allowCopy ? 'border-right: none;' : '')}
  box-sizing: border-box;
  border-radius: 4px ${({ allowCopy }) => (allowCopy ? '0px 0px' : '4px 4px')} 4px;
`;

const StyledBottomActionButton = styled(Clickable)`
  color: blue;
  text-decoration: underline;
`;

const FormInput = (props) => {
  const {
    title,
    titleAction = null,
    bottomAction = null,
    placeholder = '',
    readOnly = false,
    required = false,
    name,
    onChange = () => {},
    type = 'text',
    min = null,
    max = null,
    step = null,
    maxLength = null,
    onClick = () => {},
    testid = '',
    allowCopy = false,
    copyTextValue = null,
    compact = false,
    description = null,
  } = props;
  const theme = useTheme();
  let { value } = props;
  if (value == null) value = '';

  let BottomActionButton = null;
  if (bottomAction) {
    const { label, action } = bottomAction;
    BottomActionButton = (
      <StyledBottomActionButton onClick={() => action()}>{label}</StyledBottomActionButton>
    );
  }

  return (
    <FormFieldContainer required={required} fieldName={name} compact={compact}>
      <FormFieldLabel titleAction={titleAction} description={description}>
        {title}
      </FormFieldLabel>
      <FormInputContainer readOnly={readOnly}>
        <FormInputContent
          data-testid={testid}
          readOnly={readOnly || false}
          placeholder={placeholder}
          name={name}
          value={value}
          onChange={onChange}
          type={type}
          // TODO: these don't work. set max and min
          min={min?.toString()}
          max={max?.toString()}
          step={step?.toString()}
          onClick={onClick}
          allowCopy={allowCopy}
          maxLength={maxLength}
        />
        {allowCopy && (
          <CopyText
            isOnInput
            backgroundColor={readOnly ? theme.secondary[200] : theme.secondary[100]}
            copyTextValue={copyTextValue || value || ''}
          />
        )}
      </FormInputContainer>
      {BottomActionButton}
    </FormFieldContainer>
  );
};

FormInput.propTypes = {
  title: PropTypes.string.isRequired,
  titleAction: PropTypes.oneOfType([
    PropTypes.shape({
      action: PropTypes.func,
      label: PropTypes.string,
    }),
    PropTypes.bool,
  ]),
  bottomAction: PropTypes.oneOfType([
    PropTypes.shape({
      action: PropTypes.func,
      label: PropTypes.string,
    }),
    PropTypes.bool,
  ]),
  placeholder: PropTypes.string,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  name: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  type: PropTypes.string,
  min: PropTypes.number,
  max: PropTypes.number,
  step: PropTypes.number,
  maxLength: PropTypes.number,
  onClick: PropTypes.func,
  testid: PropTypes.string,
  compact: PropTypes.bool,
  allowCopy: PropTypes.bool,
  copyTextValue: PropTypes.string,
  description: PropTypes.string,
};

export default FormInput;
