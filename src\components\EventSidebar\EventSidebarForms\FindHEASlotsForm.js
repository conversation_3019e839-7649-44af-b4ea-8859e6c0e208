import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue, useResetRecoilState } from 'recoil';

import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import { SecondaryButton } from '@components/global/Buttons';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  FormMultiselect,
  FormRadioButtons,
  BackButton,
  FormTextBox,
} from '@components/global/Form';
import { LoadingIndicator, Checkbox } from '@components/global';

import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import {
  eventTypeOptionsSelectorFamily,
  jobAttributesSelectorFamily,
  calendarTypeAtom,
} from '@recoil/app';
import { agentsFormOptionsSelector } from '@recoil/agents';
import { selectedEventState, availableSlotsAtom } from '@recoil/eventSidebar';

import {
  returnVisitReasons,
  returnVisits,
  statePrograms,
} from '@utils/businessLogic/heaBusinessLogic';

const FindHEASlotsForm = (props) => {
  const { handleFindSlotsClick, handleSaveClick } = props;

  const [slotInfo, setSlotInfo] = useRecoilState(selectedEventState);
  const {
    includeAgents,
    numUnit,
    type,
    address,
    isInstant,
    lock = false,
    returnReason,
    notes,
    attributes,
    stateProgram,
  } = slotInfo;

  const agents = useRecoilValue(agentsFormOptionsSelector);
  const calendarType = useRecoilValue(calendarTypeAtom);
  const resetAvailableSlots = useResetRecoilState(availableSlotsAtom);
  const eventTypes = useRecoilValue(
    eventTypeOptionsSelectorFamily({
      departmentName: 'HEA',
      showGroups: false,
      stateCode: type?.slice(0, 2) || calendarType?.slice(0, 2),
    }),
  );
  const [loading, setLoading] = useState(false);
  const [displayForm, setDisplayForm] = useState(true);

  useEffect(() => {
    if (!type) setSlotInfo({ ...slotInfo, type: calendarType });
  }, [type, calendarType, setSlotInfo, slotInfo]);

  const onViewSlotsButtonClick = async () => {
    const isValid = await handleFindSlotsClick();
    if (isValid) setDisplayForm(false);
  };

  const onClickBackButton = () => {
    resetAvailableSlots();
    setDisplayForm(true);
  };

  const handleFieldChange = useCallback(
    (e, updatedEvent = slotInfo) => {
      handleFormFieldChange(e, updatedEvent, setSlotInfo);
    },
    [slotInfo, setSlotInfo],
  );

  const handleEventTypeChange = (e) => {
    const { value } = e.target;
    let { numUnit } = slotInfo;
    if (value === '000000') numUnit = 1;

    handleFieldChange(e, { ...slotInfo, numUnit });
  };

  const handleNumUnitChange = (e) => {
    const { name, value } = e.target;

    handleFieldChange({ target: { name, value: Number(value) } });
  };

  const handleCheckbox = (e) => {
    const { name } = e.target;
    handleFieldChange({ target: { name, value: !slotInfo[name] } });
  };

  // Currently showing numUnits for all event types other than regular HEA. Setting this as a variable in case there are other event types that shouldn't show
  const showNumUnitsOptions = type !== '000000';

  const showInstantCheckbox = type && ['000000', '000001', '000002'].includes(type);

  const isReturn = returnVisits.includes(type);

  const showProgramDropdown = type === '000008'; // HVAC Spec Visit

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col size={2}>
            <HeaderTitle>
              {!displayForm && <BackButton onClick={onClickBackButton} />}
              Book Appointment
            </HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            {displayForm ? (
              <>
                <FormSelect
                  title="job type"
                  name="type"
                  value={type}
                  options={eventTypes}
                  onChange={handleEventTypeChange}
                  placeholder="Event Type"
                />
                {showNumUnitsOptions && (
                  <FormRadioButtons
                    title="How many units?"
                    name="numUnit"
                    value={numUnit}
                    options={[
                      { key: '1', value: 1 },
                      { key: '2', value: 2 },
                      { key: '3', value: 3 },
                      { key: '4', value: 4 },
                    ]}
                    onChange={handleNumUnitChange}
                  />
                )}
                {numUnit && Number(numUnit) && (
                  <SfIdInputs sfObjectType="deal" setLoading={setLoading} />
                )}
                {showProgramDropdown && (
                  <FormSelect
                    required
                    title="State Program"
                    name="stateProgram"
                    value={stateProgram}
                    options={statePrograms}
                    onChange={handleFieldChange}
                  />
                )}
                {address?.displayAddress && (
                  <FormInput
                    readOnly
                    title="Address"
                    name="address"
                    value={address.displayAddress}
                  />
                )}
                {showInstantCheckbox && (
                  <Checkbox
                    label="Instant"
                    name="isInstant"
                    value={isInstant}
                    onChange={handleCheckbox}
                  />
                )}

                {isReturn && (
                  <>
                    <FormSelect
                      required
                      title="Reason for Return"
                      placeholder="Select Reason for Return"
                      name="returnReason"
                      value={returnReason}
                      onChange={handleFieldChange}
                      options={returnVisitReasons}
                    />
                  </>
                )}
                <FormMultiselect
                  title="requirement(s)"
                  name="attributes"
                  value={attributes}
                  recoilOptions={jobAttributesSelectorFamily({ type })}
                  onChange={handleFieldChange}
                />
                <FormMultiselect
                  title="Include HES(s)"
                  name="includeAgents"
                  value={includeAgents}
                  options={agents}
                  onChange={handleFieldChange}
                />
              </>
            ) : (
              <>
                <AvailableSlots allowAgentSelect={false} />
                <Row>
                  <Col>
                    <Checkbox
                      label="Lock Event"
                      name="lock"
                      value={lock}
                      onChange={handleCheckbox}
                    />
                    <FormTextBox
                      required={lock}
                      name="notes.fieldNotes"
                      value={notes.fieldNotes}
                      title="Notes"
                      placeholder=""
                      onChange={handleFieldChange}
                    />
                  </Col>
                </Row>
                <BookSlotsButton
                  handleBookSlots={() => handleSaveClick()}
                  allowAgentSelect={false}
                />
              </>
            )}
          </Col>
        </Row>
      </EventSidebarBody>
      <EventSidebarFooter>
        {displayForm && (
          <SecondaryButton left onClick={onViewSlotsButtonClick}>
            View Available Slots
          </SecondaryButton>
        )}
      </EventSidebarFooter>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

FindHEASlotsForm.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default FindHEASlotsForm;
