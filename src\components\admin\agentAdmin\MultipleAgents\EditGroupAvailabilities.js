import React, { useState, useEffect, useCallback } from 'react';
import { useRecoilValue, useSetRecoilState, useRecoilState } from 'recoil';
import styled from 'styled-components';

import {
  Container,
  Row,
  Col,
  ButtonContainer,
  FormMultiselect,
  FormSelect,
  handleFormFieldChange,
} from '@components/global/Form';
import Header from '@components/global/Header';

import EditAvailabilityFields from '@components/admin/agentAdmin/EditAvailabilityFields';

import { regionsSelector, authorizedDepartmentsSelector, statesSelector } from '@recoil/app';
import {
  agentAvailabilityInfoState,
  allAuthorizedAgentsForUserSelector,
} from '@recoil/admin/agents';
import { adminInfoHasChangedState } from '@recoil/admin';
import { PrimaryButton } from '@components/global/Buttons';

const GroupAvailabilitiesContainer = styled.div`
  margin-left: 28px;
  margin-top: 10px;
`;

const AgentsConvenienceButtons = styled(ButtonContainer)`
  height: 50px;
`;

const EditGroupAvailabilities = () => {
  const allRegions = useRecoilValue(regionsSelector);
  const departmentOptions = useRecoilValue(authorizedDepartmentsSelector);
  const agents = useRecoilValue(allAuthorizedAgentsForUserSelector);
  const statesOptions = useRecoilValue(statesSelector);

  const [agentOptions, setAgentOptions] = useState([]);
  const [agentAvailabilityInfo, setAgentAvailabilityInfo] = useRecoilState(
    agentAvailabilityInfoState,
  );
  const setAgentInfoHasChanged = useSetRecoilState(adminInfoHasChangedState);

  const { state, department, oids, region } = agentAvailabilityInfo;

  const regionsByStateOptions = allRegions.filter(({ state: regionState }) => {
    return regionState === state;
  });
  regionsByStateOptions.unshift({ key: 'All', value: '' });

  const getAgentOptions = useCallback(() => {
    if (!department) return;

    const filteredCrews = agents.filter(({ state: agentState, department: agentDepartment }) => {
      return agentState === state && agentDepartment === department;
    });

    let agentOptions = filteredCrews.map(({ displayName, oid, region, number, isManager }) => {
      return { key: displayName, value: oid, region, number, isManager };
    });

    // Filter by region if region value is selected
    if (region && region > 0) {
      let filterRegions = [region];
      if (region === 200) filterRegions = [...filterRegions, 201, 202];
      agentOptions = agentOptions.filter(({ region: agentRegion }) => {
        return filterRegions.includes(agentRegion);
      });
    }

    // Currently only for Insulation
    if (department === 6)
      // Sort twice, once by region then by number
      agentOptions = agentOptions.sort((a, b) => {
        if (a.region === b.region) {
          return a.number - b.number;
        }
        return a.region - b.region;
      });
    setAgentOptions(agentOptions);
  }, [agents, state, department, region]);

  useEffect(() => {
    getAgentOptions();
  }, [getAgentOptions]);

  useEffect(() => {
    // refresh the agents recoil selector
    if (!department) return;

    const departmentInfo = departmentOptions.find(({ value }) => {
      return value === department;
    });
    if (!departmentInfo?.eventType) {
      const resetRegionField = { target: { name: 'region', value: '' } };
      handleFieldChange(resetRegionField);
      setAgentOptions([]);
    }
  }, [department, departmentOptions, handleFieldChange]);

  const handleFieldChange = useCallback(
    (e, updatedSchedule = agentAvailabilityInfo) => {
      handleFormFieldChange(e, updatedSchedule, setAgentAvailabilityInfo);
      setAgentInfoHasChanged(true);
    },
    [agentAvailabilityInfo, setAgentAvailabilityInfo, setAgentInfoHasChanged],
  );

  const handleAgentFilter = (filterType) => {
    let newFilterOids = [];
    if (filterType === 'all') newFilterOids = agentOptions.map(({ value }) => value);
    if (filterType === 'managers')
      newFilterOids = agentOptions.filter(({ isManager }) => isManager).map(({ value }) => value);
    setAgentAvailabilityInfo({ ...agentAvailabilityInfo, oids: newFilterOids });
  };

  return (
    <GroupAvailabilitiesContainer>
      <Header h2>Set Multiple Crews Schedule</Header>
      <Container>
        <Row>
          <Col size={1}>
            <FormSelect
              required
              title="State"
              placeholder="Select State"
              name="state"
              value={state}
              // Clear out oids if they have set them to prevent empty selections
              onChange={(e) => handleFieldChange(e, { ...agentAvailabilityInfo, oids: [] })}
              options={statesOptions}
            />
          </Col>
          <Col size={1}>
            <FormSelect
              required
              title="Department"
              type="number" // number type since this is returning dept id
              placeholder="Select Department"
              name="department"
              value={department}
              // Clear out oids if they have set them to prevent empty selections
              onChange={(e) => handleFieldChange(e, { ...agentAvailabilityInfo, oids: [] })}
              options={departmentOptions}
            />
          </Col>
          <Col size={1}>
            <FormSelect
              title="Region"
              type="number"
              placeholder="Select Regions"
              name="region"
              value={region}
              // Clear out oids if they have set them to prevent empty selections
              onChange={(e) => handleFieldChange(e, { ...agentAvailabilityInfo, oids: [] })}
              options={regionsByStateOptions}
            />
          </Col>
        </Row>
        <Row>
          <Col>
            <FormMultiselect
              required
              title="Add Trucks"
              name="oids"
              options={agentOptions}
              onChange={handleFieldChange}
              value={oids}
            />
            <AgentsConvenienceButtons marginDirections={['right', 'bottom']}>
              <PrimaryButton onClick={() => handleAgentFilter('all')}>All Trucks</PrimaryButton>
              <PrimaryButton onClick={() => handleAgentFilter('managers')}>
                All Managers
              </PrimaryButton>
            </AgentsConvenienceButtons>
            <EditAvailabilityFields departmentName={department === 6 ? 'Insulation' : null} />
          </Col>
        </Row>
      </Container>
    </GroupAvailabilitiesContainer>
  );
};

export default EditGroupAvailabilities;
