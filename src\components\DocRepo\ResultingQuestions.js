import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import Header from '@components/global/Header';
import { SalesforceManager, UsersManager } from '@utils/APIManager';
import { throwError } from '@utils/EventEmitter';

const StyledDivContainer = styled.div``;
const StyledFormContainer = styled.form``;
const StyledOptionContainer = styled.option``;
const StyledQuestionsHeader = styled.div`
  display: flex;
  justify-content: space-between;
`;
const StyledQuestionContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 300px;
`;
const StyledHeader = styled(Header)`
  font-size: 25px;
`;
const StyledTextArea = styled.textarea`
  font-weight: bold;
  width: 535px;
  border: none;
  border-radius: 15px;
  box-shadow: 2px 2px ${({ theme }) => theme.secondary[200]};
  /* Mobile phones (up to mobileL: 425px) */
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 12px;
    width: 150px;
  }
  /* Regular tablets (between mobileL and tablet: 768px) */
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
         (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    font-size: 18px;
    width: 400px;
  }
`;
const StyledSubmitDiv = styled.div`
  display: flex;
  justify-content: center;
  padding-top: 20px;
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    padding-top: 15px;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    font-size: 25px;
  }
`;
const StyledResultSubmitButton = styled.button`
  height: fit-content;
  border: 2px solid ${({ theme }) => theme.primary[400]};
  color: $primary-400;
  background-color: white;
  padding: 8px 20px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  padding: 10px 10px;
  margin: 10px 20px 10px 0;
  width: 25%;
  align-self: center;
  & :hover {
    cursor: pointer;
    background-color: $primary-400;
    color: white;
    border-radius: 25px;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 15px;
    width: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 58px;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    height: 50px;
    font-size: 24px;
    padding: 0px;
    height: 50px;
    width: 50%;
  }
`;
const StyledQuestionLabel = styled.div`
  font-weight: bold;
  text-decoration: underline;
  padding: 10px;
  text-align: center;
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 16px;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    font-size: 20px;
  }
`;
const StyledLabel = styled.label`
  font-weight: bold;
  text-decoration: underline;
  margin: 6px;
  width: 50%;
  height: 40px;
  font-size: 20px;
  text-align: center;
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 16px;
    width: 100%;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    font-size: 20px;
    width: 100%;
  }
`;
const StyledSelect = styled.select`
  font-weight: bold;
  margin: 6px;
  width: 50%;
  height: 40px;
  font-size: 20px;
  text-align: center;
  border-radius: 25px;
  border: none;
  background-color: ${({ theme }) => theme.primary[400]};
  color: white;
  text-align-last: center;
  width: 50%;
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 16px;
    width: 100%;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    font-size: 20px;
    width: 100%;
  }
`;
const StyledBackButton = styled.button`
  border: 2px solid ${({ theme }) => theme.primary[400]};
  color: $primary-400;
  background-color: white;
  padding: 8px 20px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  padding: 10px 10px;
  margin: 10px 20px 10px 0;
  width: 25%;
  align-self: center;
  font-size: 20px;
  & :hover {
    cursor: pointer;
    background-color: ${({ theme }) => theme.primary[400]};
    color: white;
    border-radius: 25px;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    font-size: 11px;
    width: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 58px;
    justify-content: center;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    font-size: 16px;
    padding: 0px;
    height: 50px;
    width: 50%;
    display: flex;
    justify-content: center;
  }
`;
const StyledQuestionAnswerDisplay = styled.div`
  font-size: 20px;
  &.flexrow {
    display: flex;
    flex-direction: row;
  }
  &.flexcol {
    display: flex;
    flex-direction: column;
  }
`;
const StyledQuestionDropdown = styled.div`
  display: flex;
  align-items: center;
  width: 50%;
  flex-direction: column;
  &.center {
    align-self: center;
  }
  @media (max-width: ${({ theme }) => theme.breakpoints.mobileL}px) {
    width: 100%;
  }
  @media (min-width: calc(${({ theme }) => theme.breakpoints.mobileL}px + 0.02px)) and 
  (max-width: ${({ theme }) => theme.breakpoints.tablet}px) {
    width: 100%;
  }
`;

const DocRepoQuestionPage = (props) => {
  // Imports
  const { uniqueId, resultingQuestionObject, sfObject, showBackButton, setRenderStatus } = props;
  const { updateSfObject } = SalesforceManager;
  const { getWalkthroughTech } = UsersManager;

  // State
  const [showSubmit, setShowSubmit] = useState(false);
  const [questionDisplay, setQuestionsDisplay] = useState([resultingQuestionObject.questions[0]]);

  // Functions
  const handlePickListChange = (event) => {
    const { value, title } = event.target;
    // title is the key value in the question object
    // the key is the index the question holds in the questions array.
    const objKey = Number(title);
    let stateQuestionArrCopy = questionDisplay.slice();
    const questionObjCopy = { ...resultingQuestionObject };
    const { questions } = questionObjCopy;
    const currentQuestion = stateQuestionArrCopy[objKey];
    const blocked = currentQuestion.blockerValues.includes(value);
    stateQuestionArrCopy[objKey].currentValue = value;
    // This checks to see if the question that was just answered is the last question that was added
    if (objKey + 1 === stateQuestionArrCopy.length) {
      // if it was the last question added and it's not blocked and another question exists, it will add that question
      // if not it will display the submit button
      if (!blocked && questions[objKey + 1]) {
        stateQuestionArrCopy = [...stateQuestionArrCopy, questions[objKey + 1]];
        setShowSubmit(false);
      } else {
        setShowSubmit(true);
      }
    } else if (!blocked) {
      // Resetting currentValue of question that will be added to display empty value in picklist.
      questions[objKey + 1].currentValue = '';
      stateQuestionArrCopy = [
        ...stateQuestionArrCopy.slice(0, objKey + 1),
        { ...questions[objKey + 1] },
      ];
      setShowSubmit(false);
    } else {
      // If blocked don't add any more questions and show submit button
      stateQuestionArrCopy = stateQuestionArrCopy.slice(0, objKey + 1);
      setShowSubmit(true);
    }
    setQuestionsDisplay(stateQuestionArrCopy);
  };

  // Salesforce notes field character limit for Scope change is 255 characters.
  const handleNotesChange = (event) => {
    const questionArrCopy = [...questionDisplay];
    const { title: key, value, id } = event.target;
    if (value.length > 600) {
      return throwError({
        message: 'Maximun characters allowed is 600.',
        params: 'Please shorten your message',
      });
    }
    questionArrCopy[key].notes[id] = value;
    return setQuestionsDisplay([...questionArrCopy]);
  };

  // Makes an object that takes the questions sf field name and the value to put
  const sfObjParamsUpdater = (questionsArr, sfParamsObj) => {
    const sfParamsCopy = { ...sfParamsObj };
    questionsArr.forEach((questionObj) => {
      const { sfTargetName, currentValue, notes } = questionObj;
      sfParamsCopy[sfTargetName] = currentValue;
      if (notes) sfParamsCopy[notes.sfField] = notes.notes;
    });
    return sfParamsCopy;
  };

  // Makes an object that had the sf field names for questions not answered and put a blank value
  const sfObjParamsLeftOverQuest = (leftOverQuestionArr, sfParamsObj) => {
    const sfParamsCopy = { ...sfParamsObj };
    leftOverQuestionArr.forEach((question) => {
      const { sfTargetName, currentValue } = question;
      sfParamsCopy[sfTargetName] = currentValue;
    });
    return sfParamsCopy;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    const { questions } = resultingQuestionObject;
    const sfObjToUpdate = sfObject;
    let sfUpdateParamsObj = {
      Id: uniqueId,
    };

    // Grabs sfTargetName and value to add to the sf update obj
    sfUpdateParamsObj = sfObjParamsUpdater(questionDisplay, sfUpdateParamsObj);
    // Adds key to update Walkthrough Tech on SF
    if (sfObjToUpdate !== 'Opportunity') {
      const walkthroughTech = await getWalkthroughTech(uniqueId);
      sfUpdateParamsObj.Walkthrough_Tech__c = walkthroughTech;
    }

    // If all questions are not answered, find the rest of the questions, add the sfTargetName to the object with a value of ''
    // If they want notes reset when they are changing resulting, you can add notes in below.
    if (questionDisplay.length !== questions.length) {
      const remainingQuestionsAndValues = questions.slice(questionDisplay.length);
      sfUpdateParamsObj = sfObjParamsLeftOverQuest(remainingQuestionsAndValues, sfUpdateParamsObj);
    }
    const response = await updateSfObject(sfObjToUpdate, sfUpdateParamsObj);
    if (response.success) {
      window.location.reload();
    }
  };

  return (
    <StyledFormContainer onSubmit={handleSubmit}>
      <StyledDivContainer>
        <StyledQuestionsHeader>
          <StyledHeader h3>Resulting Questions</StyledHeader>
          {!showBackButton && (
            <StyledBackButton onClick={() => setRenderStatus('beenHereBefore')} type="button">
              Back To Documents
            </StyledBackButton>
          )}
        </StyledQuestionsHeader>
        <StyledQuestionContainer>
          {questionDisplay.map((questionObj) => {
            const { question, sfTargetName, values, key, currentValue, notes } = questionObj;
            return (
              <StyledQuestionAnswerDisplay key={question} className={notes ? 'flexrow' : 'flexcol'}>
                <StyledQuestionDropdown className={notes && 'align-center'}>
                  <StyledLabel htmlFor="question-selects"> {question} </StyledLabel>
                  <StyledSelect
                    name={sfTargetName}
                    onChange={handlePickListChange}
                    title={key}
                    value={currentValue}
                    id="question-selects"
                  >
                    {values.map((val) => {
                      return (
                        <StyledOptionContainer value={val} key={val}>
                          {val}
                        </StyledOptionContainer>
                      );
                    })}
                  </StyledSelect>
                </StyledQuestionDropdown>
                {notes ? (
                  <StyledDivContainer>
                    <StyledQuestionLabel htmlFor="notes">Change Order Notes</StyledQuestionLabel>
                    <StyledTextArea
                      rows="10"
                      cols="40"
                      type="text"
                      value={notes.notes}
                      id="notes"
                      name="notes"
                      title={key}
                      onChange={handleNotesChange}
                    />
                  </StyledDivContainer>
                ) : null}
              </StyledQuestionAnswerDisplay>
            );
          })}

          {showSubmit && (
            <StyledSubmitDiv>
              <StyledResultSubmitButton type="submit" value="Submit">
                Submit{' '}
              </StyledResultSubmitButton>
            </StyledSubmitDiv>
          )}
        </StyledQuestionContainer>
      </StyledDivContainer>
    </StyledFormContainer>
  );
};

DocRepoQuestionPage.propTypes = {
  uniqueId: PropTypes.string.isRequired,
  resultingQuestionObject: PropTypes.shape({
    questions: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  }).isRequired,
  sfObject: PropTypes.string.isRequired,
  showBackButton: PropTypes.bool.isRequired,
  setRenderStatus: PropTypes.func.isRequired,
};

export default DocRepoQuestionPage;
