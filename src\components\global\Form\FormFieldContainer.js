import React from 'react';
import { useRecoilValue } from 'recoil';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { selectedEventState } from '@recoil/eventSidebar';
import { formValuesState } from '@recoil/dataIntakeForm';

const StyledFormFieldContainer = styled.div`
  position: relative;
  min-height: 32px;
  margin-bottom: ${({ compact }) => (compact ? '0' : '1.5em')};
  display: flex;
  flex-direction: ${({ horizontal }) => (horizontal ? 'row' : 'column')};
  align-items: ${({ horizontal }) => (horizontal ? 'center' : '')};
  justify-content: ${({ horizontal }) => (horizontal ? '' : 'center')};

  label {
    ${({ horizontal }) => (horizontal ? 'margin-bottom: 0px' : '')}
  }

  label:first-child:after {
    ${({ required, theme }) => {
      return required
        ? `color: ${theme.colors.red};
           content: ' *';
           display: inline;`
        : '';
    }}
  }

  margin: ${({ border }) => {
    return border ? '16px 16px 0px 16px' : '';
  }};

  padding-right: ${({ customPaddingRight }) => {
    return customPaddingRight ? '26px' : '';
  }};

  width: ${({ customWidth }) => {
    return customWidth ? '50%' : '';
  }};
`;

const FormFieldError = styled.div`
  color: ${({ theme }) => theme.colors.red};
  font-style: italic;
  font-size: 12px;
`;

const FormFieldContainer = ({
  fieldName = '',
  required = false,
  children,
  testid = '',
  customPaddingRight = false,
  customWidth = false,
  compact = false,
  horizontal = false,
}) => {
  const { formFieldErrors } = useRecoilValue(selectedEventState);
  const { formFieldErrors: leadIntakeFormFieldErrors } = useRecoilValue(formValuesState);
  const errorMessage = formFieldErrors?.[fieldName] || leadIntakeFormFieldErrors?.[fieldName];

  return (
    <StyledFormFieldContainer
      data-testid={testid}
      required={required}
      compact={compact}
      horizontal={horizontal}
      customPaddingRight={customPaddingRight}
      customWidth={customWidth}
    >
      {children}
      {errorMessage && <FormFieldError>{errorMessage}</FormFieldError>}
    </StyledFormFieldContainer>
  );
};

FormFieldContainer.propTypes = {
  fieldName: PropTypes.string,
  children: PropTypes.node.isRequired,
  required: PropTypes.bool,
  testid: PropTypes.string,
  customPaddingRight: PropTypes.bool,
  customWidth: PropTypes.bool,
  compact: PropTypes.bool,
  horizontal: PropTypes.bool,
};

export default FormFieldContainer;
