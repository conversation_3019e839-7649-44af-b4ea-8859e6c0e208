import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { EventHistory } from './EventHistory';

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContainer = styled.div`
  position: relative;
  background: white;
  width: 500px;
  height: 400px;
  max-width: 90%;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.25);
  z-index: 1001;
  overflow-y: auto;
  overflow-x: hidden;
  @media (max-width: 550px) {
    width: 300px;
  }
`;

const Header = styled.div`
  height: 66px;
  padding: 20px;
  background: #f4f4f6;
`;

const CloseButton = styled.button`
  position: absolute;
  left: 35em;
  top: 1em;
  cursor: pointer;
  border: none;
  opacity: 1;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  color: ${({ theme }) => theme.secondary[400]};
  background: ${({ theme }) => theme.secondary[100]};
  box-shadow: 0px 7px 12px;
`;

const Content = styled.div`
  font-size: 16px;
  margin-top: 10px;
  padding: 20px;
`;

const HeaderTitle = styled.div`
  font-style: normal;
  font-weight: 500;
  font-size: 22px;
  line-height: 26px;
  color: ${({ theme }) => theme.secondary[600]};
`;

export const EventHistoryModal = ({ isOpen, id, handleClose }) => {
  return (
    <>
      {isOpen && (
        <Overlay>
          <ModalContainer>
            <CloseButton onClick={handleClose}>x</CloseButton>
            <Header>
              <HeaderTitle>History Details</HeaderTitle>
            </Header>
            <Content>
              <EventHistory eventId={id} />
            </Content>
          </ModalContainer>
        </Overlay>
      )}
    </>
  );
};

EventHistoryModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  id: PropTypes.string.isRequired,
  handleClose: PropTypes.func.isRequired,
};
