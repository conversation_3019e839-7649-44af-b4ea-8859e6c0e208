import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';

const create = (params) => {
  const {
    oids,
    phoneNumber,
    type,
    date,
    jobLength,
    notes: { fieldNotes },
    startEndTimes,
    address,
    numUnit,
    unitsSelected,
    customerInterests = [],
    customerName,
    email,
    fuelType,
    sfIds: { accountId },
    timeFrameForProject,
    monthlyPaymentExpectations,
    primaryHeatingFuel,
    centralAc,
    isHvacLeadIntake,
    leadVetted,
  } = params;

  let { leadSource, sfIds } = params;

  let requiredFields = {
    'Crew(s)': oids,
    'Job Type': type,
    Date: date,
    Address: address,
  };

  const isHESScheduled = unitsSelected.length > 0;

  requiredFields = isHESScheduled
    ? {
        ...requiredFields,
        'Units Selected': unitsSelected,
        'Customer Interests': customerInterests,
      }
    : { ...requiredFields, 'Lead Source': leadSource };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const formattedSfIds = {};
  const primaryUnit = accountId;

  if (isHESScheduled) {
    leadSource = 'HEA Visit';
    // Sorting units by Account ID
    const sortedUnitsSelected = [...unitsSelected].sort((a, b) => {
      return a.accountId.localeCompare(b.accountId, undefined, {
        numeric: true,
        sensitivity: 'base',
      });
    });

    // Assigning selected units a new unit number.
    [...sortedUnitsSelected].forEach(({ unitNum }, index) => {
      const prevUnitNumString = unitNum === 0 ? '' : `${unitNum + 1}`;
      const newUnitNumString = index === 0 ? '' : `${index + 1}`;
      formattedSfIds[`accountId${newUnitNumString}`] = sfIds[`accountId${prevUnitNumString}`];
      formattedSfIds[`dealId${newUnitNumString}`] = sfIds[`dealId${prevUnitNumString}`];
      formattedSfIds[`operationsId${newUnitNumString}`] = sfIds[`operationsId${prevUnitNumString}`];
      formattedSfIds[`siteId${newUnitNumString}`] = sfIds[`siteId${prevUnitNumString}`];
    });

    sfIds = formattedSfIds;
  }

  const createData = {
    date,
    oids,
    type,
    jobLength,
    sfIds,
    notes: { fieldNotes },
    phoneNumber,
    startEndTimes,
    address,
    shadow: false,
    numUnit: unitsSelected?.length || numUnit,
    customerInterests,
    customerName,
    email,
    fuelType,
    accountId: sfIds.accountId,
    leadSource,
    primaryUnit,
    isHESSchedulingHVAC: isHESScheduled,
    timeFrameForProject,
    monthlyPaymentExpectations,
    primaryHeatingFuel,
    centralAc,
    isHvacLeadIntake,
    leadVetted,
  };

  // Create slots using the start and end times
  return createData;
};

const getSlots = (params) => {
  const { type, address } = params;

  const requiredFields = {
    'Job Type': type,
    Address: address,
  };
  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  return [params];
};

const update = (params) => {
  const {
    id,
    associatedEventsId,
    oids,
    phoneNumber,
    type,
    date,
    jobLength,
    notes,
    startEndTimes,
    address,
    numUnit,
    customerInterests = [],
    customerName,
    email,
    fuelType,
    sfIds,
    accountId,
    associatedEventIds,
    removedUnits,
    leadVetted,
  } = params;

  const requiredFields = {
    'Crew(s)': oids,
    'Job Type': type,
    Date: date,
    Address: address,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const updateData = {
    id,
    associatedEventsId,
    date,
    oids,
    type,
    jobLength,
    sfIds,
    notes,
    phoneNumber,
    startEndTimes,
    address,
    shadow: false,
    numUnit,
    customerInterests,
    customerName,
    email,
    fuelType,
    accountId,
    associatedEventIds,
    removedUnits,
    leadVetted,
  };

  return updateData;
};

const cancel = (params) => {
  return params;
};
const reassign = (params) => {
  return params;
};
const reschedule = (params) => {
  const { rescheduleReason, date, startTime, oids } = params;

  const requiredFields = {
    'Reschedule Reason': rescheduleReason,
    Date: date,
    'Start Time': startTime,
    Agent: oids[0],
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  return requiredFields;
};

const rescheduleLater = (params) => {
  const { rescheduleReason } = params;

  const requiredFields = {
    'Reschedule Reason': rescheduleReason,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return requiredFields;
};

const hvacSales = { create, update, getSlots, cancel, reassign, reschedule, rescheduleLater };
export default hvacSales;
