import React from 'react';
import { useSetRecoilState } from 'recoil';
import { PrimaryButton } from '@components/global/Buttons';
import { showSidebarState, isSlotsSearchAtom } from '@recoil/eventSidebar';
import { isEventsSearchState } from '@recoil/eventsSearch';

const BookAppointmentButton = () => {
  const setShowSidebar = useSetRecoilState(showSidebarState);
  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);
  const setIsEventSearch = useSetRecoilState(isEventsSearchState);
  const showSlotsSidebar = () => {
    setIsEventSearch(false);
    setIsSlotsSearch(true);
    setShowSidebar(true);
  };

  return <PrimaryButton onClick={showSlotsSidebar}>Book Appointment</PrimaryButton>;
};

export default BookAppointmentButton;
