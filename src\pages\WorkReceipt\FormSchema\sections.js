import { capWRFormSchema } from '../CAPWR/FormSchema/formSchema';
import { formSchema } from './formSchema';
// const leadIntakeFields = addKeyNameToObject(formSchema);
// Section fields are imported in leadIntakeMap object
// Section field means its a group of fields and there output will be in array string format
// Suppose if we expose unitInfo to leadIntake map.
// each field of unitInfo will have list values
// unitNumber = ['911', '132' , '553']
// means 1st unit number is 911 and 2nd is 132, 3rd is 553
// so same for remaining fields, Everything is index based in Section Fields

// MARKET RATE SECTION FIELDS
const getFields = (schema) => Object.values(schema);
const aesbestosInfo = getFields(formSchema.asbestosInfo);
const cstFailure = getFields(formSchema.cstFailure);
const electrical = getFields(formSchema.electrical);
const moisture = getFields(formSchema.moisture);
const crawlSpace = getFields(formSchema.crawlSpace);
const mold = getFields(formSchema.mold);
const other = getFields(formSchema.other);
const structural = getFields(formSchema.structural);
const wallsWorkDisclosures = getFields(formSchema.wallsWorkDisclosures);
const weatherization = getFields(formSchema.weatherization);
const weatherizationHvac = getFields(formSchema.weatherizationHvac);
// CAP HEA SECTION FIELDS *****
const bathFanInstallReplace = getFields(capWRFormSchema.bathFanInstallReplace);
const pestInfestationRoofLeak = getFields(capWRFormSchema.pestInfestationRoofLeak);
const ampFail = getFields(capWRFormSchema.ampFail);
const icw = getFields(capWRFormSchema.icw);
const heaSpecReadOnly = getFields(capWRFormSchema.heaSpecReadOnly);

const sectionFields = {
  asbestosInfo: {
    text: 'Asbestos',
    type: 'section',
    fields: aesbestosInfo,
    perUnit: false,
    collapse: true,
  },
  cstFailure: {
    text: 'CST Failure',
    type: 'section',
    fields: cstFailure,
    perUnit: false,
    collapse: true,
  },
  electrical: {
    text: 'Electrical',
    type: 'section',
    fields: electrical,
    perUnit: false,
    collapse: true,
  },
  moisture: {
    text: 'Moisture',
    type: 'section',
    fields: moisture,
    perUnit: false,
    collapse: true,
  },
  crawlSpace: {
    text: 'Crawlspace',
    type: 'section',
    fields: crawlSpace,
    perUnit: false,
    collapse: true,
  },
  mold: {
    text: 'Mold',
    type: 'section',
    fields: mold,
    perUnit: false,
    collapse: true,
  },
  other: {
    text: 'Other',
    type: 'section',
    fields: other,
    perUnit: false,
    collapse: true,
  },
  structural: {
    text: 'Structural',
    type: 'section',
    fields: structural,
    perUnit: false,
    collapse: true,
  },
  wallsWorkDisclosures: {
    text: 'Walls/Work Disclosures',
    type: 'section',
    fields: wallsWorkDisclosures,
    perUnit: false,
    collapse: true,
  },
  weatherization: {
    text: 'Weatherization',
    type: 'section',
    fields: weatherization,
    perUnit: false,
    collapse: true,
  },
  hvac: {
    text: 'HVAC',
    type: 'section',
    fields: weatherizationHvac,
    perUnit: false,
    collapse: true,
  },
  pestInfestationRoofLeak: {
    text: 'Pest Infestation And Roof Leak',
    type: 'section',
    fields: pestInfestationRoofLeak,
    perUnit: false,
    collapse: true,
  },
  bathFanInstallReplace: {
    text: 'Bath Fan Install Replace',
    type: 'section',
    fields: bathFanInstallReplace,
    perUnit: false,
    collapse: true,
  },
  ampFail: {
    text: 'AMP Fail',
    type: 'section',
    fields: ampFail,
    perUnit: false,
    collapse: true,
  },
  icw: {
    text: 'ICW',
    type: 'section',
    fields: icw,
    perUnit: false,
    collapse: true,
  },
  heaSpecReadOnly: {
    text: 'HEA Spec Quality Section',
    type: 'section',
    fields: heaSpecReadOnly,
    perUnit: false,
    collapse: true,
  },
};

export { sectionFields };
