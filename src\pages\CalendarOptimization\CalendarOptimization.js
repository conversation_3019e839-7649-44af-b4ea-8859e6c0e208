import React, { useState } from 'react';
import styled from 'styled-components';
import moment from 'moment';
import Swal from 'sweetalert2/dist/sweetalert2';
import { PrimaryButton } from '@components/global/Buttons';
import { PageHeader } from '@pages/Components';
import { HorizontalLine, Header as Title } from '@components/global';
import { Row, Col, FormSelect, FormDatePicker } from '@components/global/Form';
import { UtilityManager } from '@utils/APIManager';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  overflow: scroll;
  padding: 20px;
  background-color: ${({ theme }) => theme.secondary[100]};
`;

const CalendarControlHeader = styled.div`
  justify-content: end;
  display: flex;
`;

const ButtonContainer = styled(Row)`
  display: flex;
  flex-direction: column;
  padding: 10px;
`;

const StyledPrimaryButton = styled(PrimaryButton)`
  width: 100%;
  margin-top: 20px;
`;

const ActionContainer = styled.div`
  margin-top: 30px;
`;

const OptimizedCalendarHeaderContainer = styled.div`
  margin-top: 50px;
  justify-items: anchor-center;
  margin-bottom: 20px;
`;

const OptimizedMetrixStyled = styled.div`
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
`;

const Tablestyled = styled.table`
  font-family: Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 100%;
`;

const TableRow = styled.tr``;

const TableCell = styled.td`
  border: 1px solid #ddd;
  padding: 8px;
`;

const TableHeader = styled.th`
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: center;
  background-color: #04aa6d;
  color: white;
  border: 1px solid #ddd;
  padding: 8px;
`;

const CalendarOptimization = () => {
  const [department, setDepartment] = useState(null);
  const [optimizationDate, setOptimizationDate] = useState(
    moment(new Date())
      .add(1, 'Days')
      .startOf('day'),
  );

  const [selectedOptimizedRoute, setSelectedOptimizedRoute] = useState(null);

  const userDetailsKeyTitleMap = {
    homeAddress: 'Home Address',
    originalAppointments: 'Original Appointments',
    optimizedAppointments: 'Optimized Appointments',
    totalMileageBeforeAppointment: 'Total mileage BEFORE optimization',
    totalMileageAfterAppointment: 'Total mileage AFTER optimization',
  };
  const optimizedDetailsKeyTitleMap = {
    totalMileageBefore: 'Total Mileage Before:',
    totalMileageAfter: 'Total Mileage After:',
    mileageDifference: 'Mileage Difference:',
    mileagePercentageImprovement: 'Mileage Percentage improvement:',
    totalEventsBefore: 'Total Events Before:',
    totalEventsAfter: 'Total Events After:',
  };

  const handleDateChange = (newDate) => {
    setOptimizationDate(moment(newDate).startOf('day'));
  };

  const runOptimization = async () => {
    const date = moment(optimizationDate).format('YYYY-MM-DD');
    await UtilityManager.runOptimization(department, date);
  };

  const gettingOptimizedCalendar = async () => {
    setSelectedOptimizedRoute(null);
    const data = await UtilityManager.getOptimizedCalendar(department);
    setSelectedOptimizedRoute(data);
  };

  const swapEvents = async () => {
    const date = moment(optimizationDate).format('YYYY-MM-DD');
    const confirm = await Swal.fire({
      icon: 'warning',
      title: 'Swapping Events',
      text: `Are you sure you want to Swap events for ${department} ${date}?`,
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirm?.value) return;
    await UtilityManager.swapAllEvents(selectedOptimizedRoute.swapEvents, department);
  };

  const onClickReset = () => {
    setDepartment(null);
    setSelectedOptimizedRoute(null);
  };

  const renderUsersRoute = (userRoutes) => {
    const userRows = Object.keys(userRoutes).map((key) => {
      return (
        <tr key={key}>
          <TableCell>
            <Title h4>{userDetailsKeyTitleMap[key]}</Title>
          </TableCell>
          {['originalAppointments', 'optimizedAppointments'].includes(key) ? (
            <TableCell>
              <Tablestyled>
                {userRoutes[key].map((value) => {
                  return (
                    <TableRow key={value}>
                      <TableCell>
                        <Title h4>{value}</Title>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </Tablestyled>
            </TableCell>
          ) : (
            <TableCell>
              <Title h4>{userRoutes[key]}</Title>
            </TableCell>
          )}
        </tr>
      );
    });
    return userRows;
  };

  const renderOptimizedDetails = (details) => {
    const copyDetails = { ...details };
    delete copyDetails.displayEventDetails;
    return Object.keys(copyDetails).map((key) => {
      return optimizedDetailsKeyTitleMap[key] ? (
        <Title h3 key={key}>
          {optimizedDetailsKeyTitleMap[key]} {copyDetails[key]}
        </Title>
      ) : null;
    });
  };

  return (
    <>
      <PageHeader>Calendar Optimization</PageHeader>
      <Container>
        <CalendarControlHeader>
          <PrimaryButton onClick={() => onClickReset()}>Reset</PrimaryButton>
        </CalendarControlHeader>
        <Row>
          <Col size={1}>
            <FormDatePicker
              title="Date:"
              name="optimizationDate"
              value={optimizationDate ? moment(optimizationDate) : null}
              onChange={handleDateChange}
            />
            <FormSelect
              required
              title="Department"
              placeholder="Select Department"
              name="type"
              value={department}
              onChange={(e) => setDepartment(e.target.value)}
              options={[{ key: 'HVAC Sales', value: 'HVAC-Sales' }]}
            />
          </Col>
        </Row>
        <Row>
          <ButtonContainer>
            <StyledPrimaryButton disabled={!department} onClick={() => runOptimization()}>
              Generate Optimize {department} Calendar
            </StyledPrimaryButton>
            <StyledPrimaryButton disabled={!department} onClick={() => gettingOptimizedCalendar()}>
              Get {department} Optimized Calendar
            </StyledPrimaryButton>
          </ButtonContainer>
        </Row>

        {selectedOptimizedRoute && (
          <>
            <OptimizedCalendarHeaderContainer>
              <HorizontalLine />
              <Title h2>
                {department} Calendar Route Optimization Sugession for{' '}
                {moment(optimizationDate).format('YYYY-MM-DD')}
              </Title>
              <HorizontalLine />
            </OptimizedCalendarHeaderContainer>

            <OptimizedMetrixStyled>
              {renderOptimizedDetails(selectedOptimizedRoute.displayDetails)}
            </OptimizedMetrixStyled>
            <HorizontalLine />
            <Tablestyled>
              {selectedOptimizedRoute.displayDetails.displayEventDetails.map(
                ({
                  homeAddress,
                  userName,
                  originalAppointments,
                  optimizedAppointments,
                  totalMileageBeforeAppointment,
                  totalMileageAfterAppointment,
                }) => {
                  return (
                    <>
                      <TableRow>
                        <TableHeader colSpan="2">
                          <Title h3>{userName}</Title>
                        </TableHeader>
                      </TableRow>
                      {renderUsersRoute({
                        homeAddress,
                        originalAppointments,
                        optimizedAppointments,
                        totalMileageBeforeAppointment,
                        totalMileageAfterAppointment,
                      })}
                    </>
                  );
                },
              )}
            </Tablestyled>
            <ActionContainer>
              <StyledPrimaryButton onClick={() => swapEvents()}>
                Swap Selected Optimized Events
              </StyledPrimaryButton>
            </ActionContainer>
            <HorizontalLine />
          </>
        )}
      </Container>
    </>
  );
};

export default CalendarOptimization;
