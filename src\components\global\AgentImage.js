import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const defaultImage = require('@assets/calendar-row-image-default.svg');

const StyledAgentImage = styled.img`
  width: 30px;
  height: 30px;
`;

const AgentImage = ({ imageUrl = '' }) => {
  return <StyledAgentImage src={imageUrl || defaultImage} alt="profile image" />;
};

AgentImage.propTypes = {
  imageUrl: PropTypes.string,
};

export default AgentImage;
