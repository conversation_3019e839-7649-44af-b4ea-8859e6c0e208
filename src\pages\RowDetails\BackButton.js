import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { withRouter } from 'react-router-dom';

import { ChevronLeft } from '@styled-icons/boxicons-regular';

import { Clickable } from '@components/global';

const StyledBackButton = styled(Clickable)`
  font-size: 18px;
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.primary[500]};
`;

const BackIcon = styled(ChevronLeft)`
  height: 16px;
  margin: 0 8px;
`;

const BackButton = (props) => {
  const { children: buttonText, history } = props;

  const handleBackButton = () => {
    history.goBack();
  };

  return (
    <StyledBackButton onClick={handleBackButton}>
      <BackIcon />
      {buttonText}
    </StyledBackButton>
  );
};

BackButton.propTypes = {
  history: PropTypes.shape({
    goBack: PropTypes.func,
  }).isRequired,
  children: PropTypes.string.isRequired,
};

export default withRouter(BackButton);
