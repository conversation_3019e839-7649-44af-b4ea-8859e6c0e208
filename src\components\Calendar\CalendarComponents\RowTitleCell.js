import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilValue } from 'recoil';
import { withRouter } from 'react-router-dom';
import { AgentImage } from '@components/global';
import ToolTip from '@components/global/Tooltip/Tooltip';
import { calendarTypeAtom } from '@recoil/app';

const Content = styled.div`
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  padding: 10px;
`;

const TextContainer = styled.div`
  margin-left: 10px;
`;

const TitleText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.h4};
  color: ${({ theme }) => theme.primary[400]};
`;

const DetailText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.h5};
  color: ${({ theme }) => theme.secondary[500]};
`;

const StyledRowTitleCell = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 150%;
  padding: 5px;
  flex-grow: 2;
  word-break: break-word;
  background-color: ${({ theme }) => theme.secondary[100]};

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.tablet)} {
    width: 100%;
  }

  ${Content}:hover {
    cursor: pointer;
    ${TitleText} {
      text-decoration: underline;
    }
  }
`;

const RowTitleCell = ({
  oid = '',
  rowTitle = '',
  imageUrl = '',
  details = '',
  children = null,
  history,
}) => {
  const calendarType = useRecoilValue(calendarTypeAtom);
  const [showTooltip, setShowTooltip] = useState(false);

  const handleRowDetails = () => {
    history.push(`/row-details/${oid}`);
  };

  return (
    <StyledRowTitleCell>
      <Content onClick={handleRowDetails}>
        <AgentImage imageUrl={imageUrl} />
        <TextContainer>
          <TitleText
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
          >
            {rowTitle}
          </TitleText>
          {calendarType === '000400' ? (
            showTooltip && <ToolTip text={details} />
          ) : (
            <DetailText>{details}</DetailText>
          )}
        </TextContainer>
      </Content>
      {children || null}
    </StyledRowTitleCell>
  );
};

RowTitleCell.propTypes = {
  oid: PropTypes.string,
  rowTitle: PropTypes.string,
  imageUrl: PropTypes.string,
  details: PropTypes.string,
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
    PropTypes.bool,
  ]),
  history: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
};

export default withRouter(RowTitleCell);
