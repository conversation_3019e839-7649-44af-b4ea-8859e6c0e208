import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { ChevronLeft, ChevronRight } from '@styled-icons/boxicons-regular';

import { Clickable } from '@components/global';

const StyledScrollCalendarArrows = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  color: ${({ theme }) => theme.primary[500]};
  width: max-content;
`;

const ScrollButton = styled(Clickable)`
  display: flex;
  align-items: center;
  border-radius: 4px;
  padding: 4px;
  background-color: ${({ theme }) => theme.secondary[200]};
  margin: 0px 2px;
  white-space: nowrap;

  svg {
    display: block;
  }
`;

const ARROW_SIZE = 16;

const ScrollCalendarArrows = (props) => {
  const { scrollCalendar, isMonthly } = props;

  return (
    <StyledScrollCalendarArrows>
      <ScrollButton onClick={() => scrollCalendar('-')}>
        <ChevronLeft size={ARROW_SIZE} />
      </ScrollButton>
      <ScrollButton onClick={() => scrollCalendar()}>
        {isMonthly ? 'This Month' : 'This Week'}
      </ScrollButton>
      <ScrollButton onClick={() => scrollCalendar('+')}>
        <ChevronRight size={ARROW_SIZE} />
      </ScrollButton>
    </StyledScrollCalendarArrows>
  );
};

ScrollCalendarArrows.propTypes = {
  scrollCalendar: PropTypes.func.isRequired,
  isMonthly: PropTypes.bool.isRequired,
};

export default ScrollCalendarArrows;
