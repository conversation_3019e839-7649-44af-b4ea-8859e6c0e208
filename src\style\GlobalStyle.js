// src/style/GlobalStyle.js
import { createGlobalStyle } from 'styled-components';

const GlobalStyle = createGlobalStyle`
  html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 13px;
    letter-spacing: -0.24px;
  }

  body.swal2-height-auto {
    height: 100% !important;
  }

  h1, h2, h3, h4, h5 {
    color: ${({ theme }) => theme.primary[400]};
    font-weight: bold;
    margin: 0px 0px 10px 0px;
  }

  #main {
    position: relative;
    width: 100%;
    height: 100%;

    .page-wrapper {
      display: flex;
      justify-content: flex-start;
      height: 100%;

      .content-with-menu-bar-wrapper {
        width: 100%;
        height: 100%;
        overflow: auto;
        background: ${({ theme }) => theme.primary[100]} url('../assets/house-background.svg');
        background-repeat: no-repeat;
        background-position: bottom right;
        background-size: 45%;

        .content-wrapper {
          width: 100%;
          padding: 18px;
          z-index: 100000;
          min-height: calc(100% - 40px);
          height: calc(100% - 40px);

          .form-control {
            border-radius: ${({ theme }) => theme.fontSizes.h4};
          }
        }
      }
    }
  }

  .pac-container {
    z-index: 999999;
  }

  .button {
    padding: 5px 10px;
    width: max-content;
    border-radius: 50px;
    color: white;
    background-color: ${({ theme }) => theme.primary[300]};
    text-align: center;
    user-select: none;

    &.secondary {
      background-color: ${({ theme }) => theme.secondary[600]};
    }
  }

  .modal {
    .modal-dialog {
      .modal-header {
        .modal-title {
          width: 100%;
          text-align: left;
          font-size: 20px;
          font-weight: 500;
          margin: 4px 0px -11px 20px;
          color: white;
        }
        .close {
          color: ${({ theme }) => theme.secondary[100]};
          margin-left: 0px;
          font-weight: 400;
          font-size: 40px;
          margin-right: 5px;
          text-shadow: none;
        }
        color: ${({ theme }) => theme.secondary[100]};
        background-color: ${({ theme }) => theme.secondary[600]};
      }

      .modal-body {
        min-height: 350px;
        background-color: ${({ theme }) => theme.secondary[100]};
      }

      .modal-content {
        border-radius: ${({ theme }) => theme.fontSizes.h4};
        overflow: hidden;
      }
    }
  }

  .form-control {
    border-radius: ${({ theme }) => theme.fontSizes.h4};
  }

  .page-title-filter-button {
    display: flex;
    justify-content: space-between;

    .page-title {
      font-size: 25px;
      font-weight: 500;
    }
  }

  .button-display {
    margin-top: 50px;
    font-size: 16px;
    border-radius: 35px;
  }

  .save-button {
    background-color: ${({ theme }) => theme.primary[300]};
    color: white;
    padding: 5px 40px;
    border-color: ${({ theme }) => theme.primary[300]};
  }

  .cancel-button {
    background-color: white;
    color: black;
    border-color: black;
    padding: 5px 40px;
  }

  .tab-button {
    font-size: 16px;
    font-weight: 400;
    color: white;
    border: none;
    background-color: #3e3e3e;
    margin: 0px 10px 1px 0px;
  }

  .active-tab, .tab-button:hover {
    text-decoration: none;
    border-radius: 0px;
    border-bottom-width: 4px;
    border-bottom-style: solid;
    border-bottom-color: white;
  }

  .button-rows {
    margin: 30px 0px 0px 10px;
  }

  .react-datepicker-popper {
    z-index: 901;
  }

  @media ${({ theme }) => theme.screenSize.down(theme.breakpoints.mobileL)} {
    .save-button {
      width: fit-content;
    }
  }

  @media ${({ theme }) =>
    theme.screenSize.between(theme.breakpoints.mobileM, theme.breakpoints.tablet)} {
    .edit-button {
      width: 180px;
    }
  }
`;

export default GlobalStyle;
