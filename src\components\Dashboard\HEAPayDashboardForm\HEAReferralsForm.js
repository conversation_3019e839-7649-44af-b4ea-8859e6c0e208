import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HEAReferralsForm = ({ record = {} }) => {
  const { dealId, siteId, referralCommission, startTime, heaVisitResult } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput readOnly name="siteId" value={siteId} title="Site Id" placeholder="" />
          <FormInput
            readOnly
            name="startTime"
            value={startTime}
            title="Start Time"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="referralCommission"
            value={referralCommission}
            title="Referral Comission"
            placeholder=""
          />
          <FormInput
            readOnly
            name="heaVisitResult"
            value={heaVisitResult}
            title="Timestamp Incorrectly Closed Won by IAM"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

HEAReferralsForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    siteId: PropTypes.string,
    referralCommission: PropTypes.string,
    startTime: PropTypes.string,
    heaVisitResult: PropTypes.string,
  }),
};

export default HEAReferralsForm;
