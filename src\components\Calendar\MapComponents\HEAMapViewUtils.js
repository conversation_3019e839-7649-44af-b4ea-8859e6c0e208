import moment from 'moment';
import { COLORBLIND_SAFE_COLORS } from '@style/variables';

// Generate colors dynamically
export const generateColorblindSafeColor = (index) => {
  // First use the curated palette
  if (index < COLORBLIND_SAFE_COLORS.length) {
    return COLORBLIND_SAFE_COLORS[index];
  }

  // Use specific hue ranges that work well for colorblind users
  const colorblindSafeHues = [30, 60, 180, 210, 270, 330];
  const hueIndex = (index - COLORBLIND_SAFE_COLORS.length) % colorblindSafeHues.length;
  const baseHue = colorblindSafeHues[hueIndex];

  const hueVariation =
    Math.floor((index - COLORBLIND_SAFE_COLORS.length) / colorblindSafeHues.length) * 15;
  const hue = (baseHue + hueVariation) % 360;

  const saturation = 75 + (index % 3) * 5;
  const lightness = 45 + (index % 2) * 5;
  return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
};

const colorCache = new Map();
export const getHESColor = (index) => {
  if (!colorCache.has(index)) {
    colorCache.set(index, generateColorblindSafeColor(index));
  }
  return colorCache.get(index);
};

// Export the curated palette (for backward compatibility)
export const HES_COLORS = COLORBLIND_SAFE_COLORS;

// Helper function to create a marker icon using FontAwesome
export const createFontAwesomeMarker = (iconPath, color) => {
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="32" height="32">
      <circle cx="12" cy="12" r="11" fill="${color}" stroke="white" stroke-width="2"/>
      <path d="${iconPath}" fill="white" transform="translate(12, 12) scale(0.6) translate(-12, -12)"/>
    </svg>
  `;

  return {
    url: `data:image/svg+xml;base64,${btoa(svg)}`,
    scaledSize: new window.google.maps.Size(32, 32),
    anchor: new window.google.maps.Point(16, 32),
  };
};

// Helper function to calculate time slot number based on start time
export const getTimeSlotNumber = (startTime) => {
  const time = moment(startTime, 'HH:mm:ss');
  const hour = time.hour();

  // 8:00-9:59 = 1, 10:00-11:59 = 2, 12:00-1:59 = 3, 2:00-3:59 = 4, etc.
  if (hour >= 8 && hour < 10) return 1;
  if (hour >= 10 && hour < 12) return 2;
  if (hour >= 12 && hour < 14) return 3;
  if (hour >= 14 && hour < 16) return 4;
  if (hour >= 16 && hour < 18) return 5;
  if (hour >= 18 && hour < 20) return 6;

  // Default for times outside normal range
  return Math.floor((hour - 8) / 2) + 1;
};

// Helper function to create a numbered marker icon
export const createNumberedMarker = (number, color) => {
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
      <circle cx="16" cy="16" r="14" fill="${color}" stroke="white" stroke-width="2"/>
      <text x="16" y="22" font-size="18" font-weight="bold" fill="white" text-anchor="middle" font-family="Arial, sans-serif">${number}</text>
    </svg>
  `;

  return {
    url: `data:image/svg+xml;base64,${btoa(svg)}`,
    scaledSize: new window.google.maps.Size(32, 32),
    anchor: new window.google.maps.Point(16, 32),
  };
};

// Extract appointments from API response
export const extractAppointmentsFromAPI = (allEvents) => {
  const appointmentsData = [];
  const hesAgentsMap = new Map();

  Object.entries(allEvents).forEach(([eventId, event]) => {
    if (event.address) {
      const hesName = event.agentName || 'Unknown Agent';
      const hesOid = event.oid || event.oids?.[0] || eventId;

      appointmentsData.push({
        ...event,
        hesName,
        hesOid,
        eventId,
      });

      if (!hesAgentsMap.has(hesOid)) {
        hesAgentsMap.set(hesOid, {
          oid: hesOid,
          name: hesName,
          color: getHESColor(hesAgentsMap.size), // Dynamic colorblind-safe color
          appointmentCount: 0,
        });
      }
      hesAgentsMap.get(hesOid).appointmentCount++;
    }
  });

  const appointmentsByDate = {};
  appointmentsData.forEach((apt) => {
    const date = apt.date || 'Unknown';
    appointmentsByDate[date] = (appointmentsByDate[date] || 0) + 1;
  });

  return {
    appointments: appointmentsData,
    hesAgents: Array.from(hesAgentsMap.values()),
  };
};

// Filter agents based on search term
export const filterAgentsBySearchTerm = (agents, searchTerm) => {
  if (!searchTerm) return agents;

  return agents.filter((agent) => {
    const searchableText = [
      agent.name,
      agent.displayName,
      agent.firstname,
      agent.lastname,
      `${agent.firstname} ${agent.lastname}`.trim(),
    ]
      .filter(Boolean)
      .join(' ')
      .toLowerCase();

    return searchableText.includes(searchTerm.toLowerCase());
  });
};

// Create appointment coordinates with fallback
export const getAppointmentCoordinates = (appointment) => {
  if (appointment.address?.latitude && appointment.address?.longitude) {
    return {
      lat: parseFloat(appointment.address.latitude),
      lng: parseFloat(appointment.address.longitude),
    };
  }

  // Return null if no coordinates and no address to geocode
  // This will be handled by geocoding in useMapMarkers
  if (appointment.address?.displayAddress) {
    return null;
  }

  return null;
};

// Geocode an address and return coordinates
export const geocodeAddress = (displayAddress) => {
  return new Promise((resolve) => {
    if (!displayAddress || !window.google) {
      resolve(null);
      return;
    }

    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ address: displayAddress }, (results, status) => {
      if (status === 'OK' && results[0]) {
        const {
          geometry: { location },
        } = results[0];
        resolve({
          lat: location.lat(),
          lng: location.lng(),
        });
      } else {
        resolve(null);
      }
    });
  });
};

// Create info window content for appointment
export const createAppointmentInfoContent = (
  hesName,
  customerName,
  date,
  startTime,
  endTime,
  address,
  color,
) => {
  return `
    <div style="padding: 8px; min-width: 200px;">
      <h4 style="margin: 0 0 8px 0; color: ${color};">📍 ${hesName} - Appointment</h4>
      <p style="margin: 4px 0;"><strong>Customer:</strong> ${customerName || 'N/A'}</p>
      <p style="margin: 4px 0;"><strong>Date:</strong> ${moment(
        date,
        ['YYYY-MM-DD', 'MM/DD/YYYY', 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss.SSSZ'],
        true,
      ).format('MMM DD, YYYY')}</p>
      <p style="margin: 4px 0;"><strong>Time:</strong> ${moment(startTime, 'HH:mm:ss').format(
        'h:mm A',
      )} - ${moment(endTime, 'HH:mm:ss').format('h:mm A')}</p>
      <p style="margin: 4px 0;"><strong>Address:</strong> ${address?.displayAddress ||
        address?.street}</p>
    </div>
  `;
};

// info window content for day start location
export const createHomeInfoContent = (agentName, homeAddress, color) => {
  return `
    <div style="padding: 8px; min-width: 200px;">
      <h4 style="margin: 0 0 8px 0; color: ${color};">📍 ${agentName} - Day Start</h4>
      <p style="margin: 4px 0;"><strong>Address:</strong> ${homeAddress}</p>
      <p style="margin: 4px 0; font-style: italic;">Starting point for daily routes</p>
    </div>
  `;
};

// Connecticut center coordinates
export const CT_CENTER = { lat: 41.6032, lng: -73.0877 };

// Home icon SVG path
export const HOME_ICON_PATH = 'M12 2L2 7v10c0 1.1.9 2 2 2h6V13h4v6h6c1.1 0 2-.9 2-2V7L12 2z';

// Map pin icon SVG path
export const MAP_PIN_ICON_PATH =
  'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z';
