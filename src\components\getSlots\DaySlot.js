import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';
import { Clickable } from '@components/global';
import { selectedEventState } from '@recoil/eventSidebar';

const StyledDaySlot = styled(Clickable)`
  display: flex;
  flex-direction: column;
  min-width: 15%;
  border-radius: 4px;
  margin-right: 10px;
  overflow: hidden;
  border: 1px solid
    ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.secondary[500])};
  font-weight: ${({ selected }) => (selected ? 'bold' : 'normal')};
`;

const DayOfWeekHeader = styled.div`
  display: flex;
  justify-content: center;
  text-transform: uppercase;
  // TODO: move this to variables
  background-color: #e1e8e7;
  color: ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.secondary[500])};
  border-bottom: 1px solid
    ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.secondary[500])};
`;

const DateBody = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  color: ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.primary[500])};
`;

const DaySlot = ({ date }) => {
  const [selectedSlot, setSelectedSlot] = useRecoilState(selectedEventState);

  const handleSelectDate = () => {
    // Have to reset everything after the date selection (oids, time)
    setSelectedSlot({
      ...selectedSlot,
      oids: [],
      date,
      startTime: '09:00:00',
      endTime: '17:00:00',
    });
  };

  const selected = selectedSlot.date === date;
  const dayOfWeek = moment(date, 'MM/DD/yyyy').format('ddd');
  const styledDate = moment(date, 'MM/DD/yyyy').format('MM/DD');
  return (
    <StyledDaySlot onClick={handleSelectDate} selected={selected}>
      <DayOfWeekHeader selected={selected}>{dayOfWeek}</DayOfWeekHeader>
      <DateBody selected={selected}>{styledDate}</DateBody>
    </StyledDaySlot>
  );
};

DaySlot.propTypes = {
  date: PropTypes.string.isRequired,
};

export default DaySlot;
