import React from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileUpload } from '@fortawesome/free-solid-svg-icons';

const StyledIcon = styled(FontAwesomeIcon)`
  align-self: flex-end;
  margin: 4px;
`;

const NeedsDocs = () => {
  return <StyledIcon icon={faFileUpload} title="Event has not been resulted on doc repo" />;
};

export default NeedsDocs;
