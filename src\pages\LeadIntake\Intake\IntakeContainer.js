import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { useParams } from 'react-router-dom';

import { PageHeader } from '@pages/Components';
import { leadIntakeValuesState, showDuplicateRecordsAtom } from '@recoil/leadIntake';
import DataIntakeForm from '@components/DataIntakeForm';
import validateRequiredParams from '@utils/validateRequiredParams';
import { activeTabIndexAtom } from '@recoil/app';
import { formValuesState, activeTabState } from '@recoil/dataIntakeForm';
import { hesAgentFormOptionsState } from '@recoil/agents';
import { AgentsManager } from '@utils/APIManager';
import { throwError } from '@utils/EventEmitter';

import LeadIntakeEffects from '../utils/setters/LeadIntakeEffect';
import { useFieldsAndTabsForIntake } from '../utils/getters/useTabsAndFieldsForIntake';
import {
  getLeadIntakeRequiredFieldsCT,
  getLeadIntakeRequiredFields,
} from '../FormSchema/requiredFields';
import { LeadIntakeScript } from '../LeadIntakeScript/LeadIntakeScript';
import { leadIntakeFields } from '../FormSchema/leadIntakeMap';
import DuplicateRecordsContainer from '../DuplicateRecordsTable/DuplicateRecordsContainer';
import { isAuthorized } from '../../../utils/AuthUtils';
import FormActionButtons from '../utils/actions/FormActionButtons';

const LeadIntakeContainer = styled.div`
  label,
  select,
  input,
  p,
  span,
  li {
    font-size: 16px;
  }
  h6 {
    font-size: 15px;
  }
`;
const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: calc(100% - 48px);
  position: 'relative';
`;

const requiredFieldsMap = {
  MA: getLeadIntakeRequiredFields,
  CT: getLeadIntakeRequiredFieldsCT,
};

const IntakeContainer = (props) => {
  const { state = 'MA', departments, handleSubmit, validateNextTab, nextTab } = props;
  const { leadId } = useParams();
  const getLeadIntakeRequiredFields = requiredFieldsMap[state];

  const formValues = useRecoilValue(formValuesState);
  const showDuplicateRecordsTable = useRecoilValue(showDuplicateRecordsAtom);
  const [activeTab, setActiveTab] = useRecoilState(activeTabState);
  const [activeTabIndex, setActiveTabIndex] = useRecoilState(activeTabIndexAtom(['tabs']));
  const setHesAgents = useSetRecoilState(hesAgentFormOptionsState);
  const [resetDuplicateLeadRecordsTable, setResetDuplicateLeadRecordsTable] = useState(false);
  const requiredFields = getLeadIntakeRequiredFields(formValues);

  const {
    heaOrHvac,
    atAnEvent,
    isCampaignIdValid,
    isAuditorValueOnSf,
    discountedRateCodeForCIA,
    doYouHaveHeat,
  } = formValues;

  const { leadIntakeTabs, leadIntakeMap } = useFieldsAndTabsForIntake(
    heaOrHvac,
    atAnEvent,
    isCampaignIdValid,
    leadId,
    isAuditorValueOnSf,
    nextTab,
    state,
  );

  const [stepValidation, setStepValidation] = useState(
    Array.from({ length: leadIntakeTabs.length }, () => false),
  );

  const isBA = isCampaignIdValid && atAnEvent === 'Yes' && isAuthorized('Agent', 'Marketing', true);

  useEffect(() => {
    const getAuditors = async () => {
      try {
        const response = await AgentsManager.getCrewsByStateAndDepartment({ [state]: departments });
        setHesAgents(response?.crews || []);
        return response;
      } catch (err) {
        console.error(err);
        return throwError(err);
      }
    };

    getAuditors();
  }, []);

  useEffect(() => {
    // Validation Checks on Each Section, If any section passes, we show check mark icon on tab section tab
    if (!isBA && discountedRateCodeForCIA === 'Yes' && doYouHaveHeat === 'No') {
      requiredFields.sourceInfo.doYouHaveHeat = false;
    }
    const stepValidationList = Object.values(requiredFields).map((section) => {
      const isEmptyObject = Object.keys(section).length === 0;
      if (isEmptyObject) {
        return false;
      }
      return !validateRequiredParams(section).length > 0;
    });
    setStepValidation(stepValidationList);
  }, [formValues, activeTabIndex]);

  const backTab = (tabIndex = null) => {
    setResetDuplicateLeadRecordsTable(true);
    setActiveTabIndex(tabIndex !== null ? tabIndex : activeTabIndex - 1);
    setActiveTab(
      tabIndex !== null ? leadIntakeTabs[0]?.name : leadIntakeTabs[activeTabIndex - 1]?.name,
    );
  };

  const excludedTabsForScript = ['scheduleEvent', 'wrapUp'];
  const showLeadIntake = !showDuplicateRecordsTable;

  const formActions = (
    <FormActionButtons
      leadIntakeTabs={leadIntakeTabs}
      handleSubmit={handleSubmit}
      validateNextTab={validateNextTab}
      setResetDuplicateLeadRecordsTable={setResetDuplicateLeadRecordsTable}
    />
  );

  return (
    <Wrapper>
      <PageHeader>Lead Intake</PageHeader>
      <LeadIntakeEffects>
        <DuplicateRecordsContainer
          resetDuplicateLeadRecordsTable={resetDuplicateLeadRecordsTable}
          backTab={backTab}
        />
        {showLeadIntake && (
          <LeadIntakeContainer>
            <DataIntakeForm
              map={leadIntakeMap}
              fields={leadIntakeFields}
              valuesState={leadIntakeValuesState}
              tabs={leadIntakeTabs}
              actions={formActions}
              stepValidation={stepValidation}
            />
            {!excludedTabsForScript.includes(activeTab) && (
              <LeadIntakeScript formValues={formValues} state={state} />
            )}
          </LeadIntakeContainer>
        )}
      </LeadIntakeEffects>
    </Wrapper>
  );
};

IntakeContainer.propTypes = {
  state: PropTypes.string.isRequired,
  departments: PropTypes.arrayOf(PropTypes.string).isRequired,
  handleSubmit: PropTypes.func.isRequired,
  validateNextTab: PropTypes.func.isRequired,
  nextTab: PropTypes.func.isRequired,
};

export default IntakeContainer;
