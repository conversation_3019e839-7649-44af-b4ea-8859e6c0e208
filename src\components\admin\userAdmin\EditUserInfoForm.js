import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useRecoilValue, useSetRecoilState, useRecoilState } from 'recoil';

import {
  Container,
  Row,
  Col,
  FormInput,
  FormSelect,
  handleFormFieldChange,
} from '@components/global/Form';
import { UsersManager } from '@utils/APIManager';
import { companiesSelector, statesSelector } from '@recoil/app';
import { adminInfoChangesState } from '@recoil/admin';
import { selectedUserState } from '@recoil/admin/users';

const EditUserInfoForm = () => {
  const [user, setUser] = useRecoilState(selectedUserState);
  const {
    firstname,
    lastname,
    displayName = '',
    company,
    state,
    sfId,
    sfId2,
    oid,
    assignedCrew = '',
  } = user;
  const [isCrew, setIsCrew] = useState(false);
  const [crewOptions, setCrewOptions] = useState([]);
  const [crewOidDict, setCrewOidDict] = useState({});

  const prevState = useRef();
  const companyOptions = useRecoilValue(companiesSelector(state));
  const statesOptions = useRecoilValue(statesSelector);
  // This is used in the Details.js file to determine if addresses are valid to save
  const setUserInfoChanges = useSetRecoilState(adminInfoChangesState);

  const sortCrewOptions = (crews) => {
    const oidDict = {};
    const options = [];
    crews.forEach((crew) => {
      const { displayName, oid } = crew;
      oidDict[oid] = displayName;
      options.push(displayName);
    });
    return { oidDict, options };
  };

  const getUserRoles = useCallback(async () => {
    if (oid) {
      const userRoles = await UsersManager.getUserRoles(oid);
      if (userRoles?.length) {
        const isCrew = userRoles.some(({ department }) => department === 'Crew');
        if (isCrew) {
          const crewOptions = await UsersManager.getUsersOfCrewType('6');
          const { oidDict, options } = sortCrewOptions(crewOptions);
          setCrewOptions(options);
          setCrewOidDict(oidDict);
        }
        setIsCrew(isCrew);
      }
    }
    // eslint-disable-next-line
  }, [oid]);

  useEffect(() => {
    getUserRoles();
  }, [getUserRoles]);

  useEffect(() => {
    prevState.user = user;
  }, [user]);

  const handleFieldChange = (e, updatedUser = prevState.user) => {
    const { name, value } = e.target;
    handleFormFieldChange(e, updatedUser, setUser);
    // stores only those values in an atom whose value is changed
    setUserInfoChanges({ name, value });
  };

  return (
    <Container>
      <Row>
        <Col size={1}>
          <FormInput
            required
            title="First name"
            placeholder="Enter Firstname"
            name="firstname"
            value={firstname}
            onChange={handleFieldChange}
          />
          <FormInput
            required
            title="Display Name"
            placeholder="Enter display name"
            name="displayName"
            value={displayName}
            onChange={handleFieldChange}
          />
          <FormSelect
            required
            title="Company Name"
            placeholder="Enter Company Name"
            name="company"
            value={company}
            onChange={handleFieldChange}
            options={companyOptions}
          />
          {isCrew && (
            <FormSelect
              title="Assigned Crew"
              placeholder="Select Crew"
              name="assignedCrew"
              value={crewOidDict[assignedCrew] || assignedCrew}
              onChange={handleFieldChange}
              options={crewOptions}
            />
          )}
        </Col>
        <Col size={1}>
          <FormInput
            required
            title="Last name"
            placeholder="Enter Lastname"
            name="lastname"
            value={lastname}
            onChange={handleFieldChange}
          />
          <FormSelect
            required
            title="States"
            placeholder="Select States"
            name="state"
            value={state}
            onChange={handleFieldChange}
            options={statesOptions}
          />
          <FormInput
            title="Salesforce 1.0 ID"
            placeholder="User or Employee ID"
            name="sfId"
            value={sfId}
            onChange={handleFieldChange}
          />
          <FormInput
            title="Salesforce 2.0 ID"
            placeholder="User or Employee ID"
            name="sfId2"
            value={sfId2}
            onChange={handleFieldChange}
          />
        </Col>
      </Row>
    </Container>
  );
};

EditUserInfoForm.propTypes = {};

export default EditUserInfoForm;
