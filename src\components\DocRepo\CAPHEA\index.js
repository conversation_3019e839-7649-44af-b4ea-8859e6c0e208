import React, { useState, useEffect } from 'react';
import { useSetRecoilState, useRecoilState, useResetRecoilState } from 'recoil';
import PropTypes from 'prop-types';

import { capHeaResultingQuestionsState, documentsSelector } from '@recoil/docRepo';
import ResultingQuestionsHEA from './ResultingQuestionsHEA';
import ResultingQuestionsHVACSales from './ResultingQuestionsHVACSales';
import DocRepo from './DocRepo';
import { fetchDocumentsFromS3 } from '../DocRepoButtons';

const CapDocRepo = ({ uniqueId, department }) => {
  const [showResulting, setShowResulting] = useState(true);
  const [resulting, setResulting] = useRecoilState(capHeaResultingQuestionsState);
  const resetResulting = useResetRecoilState(capHeaResultingQuestionsState);
  const setDocuments = useSetRecoilState(documentsSelector);

  const ResultingFormMaps = {
    'HEA-CAP': ResultingQuestionsHEA,
    'HVAC_Sales-CAP': ResultingQuestionsHVACSales,
  };

  const Form = ResultingFormMaps[department];

  useEffect(() => {
    if (resulting.uniqueId || uniqueId) getDocs();
  }, [resulting.uniqueId, uniqueId, department, setResulting]);

  const getDocs = async () => {
    const response = await fetchDocumentsFromS3(
      { state: 'MA', department, uniqueId },
      setDocuments,
      resulting,
      setResulting,
      resetResulting,
    );
    const resulting = Object.keys(response)?.filter((result) => {
      return !['appliancePerformed'].includes(result);
    });
    const showResulting = !(resulting?.length > 0);
    setShowResulting(showResulting);
  };

  return (
    <>
      {showResulting ? (
        <Form
          uniqueId={uniqueId}
          department={department}
          setShowResulting={setShowResulting}
          getDocs={getDocs}
        />
      ) : (
        <DocRepo uniqueId={uniqueId} department={department} setShowResulting={setShowResulting} />
      )}
    </>
  );
};

CapDocRepo.propTypes = {
  uniqueId: PropTypes.string.isRequired,
  department: PropTypes.string.isRequired,
};

export default CapDocRepo;
