import React from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import styled from 'styled-components';
import { isAuthorized } from '@utils/AuthUtils';
import { Button } from '@components/global/Buttons';
import { Truck } from '@styled-icons/boxicons-solid/Truck';

const TruckIconStyled = styled(Truck)`
  color: ${({ theme }) => theme.primary[500]};
  height: 20px;
  width: 20px;
  margin: 0 auto;
`;
const EventsPageContainer = styled.div`
  margin: 10px 0px;
  @media (max-width: 1149px) {
    margin-left: 0;
    margin-right: 0;
  }
  @media (max-width: 450px) {
    padding-top: 25px;
  }
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  @media (max-width: 450px) {
    display: flex;
    flex-direction: column-reverse;
  }
`;

const StyledCalendarButton = styled(Button)`
  color: #707172;
  border-color: #707172;
  @media (max-width: 450px) {
    height: fit-content;
    margin-bottom: 20px;
  }
`;

const PartnersLink = (props) => {
  const displayPartnersLink =
    isAuthorized('Agent', 'Partners', true) || isAuthorized('Agent', 'Insulation Partners', true);

  const openPartnerJobsPage = () => {
    const { history } = props;
    history.push('/view-partners-schedule');
  };
  return (
    <>
      {displayPartnersLink && (
        <EventsPageContainer>
          <HeaderContainer>
            <StyledCalendarButton onClick={openPartnerJobsPage}>
              <TruckIconStyled /> Sub Hub Jobs
            </StyledCalendarButton>
          </HeaderContainer>
        </EventsPageContainer>
      )}
    </>
  );
};

PartnersLink.propTypes = {
  history: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
};

export default withRouter(PartnersLink);
