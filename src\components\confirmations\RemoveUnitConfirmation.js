const fireRemoveUnitConfirmation = async (theme) => {
  // Need to lazy load swal config so that the .app-content node exists in dom
  const { createSwalWithTheme } = await import('@config/swalConfig');
  const swal = createSwalWithTheme(theme);

  const { value: cancelOrReschedule } = await swal.fire({
    titleText: 'Remove unit?',
    input: 'select',
    inputOptions: {
      reschedule: 'Reschedule',
      cancel: 'Cancel',
    },
    inputPlaceholder: 'Cancel or Reschedule?',
    showCancelButton: true,
    icon: 'warning',
    confirmButtonText: 'Remove',
    cancelButtonText: 'No',
    inputValidator: (value) => {
      return new Promise((resolve) => {
        if (value) {
          resolve();
        } else {
          resolve('You must select whether the unit should be canceled or rescheduled.');
        }
      });
    },
  });

  return cancelOrReschedule;
};

export { fireRemoveUnitConfirmation };
