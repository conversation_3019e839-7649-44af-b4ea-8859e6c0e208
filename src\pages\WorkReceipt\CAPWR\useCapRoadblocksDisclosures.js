import { addKeyNameToObject } from '@components/DataIntakeForm/dataIntakeFormHelpers';
import { cloneDeep } from 'lodash';
import { capRoadBlocksMap } from './FormSchema/formSchema';

const capRoadblocksDisclosuresKeys = Object.keys(capRoadBlocksMap);

export const useCapRoadblocksDisclosures = () => {
  const updateCapRoadblockValuesForExcelSheet = (formValues = {}) => {
    let updatedFormValues = cloneDeep(formValues);

    // The logic for tenant info whole section in the Excel sheet is unconventional.
    // For the first unit, it looks for fields like 'customerName' and 'customerAddress' 'customerEmail'.
    // However, for the second unit, it switches to 'Customer_Name2' and 'Customer_Address2'.
    // This inconsistent naming can cause issues where tenant information for the first unit is overwritten if below keys are not removed.
    // I spoke with <PERSON> about this, and they mentioned that the naming convention will be updated later.
    const mapValuesWithKeysForCapExcelSheet = () => {
      const valuesMap = addKeyNameToObject({
        ampPerformed: {
          prefix: 'AMP_',
          addIndex: true,
        },
        ampEligible: {
          prefix: 'AMPELIGIBLE',
          addIndex: true,
        },
        gasHeatPumpRecs: {
          prefix: 'gasHeatPumpRecs',
          addIndex: true,
        },
        electricAccountNumber: {
          prefix: 'Electric',
          addIndex: true,
        },
        steamSystem: {
          prefix: 'STEAMSYSTM',
          addIndex: true,
        },
        areYouRecommendingGasSpaceHeaterReplacement: {
          prefix: 'disclosure_Electrical__knt_multi',
          addIndex: false,
        },
        electricProvider: {
          prefix: 'ELECPROV',
          addIndex: true,
        },
        gasProvider: {
          prefix: 'GASPROV',
          addIndex: true,
        },
        areasOnWorkscope: {
          isList: true,
          addIndex: false,
          prefix: false,
        },
        customerName: {
          prefix: 'Customer_Name',
          addIndex: true,
        },
        customerAddress: {
          prefix: 'customer_addr',
          addIndex: true,
        },
        cityStateZip: {
          prefix: 'customer_city_state_zip',
          addIndex: true,
        },
        customerPhone: {
          prefix: 'customer_phone',
          addIndex: true,
        },
        customerEmail: {
          prefix: 'customer_email',
          addIndex: true,
        },
        siteId: {
          prefix: 'site_ID',
          addIndex: true,
        },
        rentAmount: {
          prefix: 'RentU',
          addIndex: true,
        },
      });

      updatedFormValues = updatedFormValues.map((unitInfo, index) => {
        const values = Object.values(valuesMap).reduce((acc, fieldValue) => {
          const { prefix, addIndex, name, isList = false } = fieldValue;
          if (unitInfo[name] === 'Yes') {
            return {
              ...acc,
              [`${prefix}${addIndex ? index + 1 : ''}`]: unitInfo[name] === 'Yes' ? 'Yes' : 'No',
            };
          }

          if (typeof unitInfo[name] === 'string' && unitInfo[name]) {
            return { ...acc, [`${prefix}${addIndex ? index + 1 : ''}`]: unitInfo[name] };
          }
          // Ensure both 'electric provider' and 'gas provider' fields have values.
          // If either of these fields is missing a value, set it to 'None'.
          // This is necessary because the formulas for 'electric provider' and 'gas provider'
          // in the CAP Excel sheet depend on these fields being non-empty.
          // Without this, the Appliance Visit Type will not auto-populate correctly in the CAP sheet.
          if (name === 'gasProvider' || name === 'electricProvider') {
            return { ...acc, [`${prefix}${addIndex ? index + 1 : ''}`]: unitInfo[name] || 'None' };
          }

          if (Array.isArray(unitInfo[name]) && isList) {
            let listValue;
            unitInfo[name].forEach((area) => {
              listValue = { ...listValue, [area]: 'Yes' };
            });
            return { ...acc, ...listValue };
          }
          return acc;
        }, {});
        return { ...unitInfo, ...values };
      });
      return updatedFormValues;
    };

    updatedFormValues = mapValuesWithKeysForCapExcelSheet(updatedFormValues);
    // When the user selects "Single Family" as the Property Owner type,
    // we need to clear out customer-related information (`customerInfo` keys)
    // from the `updatedFormValues` array.
    // This ensures that the Property Owner section is filled correctly without duplicating
    // information from the "Unit 1 Renter" section.
    // Note: A `useEffect` in `CapWorkReceipt.js` handles this logic dynamically,
    // so we remove these keys here to avoid showing redundant information ON GENERATED SHEET.
    const swapCustomerKeysWithPropertyOwnerFieldsByRemoving = () => {
      const customerInformationKeys = [
        'customerName',
        'customerAddress',
        'cityStateZip',
        'customerPhone',
        'customerEmail',
        'siteId',
      ];

      /* eslint-disable no-loop-func */
      for (let i = 0; i < updatedFormValues.length; i++) {
        customerInformationKeys.forEach((key) => {
          if (
            Object.prototype.hasOwnProperty.call(updatedFormValues[i], key) &&
            formValues[0].ownerOccupancyType === 'Single Family'
          ) {
            delete updatedFormValues[i][key];
          }
        });
      }
      /* eslint-enable no-loop-func */

      return updatedFormValues;
    };

    updatedFormValues = swapCustomerKeysWithPropertyOwnerFieldsByRemoving(updatedFormValues);

    // Define the keys related to bathroom fan installations or replacements.
    // If any of these keys have a value greater than 0 in `values`,
    // we need to set "Yes" for `disclosureOtherAtticAirSealingRoadblock` in `updatedFormValues`.
    // This ensures the Excel sheet correctly displays "Yes" for Bath Fan Install/Replace.
    // 'numberOfBathroomsWindowsUnder30cfm',
    // 'numberOfBathroomsWindowsUnder50cfm',
    // 'numberOfBathroomsFans',
    const mapBathFanInstallReplaceRoadblockSupport = (updatedFormValues) => {
      const values = [...updatedFormValues];
      for (let iterator = 0; iterator <= updatedFormValues.length; iterator++) {
        if (
          Number(values[iterator]?.numberOfBathroomsWindowsUnder30cfm) > 0 ||
          Number(values[iterator]?.numberOfBathroomsWindowsUnder50cfm) > 0 ||
          Number(values[iterator]?.numberOfBathroomsFans) > 0
        ) {
          values[0].disclosureOtherAtticAirSealingRoadblock = 'Yes';
          return values;
        }
      }
      return values;
    };

    if (updatedFormValues.length === 1) {
      const result = mapBathFanInstallReplaceRoadblockSupport(updatedFormValues);
      return result;
    }

    updatedFormValues.forEach((value) => {
      Object.entries(value).forEach(([key]) => {
        if (capRoadblocksDisclosuresKeys.includes(key)) {
          // Check each unit in `formValues` to see if any roadblock has a value of "Yes".
          // If any roadblock is marked as "Yes" for any unit index,
          // we set it as "Yes" for the `generateWR` lambda function to indicate roadblocks.
          // This allows the roadblocks to be populated correctly on the Excel sheet.
          const isDisclosureYes = formValues.some((_, index) => formValues[index][key] === 'Yes');
          if (isDisclosureYes) updatedFormValues[0][key] = 'Yes';
        }
      });
    });
    updatedFormValues = mapBathFanInstallReplaceRoadblockSupport(updatedFormValues);

    // Delete Roadblocks values and keys from all indexes except first index
    // Because we show only one column of Roadblocks on CAP Generated Sheet.
    // Please check above code that is checking if any one of roadblock from
    // any unit is Yes then we consider it as yes.
    if (updatedFormValues.length > 1) {
      updatedFormValues.forEach((_, index) => {
        if (index !== 0) {
          capRoadblocksDisclosuresKeys.forEach((key) => {
            delete updatedFormValues[index][key];
          });
        }
      });
    }

    return updatedFormValues;
  };
  return updateCapRoadblockValuesForExcelSheet;
};
