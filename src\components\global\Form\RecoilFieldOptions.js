import React from 'react';
import PropTypes from 'prop-types';
import { useRecoilValue } from 'recoil';

const RecoilFieldOptions = ({ recoilOptions, Component, ...props }) => {
  const options = useRecoilValue(recoilOptions);
  if (!options) return null;
  return <Component {...props} options={options} />;
};

RecoilFieldOptions.propTypes = {
  recoilOptions: PropTypes.shape({}).isRequired,
  Component: PropTypes.oneOfType([PropTypes.node, PropTypes.func]).isRequired,
};

export default RecoilFieldOptions;
