import { useState, useEffect } from 'react';

/** Custom hook to load HES agent day start addresses */
const useHESAgentHomes = (hesAgents) => {
  const [hesAgentsWithHomes, setHesAgentsWithHomes] = useState([]);

  useEffect(() => {
    if (hesAgents.length === 0 || !window.google) return;

    const loadHesAgentHomes = async () => {
      try {
        const { default: AgentsManager } = await import('@utils/APIManager/AgentsManager');
        const { crews: fullAgentDetails } = await AgentsManager.getCrewsByStateAndDepartment({
          CT: ['HEA'],
        });

        const hesAgentsWithHomesTemp = [];
        let geocodingCount = 0;
        let geocodingCompleted = 0;

        const updateState = () => {
          setHesAgentsWithHomes([...hesAgentsWithHomesTemp]);
        };

        const processAgent = (agent) => {
          const fullAgentDetail = fullAgentDetails.find((fullAgent) => fullAgent.oid === agent.oid);
          if (!fullAgentDetail) return;

          // Use dayStartAddress instead of homeAddress
          const {
            dayStartAddress: dayStartAddressObj = fullAgentDetail.homeAddress ||
              fullAgentDetail.address,
          } = fullAgentDetail;
          if (!dayStartAddressObj) return;

          const dayStartAddress =
            typeof dayStartAddressObj === 'string'
              ? dayStartAddressObj
              : dayStartAddressObj.displayAddress ||
                dayStartAddressObj.address ||
                dayStartAddressObj.street;

          if (!dayStartAddress || typeof dayStartAddress !== 'string') return;

          // Use latitude and longitude from dayStartAddress if available
          const { latitude: lat, longitude: lng } = dayStartAddressObj;
          if (typeof lat === 'number' && typeof lng === 'number') {
            const agentWithHome = {
              ...agent,
              ...fullAgentDetail,
              homeAddress: dayStartAddress,
              displayName: fullAgentDetail.displayName || agent.name || 'HES Agent',
              homeCoordinates: {
                lat,
                lng,
              },
            };
            hesAgentsWithHomesTemp.push(agentWithHome);
            return;
          }

          // Fallback to geocoding if lat/lng not available
          geocodingCount++;
          const geocoder = new window.google.maps.Geocoder();
          geocoder.geocode({ address: dayStartAddress }, (results, status) => {
            geocodingCompleted++;
            if (status === 'OK' && results[0]) {
              const { location } = results[0].geometry;
              const agentWithHome = {
                ...agent,
                ...fullAgentDetail,
                homeAddress: dayStartAddress,
                displayName: fullAgentDetail.displayName || agent.name || 'HES Agent',
                homeCoordinates: {
                  lat: location.lat(),
                  lng: location.lng(),
                },
              };
              hesAgentsWithHomesTemp.push(agentWithHome);
            }
            // Update state after all geocoding is complete
            if (geocodingCompleted === geocodingCount) {
              updateState();
            }
          });
        };

        hesAgents.forEach(processAgent);

        // If no geocoding needed, update state immediately
        if (geocodingCount === 0) {
          updateState();
        }
      } catch (error) {
        console.error('Error loading HES agent day start addresses:', error);
      }
    };

    loadHesAgentHomes();
  }, [hesAgents]);

  return { hesAgentsWithHomes };
};

export default useHESAgentHomes;
