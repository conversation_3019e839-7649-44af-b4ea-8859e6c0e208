import React, { useState, lazy } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { House as HouseIcon } from '@styled-icons/bootstrap/House';
import { BarChart as BarChartIcon } from '@styled-icons/bootstrap/BarChart';
import { ChevronCompactDown as DownArrowIcon } from '@styled-icons/bootstrap/ChevronCompactDown';
import { ChevronCompactUp as UpArrowIcon } from '@styled-icons/bootstrap/ChevronCompactUp';

import Tooltip from '@components/global/Tooltip/Tooltip';
import { Header } from '@components/global';

const ICWForm = lazy(() => import('./ICWForm'));
const AuditQCFailForm = lazy(() => import('./AuditQCFailForm'));
const UnresultedVisitsForm = lazy(() => import('./UnresultedVisitsForm'));
const DHWICWForm = lazy(() => import('./DHW-ICWForm'));
const IETeamUnresultedVisitsForm = lazy(() => import('./IETeamUnresultedVisitsForm'));
const AMPPendingReport = lazy(() => import('./AMPPendingReport'));
const HVACIcwReport = lazy(() => import('./HVACIcwReport'));
const CAPHVACFailedSalesQC = lazy(() => import('./CAPHVACFailedSalesQC'));
const CAPHVACReconcileReport = lazy(() => import('./CAPHVACReconcileReport'));
const IETeamICWReport = lazy(() => import('./IETeamICWReport'));
const LTAWaiverMissing = lazy(() => import('./LTAWaiverMissing'));
const AuditIncentiveForm = lazy(() => import('./HEAPayDashboardForm/AuditIncentiveForm'));
const HEAReferralsForm = lazy(() => import('./HEAPayDashboardForm/HEAReferralsForm'));
const HEAISMIncentive = lazy(() => import('./HEAPayDashboardForm/HEAISMIncentive'));
const HEAWXVolumeBonus = lazy(() => import('./HEAPayDashboardForm/HEAWXVolumeBonus'));
const HEAReturnCSTPay = lazy(() => import('./HEAPayDashboardForm/HEAReturnCSTPay'));
const HEAReturnISMPay = lazy(() => import('./HEAPayDashboardForm/HEAReturnISMPay'));
const HEAReferralBonus = lazy(() => import('./HEAPayDashboardForm/HEAReferralBonus'));
const SpecQualityBonusForm = lazy(() => import('./HEAPayDashboardForm/SpecQualityBonusForm'));
const HVACComissionForm = lazy(() => import('./HEAPayDashboardForm/HVACComissionForm'));
const WXManagerIncentiveForm = lazy(() => import('./HEAPayDashboardForm/WXManagerIncentiveForm'));
const IncomeEligibleWXIncentive = lazy(() =>
  import('./HEAPayDashboardForm/IncomeEligibleWXIncentive'),
);
const GoodSpecBonusForm = lazy(() => import('./HEAPayDashboardForm/GoodSpecBonusForm'));
const CapHvacIncentiveForm = lazy(() => import('./HEAPayDashboardForm/CapHvacIncentiveForm'));
const HesHvacSubmitToCAPForm = lazy(() => import('./HEAPayDashboardForm/HES-HVAC-SubmitToCAPForm'));
const HesWxWeeklyIncentiveForm = lazy(() =>
  import('./HEAPayDashboardForm/HesWxWeeklyIncentiveForm'),
);
const WxCommissionReportForm = lazy(() => import('./HEAPayDashboardForm/WxCommissionReportForm'));

const CalendarRowsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-flow: row dense;
  flex: 1 1 auto;
  overflow: auto;
  padding: 5px;
`;

const DashboardHeader = styled.div`
  display: flex;
  flex-direction: row;
  margin: 12px 12px;
  font-size: 24px;
  font-weight: 500;
  padding: 8px;
  color: #032d60;
  @media (max-width: 450px) {
    font-size: 20px;
    padding-bottom: 10px;
    align-self: flex-start;
  }
`;

const HouseIconStyle = styled(HouseIcon)`
  height: 20px;
  margin-bottom: 5px;
  margin-left: 5px;
  margin-right: 5px;
  & :hover {
    color: ${({ theme }) => theme.secondary[700]};
  }
`;

const BarChartIconStyle = styled(BarChartIcon)`
  height: 35px;
  margin-right: 5px;
  margin-bottom: 2px;
  color: white;
  background-color: #707172;
  padding: 4px;
  border-radius: 2px;
`;

const UpArrowIconStyle = styled(UpArrowIcon)`
  height: 20px;
  margin-bottom: 5px;
  margin-left: 5px;
  margin-right: 5px;
  & :hover {
    color: ${({ theme }) => theme.secondary[700]};
  }
`;
const DownArrowIconStyle = styled(DownArrowIcon)`
  height: 20px;
  margin-bottom: 5px;
  margin-left: 5px;
  margin-right: 5px;
  & :hover {
    color: ${({ theme }) => theme.secondary[700]};
  }
`;

const RecordDetailsContainer = styled.div`
  padding: 1em;
  width: 100%;
  display: ${({ openPanel }) => (!openPanel ? 'none' : 'block')};
`;

const DashboardCard = styled.div`
  margin-bottom: 10px;
  border: 1px solid black;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  background-color: white;
  width: 99%;
  grid-column: ${({ column }) => {
    return column;
  }};
  grid-row: ${({ row }) => {
    return row;
  }};
`;

const HeaderContainer = styled.div`
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: #f3f3f3;
  border-bottom: 1px solid #c9c9c9;
`;

const ReportMetricsContainer = styled.div`
  position: relative;
  overflow: visible;
  border-bottom: 1px solid #c9c9c9;
  padding-bottom: 0.75rem;
  display: flex;
  flex-direction: row;
`;

const MetricsUnorderedList = styled.ul`
  display: contents;
  list-style: none;
`;

const MetricsListContainer = styled.li`
  padding: 10px;
`;

const MetricsTitle = styled.div`
  padding-bottom: 0.25rem;
  color: #747474;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: ${({ bold }) => bold};
`;

const MetricsValue = styled.div`
  font-size: 1.5rem;
  color: #032d60;
`;

const ReportDetailsContainer = styled.div`
  padding: 10px;
`;

const Record = styled.div`
  font-size: medium;
  cursor: pointer;
  padding: 5px;
  border-radius: 5px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: ${({ isRecordSelected, highlightRecord }) => {
    let color = '';
    if (isRecordSelected && !highlightRecord) color = '#f3f3f3';
    else if (highlightRecord) color = 'red';
    return color;
  }};
  color: ${({ highlightRecord }) => {
    return highlightRecord ? 'white' : null;
  }};
  & :hover {
    ${({ highlightRecord }) => {
      return !highlightRecord
        ? `color: ${({ theme }) => theme.secondary[700]}; background-color: #f3f3f3;`
        : null;
    }}
    border: 0.5px solid lightblue;
  }
`;

const Banner = styled.div`
  border-radius: 5px;
  text-align: center;
  background-color: #f3f3f3;
  padding: 5px;
  margin-top: 15px;
  margin-bottom: 15px;
  grid-column: 1 / -1;
  grid-row: ${({ row }) => row};
`;

const HorizontalDivider = styled.hr`
  margin-top: 1px;
  margin-bottom: 3px;
`;

const TimeoutComponent = styled.div`
  text-align: center;
`;

const Dashboard = ({ details = [], region = null }) => {
  const [selectedUniqueId, setSelectedUniqueId] = useState({});
  const [hoveringDeal, setHoveringDeal] = useState(null);

  const recordDetailsForm = {
    incorrectlyClosedWon: ICWForm,
    auditQCFail: AuditQCFailForm,
    unresultedVisits: UnresultedVisitsForm,
    dhwIcws: DHWICWForm,
    auditIncentive: AuditIncentiveForm,
    heaReferrals: HEAReferralsForm,
    ismIncentive: HEAISMIncentive,
    hesWXVolumeBonus: HEAWXVolumeBonus,
    hesWXVolumeBonusCurrentMonth: HEAWXVolumeBonus,
    returnCSTPay: HEAReturnCSTPay,
    returnISMPay: HEAReturnISMPay,
    incomeEligibleWxIncentive: IncomeEligibleWXIncentive,
    referralBonus: HEAReferralBonus,
    referralBonusCurrentMonth: HEAReferralBonus,
    specQualityBonus: SpecQualityBonusForm,
    hvacComission: HVACComissionForm,
    hesWxManagerIncentive: WXManagerIncentiveForm,
    goodSpecBonus: GoodSpecBonusForm,
    goodSpecBonusCurrentMonth: GoodSpecBonusForm,
    capHvacIncentive: CapHvacIncentiveForm,
    hesHvacPaySubmitToCAP: HesHvacSubmitToCAPForm,
    hesWxWeeklyIncentive: HesWxWeeklyIncentiveForm,
    wxCommissionReport: WxCommissionReportForm,
    ieTeamUnResultedVisit: IETeamUnresultedVisitsForm,
    ampPendingReport: AMPPendingReport,
    hvacIcwReport: HVACIcwReport,
    capHVACFailedSalesQC: CAPHVACFailedSalesQC,
    capHVACReconcileReport: CAPHVACReconcileReport,
    ltaWaiverMissing: LTAWaiverMissing,
    ieTeamICWReport: IETeamICWReport,
  };

  const handleOnRecordClick = (sfId, type) => {
    const value = sfId === selectedUniqueId?.[type] ? { [type]: null } : { [type]: sfId };
    const ids = selectedUniqueId === null ? value : { ...selectedUniqueId, ...value };
    setSelectedUniqueId(ids);
  };

  const renderMetrics = (metricsDetails) => {
    const keys = Object.keys(metricsDetails);
    return (
      <MetricsUnorderedList>
        {keys.map((key) => {
          return (
            <MetricsListContainer key={key}>
              <MetricsTitle bold={metricsDetails[key]?.bold ? 'bold' : ''}>
                {metricsDetails[key]?.title}
              </MetricsTitle>
              <MetricsValue>{metricsDetails[key]?.value}</MetricsValue>
            </MetricsListContainer>
          );
        })}
      </MetricsUnorderedList>
    );
  };

  const renderRecords = (records, type) => {
    if (!records || records?.length === 0)
      return (
        <TimeoutComponent>
          <Header h2>No Results.</Header>
          <Header h4>No records returned.</Header>
        </TimeoutComponent>
      );

    return records?.map((record, index) => {
      const { uniqueId, name, highlightRecord } = record;
      return (
        <>
          <Record
            onClick={() => handleOnRecordClick(uniqueId, type)}
            isRecordSelected={uniqueId === selectedUniqueId?.[type]}
            onMouseEnter={() => setHoveringDeal(uniqueId)}
            onMouseLeave={() => setHoveringDeal(null)}
            highlightRecord={highlightRecord}
          >
            <div>
              <HouseIconStyle /> {name}
            </div>
            {uniqueId === selectedUniqueId?.[type] ? <UpArrowIconStyle /> : <DownArrowIconStyle />}
          </Record>
          {uniqueId === hoveringDeal && <Tooltip text="Click to View Details" />}
          {record && type && renderRecordDetails(record, type)}
          {index + 1 !== records?.length && <HorizontalDivider />}
        </>
      );
    });
  };

  const renderRecordDetails = (details, type) => {
    const { uniqueId } = details;
    const Form = recordDetailsForm[type];
    if (!Form) return null;
    return (
      <RecordDetailsContainer openPanel={uniqueId === selectedUniqueId?.[type]}>
        <Form record={details} />
      </RecordDetailsContainer>
    );
  };

  let column = 0;
  let row = 0;

  const renderBanner = (row, title) => {
    return (
      <Banner row={row + 1}>
        <Header h2>{title}</Header>
      </Banner>
    );
  };

  return (
    <>
      {Object.keys(details).length > 0 ? (
        <CalendarRowsContainer>
          {Object.keys(details).map((key, sectionIndex) => {
            const reports = details?.[key]?.reports;
            return (
              <>
                {details?.[key]?.banner && renderBanner(row, details?.[key]?.banner)}
                {Object.keys(reports).map((reportKey, index) => {
                  const reportDetails = reports?.[reportKey];
                  column = (index + 1) % 2 === 0 ? 2 : 1;
                  if (sectionIndex > 0) {
                    if (index === 0) {
                      row += 2;
                    } else {
                      row = (index + 1) % 2 === 0 ? row : row + 1;
                    }
                  } else {
                    row = parseInt(index / 2 + 1, 10);
                  }

                  return (
                    <DashboardCard key={reportKey} column={column} row={row}>
                      <HeaderContainer>
                        <DashboardHeader>
                          <BarChartIconStyle />
                          {reportDetails?.title?.replace('REGION', region)}
                        </DashboardHeader>
                      </HeaderContainer>
                      <ReportMetricsContainer>
                        {details && renderMetrics(reportDetails?.metrics, reportKey)}
                      </ReportMetricsContainer>
                      <ReportDetailsContainer>
                        {details && renderRecords(reportDetails?.records, reportKey)}
                      </ReportDetailsContainer>
                    </DashboardCard>
                  );
                })}
              </>
            );
          })}
        </CalendarRowsContainer>
      ) : (
        <TimeoutComponent>
          <Header h2>Sorry, there is nothing to display here</Header>
        </TimeoutComponent>
      )}
    </>
  );
};

Dashboard.propTypes = {
  details: PropTypes.shape(
    PropTypes.shape({
      title: PropTypes.string,
      metrics: PropTypes.shape({}),
      reports: PropTypes.arrayOf(PropTypes.shape({})),
    }),
  ),
  region: PropTypes.string,
};

export default Dashboard;
