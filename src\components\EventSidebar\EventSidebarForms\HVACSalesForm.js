import React, { Suspense, useState, useEffect, useRef } from 'react';
import { decodeEventType } from '@homeworksenergy/utility-service';

import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import moment from 'moment';

import useStartEndTimes from '@hooks/useStartEndTimes';

import { selectedEventState, isSchedulingViewAtom, schedulingTypeAtom } from '@recoil/eventSidebar';

import { allAgentsFormOptionsState } from '@recoil/agents';

import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  FormTextBox,
  FormInfoField,
  FormInfo,
  FormStartEndDateTimePickers,
  FormMultiselect,
} from '@components/global/Form';

import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import EventSidebarHeader, {
  HeaderLabel,
  HeaderTitle,
} from '@components/EventSidebar/EventSidebarHeader';

import { isAuthorized } from '@utils/AuthUtils';
import Pin from '@components/Calendar/Pin';
import Lock from '@components/Calendar/Lock';
import { DropDownMenu, LoadingIndicator } from '@components/global';
import { Payment } from '@components/Calendar/EventComponents';
import { UtilityManager, SalesforceManager } from '@utils/APIManager';
import { openNewTabWithUrl } from '@utils/functions';
import { leadVettedValues } from '@utils/businessLogic/hvacSalesBusinessLogic';

const IconContainer = styled.div`
  margin-right: 10px;
  margin-top: auto;
  height: 24px;
  & :hover {
    cursor: pointer;
  }
`;

const HVACSalesForm = ({ handleCancelClick, handleSaveClick }) => {
  const handleTimeChange = useStartEndTimes();
  const setIsSchedulingView = useSetRecoilState(isSchedulingViewAtom);
  const setSchedulingType = useSetRecoilState(schedulingTypeAtom);
  const [event, setEvent] = useRecoilState(selectedEventState);
  const agents = useRecoilValue(allAgentsFormOptionsState);
  const [loading, setLoading] = useState(false);
  const paymentRef = useRef();
  const {
    id,
    customerName,
    notes,
    numUnit,
    phoneNumber,
    email,
    oids: [oid],
    startTime,
    endTime,
    date,
    scheduledBy,
    scheduledDate,
    type,
    address,
    lock,
    startEndTimes,
    sfIds: {
      hvacVisitId,
      hvacVisitId2,
      hvacVisitId3,
      hvacVisitId4,
      opportunityId,
      opportunityId2,
      opportunityId3,
      opportunityId4,
    },
    sfIds,
    customerInterests,
    leadVetted,
  } = event;

  const isCreate = !id;
  const isManager = isAuthorized('Manager', 'HVAC-Sales');
  const createEventTypes = [{ key: 'Custom Block', value: '999999' }];
  useEffect(() => {
    if (!date || !isCreate || type) return;

    if (isCreate) setEvent({ ...event, type: createEventTypes[0].value });
  }, [date, createEventTypes, type, isCreate, setEvent, event]);

  const displayAddress = address?.displayAddress;

  const isSchedulerLevel = isAuthorized('Scheduler', 'All');
  const isUserHVACScheduler = isAuthorized('Scheduler', 'HVAC-Sales');
  const canPerformActions = isSchedulerLevel && !lock;

  const agent = agents.find((agent) => agent.value === oid);

  const agentName = agent?.key;

  const multiUnit = numUnit && Number(numUnit) > 1;

  const dropDownList = [];
  const list = [
    { text: 'SF Page', onClick: (unitNum) => onClickSalesId(unitNum) },
    {
      text: 'Doc Repo link',
      onClick: (unitNum) => onClickDocRepo(unitNum),
    },
    {
      text: 'HEA - Doc Repo link',
      onClick: (unitNum) => onClickHeaDocRepo(unitNum),
    },
  ];

  if (multiUnit)
    list.push({
      text: 'Payment',
      onClick: (unitNum) => paymentRef.current.handlePaymentButtonClick(unitNum, true),
    });

  list.forEach(({ text, onClick }) => {
    const childrens = [];
    if (multiUnit)
      for (let k = 0; k < Number(numUnit); k++) {
        childrens.push({
          text: `${text} (unit ${k + 1})`,
          onClick: () => onClick(k),
        });
      }
    dropDownList.push({
      text,
      onClick: multiUnit ? {} : () => onClick(0),
      childrens,
    });
  });

  const customerInterestsOptions = [
    { key: 'Combination Boiler', value: 'Combination Boiler' },
    { key: 'Forced Hot Water Boiler', value: 'Forced Hot Water Boiler' },
    { key: 'Steam Boiler', value: 'Steam Boiler' },
    { key: 'Furnace', value: 'Furnace' },
    { key: 'Ductless Mini-Splits', value: 'Ductless Mini-Splits' },
    { key: 'Central Air', value: 'Central Air' },
    { key: 'Heat Pump', value: 'Heat Pump' },
  ];

  const leadVettedOptions = leadVettedValues.map((leadVetted) => {
    return { key: leadVetted, value: leadVetted };
  });

  if (!type) return null;
  const { state, business, businessEvent: eventTypeName } = decodeEventType(type);
  const eventDepartment = business.replace(' ', '_');

  const onClickDocRepo = (unitNum) => {
    const unitNumText = unitNum === 0 ? '' : unitNum + 1;
    openDocRepoTab(`/doc-repo/${state}/${eventDepartment}/${sfIds[`opportunityId${unitNumText}`]}`);
  };

  const onClickHeaDocRepo = async (unitNum) => {
    const unitNumText = unitNum === 0 ? '' : unitNum + 1;
    const sfId = sfIds[`dealId${unitNumText}`];
    const {
      customerName,
      leadVendor,
      unitsInBuilding,
    } = await SalesforceManager.getHEAEventInfoWithDealIds([sfId]);
    UtilityManager.openDocRepo(sfId, customerName, leadVendor, parseInt(unitsInBuilding, 10));
  };

  const onClickSalesId = (unitNum) => {
    if (!isCreate) {
      const sfIdsArr = [
        opportunityId || hvacVisitId,
        opportunityId2 || hvacVisitId2,
        opportunityId3 || hvacVisitId3,
        opportunityId4 || hvacVisitId4,
      ];
      const sfObject = hvacVisitId ? 'HVAC_Visit__c' : 'Opportunity';
      UtilityManager.openSfPage(sfIdsArr[unitNum], sfObject);
    }
  };

  const openDocRepoTab = async (url) => {
    openNewTabWithUrl(`${url}/`);
  };

  const handleSchedulingActionClick = (action) => {
    setSchedulingType(action);
    setIsSchedulingView(true);
  };

  const handleFieldChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  const leftButtons = [];
  const rightButtons = [];

  if (canPerformActions) {
    leftButtons.push(<PrimaryButton onClick={() => handleSaveClick()}>Save</PrimaryButton>);
    if (!isCreate)
      leftButtons.push(
        <CancelButton onClick={() => handleCancelClick()}>Cancel Event</CancelButton>,
      );
    rightButtons.push(
      <PrimaryButton onClick={() => handleSchedulingActionClick('reschedule')}>
        Reschedule
      </PrimaryButton>,
      <PrimaryButton onClick={() => handleSchedulingActionClick('reassign')}>
        Reassign
      </PrimaryButton>,
    );
  }

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col size={2}>
              <HeaderLabel>Customer Name:</HeaderLabel>
              <HeaderTitle>{customerName}</HeaderTitle>
            </Col>
            <Col size={1} right>
              <HeaderLabel>HCS:</HeaderLabel>
              <HeaderTitle>{agentName}</HeaderTitle>
            </Col>
            <Col size={0} left>
              <Row>
                <IconContainer>
                  {canPerformActions && <Pin event={event} onSidebar />}
                </IconContainer>
                <IconContainer>{isUserHVACScheduler && <Lock event={event} />}</IconContainer>
                <DropDownMenu listItems={dropDownList} />
              </Row>
              <Row>
                <Payment
                  ref={paymentRef}
                  showButton={!multiUnit}
                  onClick={() => paymentRef.current.handlePaymentButtonClick()}
                />
              </Row>
            </Col>
          </Row>
        </EventSidebarHeader>
        <EventSidebarBody>
          <Row>
            <Col size={1}>
              {numUnit && Number(numUnit) && (
                <SfIdInputs sfObjectType="account" setLoading={setLoading} />
              )}
            </Col>
            <Col size={1}>
              {numUnit && Number(numUnit) && (
                <SfIdInputs sfObjectType="opportunity" setLoading={setLoading} readOnly />
              )}
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput readOnly name="date" value={date} title="date" placeholder="" />
            </Col>
            <Col size={1}>
              {isCreate ? (
                <FormSelect
                  required
                  title="Visit Type"
                  placeholder="Select Visit Type"
                  name="type"
                  value={type}
                  onChange={handleFieldChange}
                  options={createEventTypes}
                />
              ) : (
                <FormInput
                  readOnly
                  name="eventTypeName"
                  value={eventTypeName}
                  title="Visit Type"
                  placeholder=""
                />
              )}
            </Col>
          </Row>
          <Row>
            {isCreate ? (
              <Col size={2}>
                <FormStartEndDateTimePickers
                  key="startEndTime"
                  name="startEndTime"
                  displayDay={false}
                  startEndTimes={startEndTimes}
                  onChange={handleTimeChange}
                  dateFormat="h:mm aa"
                  allowDateSelect={false}
                  direction="row"
                />
              </Col>
            ) : (
              <>
                <Col size={1}>
                  <FormInput
                    readOnly
                    name="startTime"
                    value={moment(startTime, 'HH:mm:ss').format('h:mm A')}
                    title="Start Time"
                    placeholder=""
                  />
                </Col>
                <Col size={1}>
                  <FormInput
                    readOnly
                    name="endTime"
                    value={moment(endTime, 'HH:mm:ss').format('h:mm A')}
                    title="End Time"
                    placeholder=""
                  />
                </Col>
              </>
            )}
          </Row>
          <Row>
            {customerInterests.length > 0 && (
              <Col>
                <FormMultiselect
                  required
                  title="Customer Interest(s)"
                  name="customerInterests"
                  options={customerInterestsOptions}
                  onChange={handleFieldChange}
                  value={customerInterests}
                />
              </Col>
            )}
            <Col>
              <FormInput
                readOnly
                name="address"
                value={displayAddress || ''}
                title="location"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormSelect
                required
                name="leadVetted"
                value={leadVetted}
                title="Lead Vetted"
                readOnly={!isManager}
                placeholder=""
                onChange={handleFieldChange}
                options={leadVettedOptions}
              />
            </Col>
            <Col>
              <FormTextBox
                readOnly={!isUserHVACScheduler}
                name="notes.fieldNotes"
                value={notes.fieldNotes}
                title="Notes"
                placeholder=""
                onChange={handleFieldChange}
              />
            </Col>
          </Row>

          <Row>
            <Col size={1}>
              <FormInput
                readOnly
                name="phoneNumber"
                value={phoneNumber}
                title="Customer Phone Number"
                placeholder=""
              />
            </Col>
            <Col size={1}>
              <FormInput
                readOnly
                name="email"
                value={email}
                title="Customer Email"
                placeholder=""
              />
            </Col>
          </Row>
          {isSchedulerLevel && !isCreate && (
            <Row>
              <Col>
                <FormInfo>
                  <FormInfoField title="Scheduled By :" body={scheduledBy} />
                  <FormInfoField
                    title="Scheduled On :"
                    body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
                  />
                </FormInfo>
              </Col>
            </Row>
          )}
        </EventSidebarBody>
        <EventSidebarFooter leftButtons={leftButtons} rightButtons={rightButtons} />
      </Suspense>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

HVACSalesForm.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
  history: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
};

export default withRouter(HVACSalesForm);
