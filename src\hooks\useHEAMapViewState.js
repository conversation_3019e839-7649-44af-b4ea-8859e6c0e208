import { useCallback, useEffect } from 'react';
import { useSetRecoilState, useRecoilValue } from 'recoil';
import moment from 'moment';
import { throwError, startLoading, stopLoading, updateAllEvents } from '@utils/EventEmitter';
import { handleApiCall } from '@utils/APIManager/utils/AxiosConfig';
import { extractAppointmentsFromAPI } from '@components/Calendar/MapComponents/HEAMapViewUtils';
import {
  heaSelectedDateAtom,
  heaAppointmentsAtom,
  heaAgentsAtom,
  heaActiveFiltersAtom,
  heaSelectedHesOidAtom,
  heaLoadingAtom,
} from '@recoil/heaMapView/heaMapViewAtoms';

const useHEAMapViewState = ({ startDate, endDate, type }) => {
  const setSelectedDate = useSetRecoilState(heaSelectedDateAtom);
  const setAppointments = useSetRecoilState(heaAppointmentsAtom);
  const setAgents = useSetRecoilState(heaAgentsAtom);
  const setActiveFilters = useSetRecoilState(heaActiveFiltersAtom);
  const setSelectedHesOid = useSetRecoilState(heaSelectedHesOidAtom);
  const setLoading = useSetRecoilState(heaLoadingAtom);

  // state getters
  const selectedDate = useRecoilValue(heaSelectedDateAtom);
  const appointments = useRecoilValue(heaAppointmentsAtom);
  const agents = useRecoilValue(heaAgentsAtom);
  const activeFilters = useRecoilValue(heaActiveFiltersAtom);
  const selectedHesOid = useRecoilValue(heaSelectedHesOidAtom);
  const loading = useRecoilValue(heaLoadingAtom);

  // Load appointments from API
  const loadAppointments = useCallback(async () => {
    try {
      setLoading(true);
      startLoading('Loading appointments...');

      // Call API directly to get both rows and allEvents
      const startDateStr = startDate.format('YYYY-MM-DD');
      const endDateStr = endDate.format('YYYY-MM-DD');
      const params = { startDate: startDateStr, endDate: endDateStr, type, oids: null };

      const response = await handleApiCall({
        url: '/api/events/get',
        method: 'post',
        params,
        loadingMessage: 'Loading appointments...',
      });

      if (!response || !response.allEvents || Object.keys(response.allEvents).length === 0) {
        setAppointments([]);
        setAgents([]);
        setLoading(false);
        stopLoading();
        return;
      }

      // Update global allEvents state
      updateAllEvents(response.allEvents);

      // Extract appointments and agents from the API response
      const {
        appointments: appointmentsData,
        hesAgents: hesAgentsList,
      } = extractAppointmentsFromAPI(response.allEvents);

      // Filter appointments to only include those for the selected date
      const selectedDateStr = startDate.format('YYYY-MM-DD');
      const filteredAppointments = appointmentsData.filter((apt) => {
        const aptDate = moment(
          apt.date,
          ['YYYY-MM-DD', 'MM/DD/YYYY', 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss.SSSZ'],
          true,
        ).format('YYYY-MM-DD');
        return aptDate === selectedDateStr;
      });

      // Recalculate appointment counts based on filtered appointments
      const hesAgentsMap = new Map();
      hesAgentsList.forEach((agent) => {
        hesAgentsMap.set(agent.oid, { ...agent, appointmentCount: 0 });
      });

      filteredAppointments.forEach((apt) => {
        if (hesAgentsMap.has(apt.hesOid)) {
          hesAgentsMap.get(apt.hesOid).appointmentCount++;
        }
      });

      // Filter out agents with 0 appointments
      const updatedHesAgents = Array.from(hesAgentsMap.values()).filter(
        (agent) => agent.appointmentCount > 0,
      );

      setAppointments(filteredAppointments);
      setAgents(updatedHesAgents);

      // Initialize active filters with all agents that have appointments
      setActiveFilters(new Set(updatedHesAgents.map((agent) => agent.oid)));
    } catch (error) {
      throwError(`Error loading appointments: ${error.message}`);
    } finally {
      setLoading(false);
      stopLoading();
    }
  }, [startDate, endDate, type, setAppointments, setAgents, setActiveFilters, setLoading]);

  // Load appointments on mount
  useEffect(() => {
    loadAppointments();
  }, [loadAppointments]);

  // Clear selected HES when date changes
  useEffect(() => {
    setSelectedHesOid(null);
  }, [selectedDate, setSelectedHesOid]);

  // Toggle agent filter
  const toggleAgentFilter = useCallback(
    (hesOid) => {
      setActiveFilters((prev) => {
        const newFilters = new Set(prev);
        if (newFilters.has(hesOid)) {
          newFilters.delete(hesOid);
        } else {
          newFilters.add(hesOid);
        }
        return newFilters;
      });
      setSelectedHesOid(hesOid);
    },
    [setActiveFilters, setSelectedHesOid],
  );

  // Select all agents
  const selectAllAgents = useCallback(() => {
    setSelectedHesOid(null);
    setActiveFilters(new Set(agents.map((agent) => agent.oid)));
  }, [agents, setActiveFilters, setSelectedHesOid]);

  // Deselect all agents
  const deselectAllAgents = useCallback(() => {
    setSelectedHesOid(null);
    setActiveFilters(new Set());
  }, [setActiveFilters, setSelectedHesOid]);

  // Reset filters
  const resetFilters = useCallback(() => {
    setSelectedHesOid(null);
    setActiveFilters(new Set());
  }, [setActiveFilters, setSelectedHesOid]);

  return {
    // State
    selectedDate,
    appointments,
    agents,
    activeFilters,
    selectedHesOid,
    loading,

    // Actions
    setSelectedDate,
    loadAppointments,
    toggleAgentFilter,
    selectAllAgents,
    deselectAllAgents,
    resetFilters,
  };
};

export default useHEAMapViewState;
