import React from 'react';
import PropTypes from 'prop-types';
import { useTheme } from 'styled-components';
import { useRecoilValue } from 'recoil';
import { calendarTypeAtom } from '@recoil/app';
import { decodeEventType } from '@homeworksenergy/utility-service';

import { renderStartAndEndTimes, chopString, getCityAndZipcodeFromAddress } from '@utils/functions';
import Event from './Event';

const CustomBlockEvent = ({
  event = {
    eventName: 'Custom Block',
    shadow: false,
    lock: false,
    patternName: '',
    address: {},
  },
  ...otherProps
}) => {
  const theme = useTheme();
  const { eventName, shadow, notes, lock, patternName, address } = event;

  // TODO: remove when we have the rest of the hvac support built out
  const calendarType = useRecoilValue(calendarTypeAtom);
  let isInsulation = false;
  if (calendarType) {
    const { business: department } = decodeEventType(calendarType);
    isInsulation = department === 'Insulation';
  }

  const cityAndZipcode = getCityAndZipcodeFromAddress(address);

  return (
    <Event
      event={event}
      backgroundColor={shadow ? theme.colors.shadowBlock : theme.colors.customBlock}
      tooltip={notes.officeNotes}
      headerText={eventName ? chopString(eventName, 30) : ''}
      bodyHeader={patternName ? chopString(patternName, 30) : ''}
      bodyText={[renderStartAndEndTimes(event), cityAndZipcode || '']}
      shadow={shadow}
      lockable={isInsulation}
      pinnable={isInsulation}
      lock={lock}
      {...otherProps}
    />
  );
};

CustomBlockEvent.propTypes = {
  event: PropTypes.shape({
    id: PropTypes.string.isRequired,
    eventName: PropTypes.string,
    eventTypeName: PropTypes.string.isRequired,
    address: PropTypes.shape({}),
    startTime: PropTypes.string,
    endTime: PropTypes.string,
    type: PropTypes.string,
    shadow: PropTypes.bool,
    lock: PropTypes.bool,
    notes: PropTypes.shape({
      officeNotes: PropTypes.string,
    }),
    patternName: PropTypes.string,
  }),
};

export default CustomBlockEvent;
