import React from 'react';
import { useRecoilValue } from 'recoil';
import styled from 'styled-components';

import { selectedAgentAtom } from '@recoil/admin/agents';
import EditAvailabilityFields from '@components/admin/agentAdmin/EditAvailabilityFields';

const EditAgentAvailabilityFormContainer = styled.div`
  margin: 1.5em 1.5em 4.5em 1.5em;
`;

const EditAgentAvailabilityForm = () => {
  const { oid, departmentName } = useRecoilValue(selectedAgentAtom);

  return (
    <EditAgentAvailabilityFormContainer>
      <EditAvailabilityFields oid={oid} departmentName={departmentName} />
    </EditAgentAvailabilityFormContainer>
  );
};

export default EditAgentAvailabilityForm;
