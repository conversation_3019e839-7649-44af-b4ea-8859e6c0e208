import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useRecoilValue } from 'recoil';
import { heaAppointmentsAtom, heaSelectedDateAtom } from '@recoil/heaMapView/heaMapViewAtoms';
import {
  ScheduleCardContainer,
  ScheduleCardTitle,
  RouteStopsContainer,
  RouteStop,
  StopNumber,
  StopDetails,
  StopCustomerName,
  StopAddress,
  StopTime,
  StopEventType,
} from '../HEAMapViewStyles';

/** Displays route stops for a selected HES agent */
const ScheduleCard = ({ agent, selectedHesOid }) => {
  const appointments = useRecoilValue(heaAppointmentsAtom);
  const selectedDate = useRecoilValue(heaSelectedDateAtom);

  // get appointments for the selected HES and date
  const hesAppointments = useMemo(() => {
    // Always format selectedDate - it should always be provided
    const selectedDateStr = moment(selectedDate).format('YYYY-MM-DD');
    const hesAppts = appointments.filter((apt) => {
      // Filter by HES OID
      if (apt.hesOid !== selectedHesOid) return false;

      // Filter by datealways apply date filter
      const aptDate = moment(
        apt.date,
        ['YYYY-MM-DD', 'MM/DD/YYYY', 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss.SSSZ'],
        true,
      ).format('YYYY-MM-DD');
      return aptDate === selectedDateStr;
    });
    return hesAppts.sort((a, b) =>
      moment(a.startTime, 'HH:mm:ss').diff(moment(b.startTime, 'HH:mm:ss')),
    );
  }, [appointments, selectedHesOid, selectedDate]);

  // Format time range for display
  const formatTimeRange = (startTime, endTime) => {
    return `${moment(startTime, 'HH:mm:ss').format('h:mm A')} - ${moment(
      endTime,
      'HH:mm:ss',
    ).format('h:mm A')}`;
  };

  return (
    <ScheduleCardContainer>
      <ScheduleCardTitle>Route Stops:</ScheduleCardTitle>
      <RouteStopsContainer>
        {hesAppointments.map((appointment, index) => (
          <RouteStop key={appointment.id}>
            <StopNumber color={agent.color}>{index + 1}</StopNumber>
            <StopDetails>
              <StopCustomerName>{appointment.customerName}</StopCustomerName>
              <StopAddress>
                {appointment.address?.displayAddress || appointment.address?.street || 'No address'}
              </StopAddress>
              <StopTime>{formatTimeRange(appointment.startTime, appointment.endTime)}</StopTime>
              <StopEventType>{appointment.eventTypeName || 'Visit'}</StopEventType>
            </StopDetails>
          </RouteStop>
        ))}
      </RouteStopsContainer>
    </ScheduleCardContainer>
  );
};

ScheduleCard.propTypes = {
  agent: PropTypes.shape({
    color: PropTypes.string,
  }).isRequired,
  selectedHesOid: PropTypes.string.isRequired,
};

export default ScheduleCard;
