import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import Modal from 'react-bootstrap/Modal';
import swal from 'sweetalert2/dist/sweetalert2';
import { PrimaryButton } from '@components/global/Buttons';
import { EventsManager } from '@utils/APIManager';

const StyledContainer = styled.div``;
const StyledPre = styled.pre``;

const ErrorModal = (props) => {
  const { event, showModal, closeModal } = props;

  const cancelEvent = async () => {
    const {
      event: { id },
    } = props;
    const { value: confirmed } = await swal.fire({
      title: 'Are you sure you want to cancel this appointment?',
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirmed) return;
    await EventsManager.cancelEvent({ id });
  };

  const handleSelect = (selection) => {
    if (selection === 'CANCEL') {
      cancelEvent();
    }
  };

  return (
    <Modal className="event-details-modal" show={showModal} onHide={closeModal}>
      <Modal.Header closeButton>
        <Modal.Title>Error event details</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <StyledContainer>
          <StyledPre>{JSON.stringify(event, null, 2)}</StyledPre>
        </StyledContainer>
      </Modal.Body>
      <Modal.Footer>
        <PrimaryButton text="Cancel Event" onClick={handleSelect} />
      </Modal.Footer>
    </Modal>
  );
};

ErrorModal.propTypes = {
  closeModal: PropTypes.func.isRequired,
  event: PropTypes.shape({ id: PropTypes.number }).isRequired,
  showModal: PropTypes.bool.isRequired,
};

export default ErrorModal;
