import { selector } from 'recoil';
import { decodeEventType } from '@homeworksenergy/utility-service';
import selectedEventState from '@recoil/eventSidebar/selectedEventState';
import agentsSelectorFamily from '@recoil/agents/agentsSelectorFamily';
import { calendarTypeAtom } from '@recoil/app';

// TODO: this should also only grab agents that are open on that day
const availableAgentsSelector = selector({
  key: 'availableAgents',
  get: ({ get }) => {
    const calendarType = get(calendarTypeAtom);
    const selectedEvent = get(selectedEventState);
    const { attributes, type } = selectedEvent;

    const searchType = type || calendarType;

    if (!searchType) return []; // Should this actually just throw an error? When would this happen?
    const { state, business: department } = decodeEventType(searchType);

    // For custom blocks, grab all agents on the calendar
    if (department === 'N/A') {
      const { state, business: department } = decodeEventType(calendarType);
      const allAgents = get(agentsSelectorFamily({ [state]: [department] }));
      return allAgents;
    }

    const allAgents = get(agentsSelectorFamily({ [state]: [department] }));

    // Filter down to just the agents that have permission to perform all of the
    // selected attributes and eventTypes
    const filteredAgents = allAgents
      .filter((agent) => attributes.every((attribute) => agent.attributes.includes(attribute)))
      .filter((agent) => agent.eventTypes.includes(type));

    return filteredAgents;
  },
});

export default availableAgentsSelector;
