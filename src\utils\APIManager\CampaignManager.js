import { handleApiCall } from '@utils/APIManager/utils/AxiosConfig';
import { throwError } from '@utils/EventEmitter';

const validateCampaignID = async (campaignId) => {
  try {
    const host = window.location.origin;
    const env = host === 'https://sch.homeworksenergy.com' ? 'production' : 'development';
    const url = `https://s05hc5nuyd.execute-api.us-east-1.amazonaws.com/${env}/campaign/${campaignId}`;
    const response = await handleApiCall({
      url,
      method: 'get',
      loadingMessage: 'Validating Campaign ID...',
    });
    if (!response) return null;

    return response;
  } catch (err) {
    throwError(err);
    return false;
  }
};

export default {
  validateCampaignID,
};
