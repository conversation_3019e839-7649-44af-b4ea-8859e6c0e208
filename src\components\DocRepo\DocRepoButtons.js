import React from 'react';
import styled, { css } from 'styled-components';
import Swal from 'sweetalert2/dist/sweetalert2';

import { SecondaryButton } from '@components/global/Buttons';
import { Col } from '@components/global/Form';
import { startLoading, stopLoading, throwError } from '@utils/EventEmitter';
import { DocRepoManager } from '@utils/APIManager';
import { hasRole } from '@utils/AuthUtils';

import { EyeFill } from '@styled-icons/bootstrap/EyeFill';
import { EyeSlashFill } from '@styled-icons/bootstrap/EyeSlashFill';
import { Upload } from '@styled-icons/bootstrap/Upload';
import { Download } from '@styled-icons/bootstrap/Download';
import { ViewStacked } from '@styled-icons/bootstrap/ViewStacked';
import { Trash } from '@styled-icons/bootstrap/Trash';
import { Send } from '@styled-icons/boxicons-solid/Send';
import { FileEarmarkPlusFill } from '@styled-icons/bootstrap/FileEarmarkPlusFill';
import { Plus } from '@styled-icons/bootstrap/Plus';
import { DropDownMenu } from '@components/global';
import Clickable from '@components/global/Clickable';
import { getCapHeaRequiredDocs } from '@utils/getRequiredDocs';
import { documents as capHeaDocuments } from '@utils/businessLogic/capHeaDocRepoBusinessLogic';
import { documents as capHvacDocuments } from '@utils/businessLogic/capHvacSalesDocRepoBusinessLogic';

const documents = { ...capHeaDocuments, ...capHvacDocuments };

const {
  getDocsFromS3,
  downloadFileFromS3,
  deleteFileFromS3,
  getSignedUrl,
  uploadFileToS3,
} = DocRepoManager;

let selectedDocument = '';

const EyeFillIconStyled = styled(EyeFill)`
  cursor: pointer;
  &:hover {
    background-color: initial;
    background-position: 0 0;
    color: black;
  }
  height: 30px;
  color: ${({ theme }) => theme.colors.eventA};
`;
const EyeSlashFillIconStyled = styled(EyeSlashFill)`
  height: 30px;
  color: ${({ theme }) => theme.colors.red};
`;

const ButtonIconStyle = css`
  height: 20px;
  margin-right: 10px;
  &:hover {
    color: black;
    cursor: pointer;
  }
`;

const UploadIconStyled = styled(Upload)`
  ${ButtonIconStyle}
`;
const DownloadIconStyled = styled(Download)`
  ${ButtonIconStyle}
  height:26px;
`;
const ViewIconStyled = styled(ViewStacked)`
  ${ButtonIconStyle}
`;

const FooterButtonStyle = css`
  padding: 10px;
  border-radius: 8px;
  font-size: 16px;
  &:hover {
    color: black;
    cursor: pointer;
  }
`;

const DropDownButtonStyle = css`
  position: relative;
  overflow: hidden;
  display: inline-block;
  display: flex;
  font-size: 20px;
`;

const UploadButtonStyle = styled.button`
  border: 2px solid ${({ theme }) => theme.secondary[100]};
  background-color: ${({ theme }) => theme.colors.eventGreen};
  color: ${({ theme }) => theme.secondary[100]};
  margin: 10px 20px 10px 0;
  position: relative;
  overflow: hidden;
  display: inline-block;
  ${FooterButtonStyle}
`;
const DownloadButtonStyle = styled(SecondaryButton)`
  ${FooterButtonStyle}
`;
const ViewAllButtonStyle = styled(SecondaryButton)`
${FooterButtonStyle}
background-color: ${({ theme }) => theme.colors.eventA};
color: white;
`;
const UploadFileInputStyle = styled.input`
  width: -webkit-fill-available;
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  ${({ theme }) => theme.screenSize.down(theme.breakpoints.laptopM)} {
    height: 40px;
  }
`;

const ActionContainer = styled(Col)`
  display: flex;
  flex-direction: row-reverse;
`;

const DropDownButtonContainer = styled.div`
  ${DropDownButtonStyle}
`;
const UploadButtonContainer = styled.div`
  ${DropDownButtonStyle}
  padding: 5px;
  &:hover {
    cursor: pointer;
    background-color: ${({ theme }) => theme.secondary[600]};
    border-radius: 5%;
    color: ${({ theme }) => theme.secondary[100]};
  }
`;
const DeleteIconStyled = styled(Trash)`
  height: 20px;
  margin-right: 10px;
`;

const EmailIconStyled = styled(Send)`
  height: 20px;
  margin-right: 10px;
`;

const MergeFileIconStyled = styled(FileEarmarkPlusFill)`
  height: 20px;
  margin-right: 10px;
`;

const ActionsIconStyled = styled(Plus)`
  height: 40px;
  margin-right: 10px;
`;

const SensitiveDocActionContainer = styled.div`
  display: flex;
  justify-content: space-between;
  width: 20%;
`;

let dropDownList = [
  {
    text: (
      <DropDownButtonContainer>
        <DownloadIconStyled />
        Download
      </DropDownButtonContainer>
    ),
    onClick: (document) => {
      downloadFile(document);
    },
    key: 'downloadDoc',
  },
  {
    text: (
      <DropDownButtonContainer>
        <UploadIconStyled />
        Reupload
      </DropDownButtonContainer>
    ),
    onClick: (doc) => {
      selectedDocument = doc.docName;
      document.getElementById('fileInput').click();
    },
    key: 'reUploadDoc',
  },
];
const isWxCrew =
  hasRole('Agent', 'Crew') ||
  hasRole('External Scheduler', 'Crew') ||
  hasRole('Basic', 'Crew') ||
  hasRole('Scheduler', 'Crew');

if (!isWxCrew) {
  dropDownList = [
    ...dropDownList,
    {
      text: (
        <DropDownButtonContainer>
          <DeleteIconStyled />
          Delete
        </DropDownButtonContainer>
      ),
      onClick: (document) => {
        deleteFile(document);
      },
    },
  ];
}

const fetchDocumentsFromS3 = async (
  { state, department, uniqueId, dealId },
  setDocuments,
  resulting,
  setResulting,
  resetResulting,
) => {
  try {
    const s3ParamsObj = {
      bucketKeys: `${state}/${department}/${uniqueId}${dealId ? `/${dealId}` : ''}`,
      department,
    };
    const response = await getDocsFromS3(s3ParamsObj);
    const { alreadyUploadedDocs, resulting: docRepoResulting, finalDocuments } = response;
    let { docs } = response;
    if (department === 'Partners') {
      docs = docs.map((doc) => {
        return { ...doc, checked: false };
      });
      setDocuments(docs);
    } else {
      const formatDocRepoResulting = Object.keys(docRepoResulting)?.filter((result) => {
        return !['appliancePerformed'].includes(result);
      });
      if (formatDocRepoResulting.length === 0) resetResulting();
      setResulting(docRepoResulting);
      const requiredDocs = getCapHeaRequiredDocs(docRepoResulting, department);
      setDocumentsState(requiredDocs, alreadyUploadedDocs, finalDocuments, setDocuments);
    }
    return docRepoResulting || {};
  } catch (error) {
    stopLoading();
    throwError({
      message: 'Failed to get document. Error: Network Error.',
    });
  }
  return {};
};

const setDocumentsState = (requiredDocs, alreadyUploadedDocs, finalDocuments, setDocuments) => {
  const formattedAlreadyUploadDocs = alreadyUploadedDocs
    .map((doc) => {
      if (doc === 'resulting') return null;
      const docArr = doc.split('.');
      docArr.pop();
      return docArr.join('.');
    })
    .filter((doc) => doc);
  let documentsDetail = {};
  [...requiredDocs, ...formattedAlreadyUploadDocs].forEach((doc) => {
    const documentDetail = documents[doc];
    let fileName = null;
    const [alreadyUploadDocName] = alreadyUploadedDocs.filter((uploadDoc) => {
      const uploadDocNameArr = uploadDoc.split('.');
      uploadDocNameArr.splice(-1);
      if (uploadDocNameArr.join('.') === doc) {
        fileName = uploadDoc;
        return true;
      }
      return false;
    });
    const uploadDocDetails = finalDocuments?.[alreadyUploadDocName];
    const obj = {
      name: documentDetail ? `${documentDetail?.fullName} (${doc})` : doc,
      fileName: fileName || doc,
      fileType: alreadyUploadDocName?.split('.')?.pop() || '',
      checked: false,
      uploadedBy: uploadDocDetails ? uploadDocDetails.author : '',
      uploadDate: uploadDocDetails ? uploadDocDetails.docUploadedTimeStamp : '',
      uploaded: !!uploadDocDetails,
    };
    documentsDetail = { ...documentsDetail, [doc]: obj };
  });
  setDocuments(documentsDetail);
};

const isDuplicateUpload = (allDocumentDetails, filename) => {
  let docType = null;
  const filenameSplit = filename.split('.');
  const fileExtension = filenameSplit[filenameSplit.length - 1].toLowerCase();
  const allDocumentDetailsKeys = Object.keys(allDocumentDetails);
  allDocumentDetailsKeys.sort((a, b) => {
    return b.length - a.length;
  });
  allDocumentDetailsKeys.forEach((docKey) => {
    if (
      filename !== `${docKey}.${fileExtension}` &&
      filename.includes(docKey) &&
      documents[docKey]?.fileType.includes(fileExtension)
    )
      docType = docKey;
  });
  return docType ? `${docType}.${fileExtension}` : null;
};

const handleUpload = async (
  event,
  { state, department, uniqueId, dealId, document },
  setDocuments,
  resulting,
  setResulting,
  checkDuplicate,
) => {
  try {
    const { target } = event;
    const files = Array.from(event.target.files);
    const filesThatFailedToUpload = [];
    if (selectedDocument.length > 0 && files.length > 0 && selectedDocument !== files[0].name) {
      throwError({
        message: 'The selected file name does not match with the file you want to replace.',
      });
      selectedDocument = '';
      target.value = null;
      return false;
    }
    // Loops through each file, checks if it is a required document, then upload.
    startLoading('Uploading Files...');
    /* eslint-disable no-await-in-loop */
    for (let k = 0; k < files.length; k++) {
      const file = files[k];
      let formattedFileName = file.name;
      if (checkDuplicate) {
        const duplicateFile = isDuplicateUpload(checkDuplicate.documents, files[k].name);
        if (duplicateFile) {
          stopLoading();
          const { value: confirmed } = await Swal.fire({
            icon: 'warning',
            title: `File '${files[k].name}'contains a valid acronym i.e ('${duplicateFile}')`,
            text: `To upload file under valid acronmy i.e ('${duplicateFile}') please click on Yes or else the file name will be uploaded as ${files[k].name}.`,
            confirmButtonText: 'Yes',
            showCancelButton: true,
            cancelButtonText: 'No',
          });
          if (confirmed) formattedFileName = duplicateFile;
        }
      }
      const urlParamsObj = {
        state,
        department,
        uniqueId,
      };
      const fileType = file?.name?.split('.')?.[1];
      const { fileName } = document || {};
      if (document && !documents[fileName].fileType.includes(fileType)) {
        throwError({
          message: `The file type for ${fileName} should be one of these types :\n "${documents[
            fileName
          ].fileType.join(',')}"`,
        });
        target.value = null;
        return false;
      }

      const data = new FormData();
      const fileNameWithExtention = document ? `${fileName}.${fileType}` : formattedFileName;
      data.append('file', file, fileNameWithExtention);

      // eslint-disable-next-line no-await-in-loop
      const uploadFile = await uploadFileToS3(data, urlParamsObj);
      if (!uploadFile) {
        filesThatFailedToUpload.push(file);
      }
      selectedDocument = '';
    }
    /* eslint-enable no-await-in-loop */
    stopLoading();
    if (filesThatFailedToUpload.length > 0) {
      throwError({
        message: 'Looks like files failed to upload. Please see below to see which files failed.',
        params: `${filesThatFailedToUpload.join(',')}`,
      });
    }
    target.value = null;
    fetchDocumentsFromS3(
      { state, department, uniqueId, dealId },
      setDocuments,
      resulting,
      setResulting,
    );
  } catch (error) {
    stopLoading();
    throwError({
      message: 'Looks like files failed to upload. Error: Network Error.',
    });
  }
  return true;
};

// Opens document in new tab
const openFile = async (urlParamsObj) => {
  try {
    const getUrl = await getSignedUrl(urlParamsObj);
    if (getUrl) window.open(getUrl, '_blank');
  } catch (error) {
    throwError({
      message: 'Failed to open file. Error: Network Error',
    });
  }
};

const downloadFile = async ({ docName, state, department, uniqueId }) => {
  const params = {
    doc: docName,
    state,
    department,
    uniqueId,
  };
  return downloadFileFromS3(params);
};

const deleteFile = async ({
  docName,
  state,
  department,
  uniqueId,
  dealId,
  setDocuments,
  resulting,
  setResulting,
}) => {
  const params = {
    doc: docName,
    state,
    department,
    uniqueId,
    dealId,
  };
  try {
    const success = await deleteFileFromS3(params);
    if (success) {
      // * The document deleted will be passed here
      await fetchDocumentsFromS3(
        { docName, state, department, uniqueId, dealId },
        setDocuments,
        resulting,
        setResulting,
      );
    }
  } catch (error) {
    stopLoading();
    throwError({
      message: 'Failed to delete file. Error: Network Error.',
    });
  }
};

const sensitiveDocAction = (uploaded, urlParamsObj) => {
  let obj = { ...urlParamsObj };
  if (['PWB', 'PICS', 'MOLD'].includes(urlParamsObj.fileName))
    obj = { ...obj, uniqueId: urlParamsObj.dealId };
  return uploaded ? (
    <SensitiveDocActionContainer>
      <DownloadIconStyled onClick={() => downloadFile(obj)} />
      <EyeFillIconStyled onClick={() => openFile(obj)} />
    </SensitiveDocActionContainer>
  ) : (
    <EyeSlashFillIconStyled />
  );
};

const renderDocumentActionContainer = (
  { document, state, department, uniqueId, dealId },
  setDocuments,
  resulting,
  setResulting,
) => {
  const isPartnerDoc = Object.prototype.hasOwnProperty.call(document, 'sensitive');
  const listItems = dropDownList.map((list) => {
    return {
      ...list,
      value: {
        docName: document.fileName,
        state,
        department,
        uniqueId,
        dealId,
        setDocuments,
        resulting,
        setResulting,
      },
    };
  });

  if (isPartnerDoc)
    return (
      <ActionContainer size={2} right>
        {!document.sensitive ? (
          <DropDownMenu listItems={listItems} />
        ) : (
          sensitiveDocAction(document.uploaded, {
            docName: `${document.fileName}.${document.type}`,
            fileName: document.fileName,
            state,
            department,
            uniqueId,
            dealId,
          })
        )}
      </ActionContainer>
    );

  return (
    <ActionContainer size={2} right>
      {document.uploaded ? (
        <DropDownMenu listItems={listItems} />
      ) : (
        <UploadButtonContainer>
          <Clickable>
            <UploadIconStyled />
            Upload
            <UploadFileInputStyle
              type="file"
              name="file"
              id="fileInput"
              onChange={(event) =>
                handleUpload(
                  event,
                  { document, state, department, uniqueId },
                  setDocuments,
                  resulting,
                  setResulting,
                )
              }
              multiple={false}
            />
          </Clickable>
        </UploadButtonContainer>
      )}
    </ActionContainer>
  );
};

export {
  UploadIconStyled,
  DownloadIconStyled,
  ViewIconStyled,
  UploadButtonStyle,
  DownloadButtonStyle,
  ViewAllButtonStyle,
  UploadFileInputStyle,
  EmailIconStyled,
  MergeFileIconStyled,
  ActionsIconStyled,
  renderDocumentActionContainer,
  openFile,
  fetchDocumentsFromS3,
  handleUpload,
  downloadFile,
};
