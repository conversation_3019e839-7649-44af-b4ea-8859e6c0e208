import { selector } from 'recoil';
import { PatternsManager } from '@utils/APIManager';
import refreshPatternsState from './refreshPatternsState';

const allPatternsState = selector({
  key: 'allPatternsState',
  get: async ({ get }) => {
    // counter used to force async refresh selector
    get(refreshPatternsState);

    return PatternsManager.getPatterns();
  },
});

export default allPatternsState;
