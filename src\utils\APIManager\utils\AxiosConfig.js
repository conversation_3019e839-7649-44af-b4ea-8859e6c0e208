import axios from 'axios';
import { throwError, displaySuccessMessage, startLoading, stopLoading } from '@utils/EventEmitter';

axios.defaults.validateStatus = (status) => status < 500;
axios.defaults.headers.post['Content-Type'] = 'application/json';

export const handleApiCall = async ({
  url,
  method,
  params,
  loadingMessage = null,
  successMessage = null,
}) => {
  try {
    if (loadingMessage) startLoading(loadingMessage);
    // Axios accepts a second argument, an object with a value 'param' for GET requests to add to url params
    // For POST requests the second argument is taken as is for the POST body.
    const apiCall = params
      ? axios[method](url, method.toUpperCase() === 'GET' ? { params } : params)
      : axios[method](url);
    const response = await apiCall;
    stopLoading();

    // Handle error codes we have messages for
    if (response.data?.error) return throwError(response.data?.error);

    // Handling client Requests Error
    // Example: Errors as 203, 404, etc
    if (response.status !== 200) return throwError(response.statusText);
    if (successMessage) displaySuccessMessage(successMessage);
    return response.data;
  } catch (error) {
    // Handling Server Errors
    // Example: Server is down and Request fails
    return throwError(error);
  }
};

export default axios;
