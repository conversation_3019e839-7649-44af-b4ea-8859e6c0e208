import styled from 'styled-components';

export const Wrapper = styled.div(
  ({ fullscreen }) => `

.loading-indicator {
  .overlay {
    position: ${fullscreen ? 'fixed' : 'absolute'};
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    z-index: 10000;
    background-color: rgba(0, 0, 0, 0.3);
  }
  .loading-container {
    background-color: white;
    position: ${fullscreen ? 'fixed' : 'absolute'};
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    top: calc(50% - 75px); // 50% - half of height
    left: calc(50% - 150px); // 50% - half of width
    height: 150px;
    width: 300px;
    z-index: 10001;
    border-radius: 15px;
    
    .loading-message {
      margin-bottom: 20px;
      text-align: center;
    }
  }
}

`,
);
