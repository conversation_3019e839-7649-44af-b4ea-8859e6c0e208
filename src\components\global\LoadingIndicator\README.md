# LoadingIndicator Component

This component is a dual use component. Ie..

**==>it can work as a global loading indicator handled and triggered by Recoil global state**

_==> and/or_

**==> it can be used as a dumb component that triggers based on props passed to it**

## Usage

If you are implementing this component, you are most likely implementing it as a local dumb indicator, not a global indicator, as there can only be one global loading indicator present in any application.

```
<LoadingIndicator
    loading={boolean} // defaults to false
    message={string} // defaults to empty string ('')
    fullscreen={boolean} // defaults to true
/>
```

None of the props are required. If `fullscreen` is set to false, the component runs off of the loading and message props that are passed to it, and it cannot be controlled by global recoil state.
