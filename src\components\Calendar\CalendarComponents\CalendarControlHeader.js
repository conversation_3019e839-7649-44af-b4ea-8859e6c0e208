import React, { useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';
import { ChevronLeft, ChevronRight } from '@styled-icons/boxicons-regular';

import { decodeEventType } from '@homeworksenergy/utility-service';

import { showFilterBarState } from '@recoil/calendar';
import { calendarTypeAtom } from '@recoil/app';

// eslint-disable-next-line import/no-cycle
import { RefreshButton } from '@components/global';
import { SecondaryButton } from '@components/global/Buttons';
import CalendarFilter from './CalendarFilter';
import Legend from './Legend';
import ManagerTrucksFilter from './ManagerTrucksFilter';
// eslint-disable-next-line import/no-cycle
import DatePicker from './DatePicker';
import FilterToggle from '../FilterToggle';
// eslint-disable-next-line import/no-cycle
import ScrollCalendarArrows from './ScrollCalendarArrows';

// Custom date display for map view
const MapDateDisplay = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.primary[500]};
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  text-align: center;
  min-width: 0;
`;

// Custom scroll arrows for map view (daily navigation)
const MapScrollArrows = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: ${({ theme }) => theme.primary[500]};
  gap: 2px;
  flex-shrink: 0;
  max-width: 200px;
`;

const MapScrollButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 6px 8px;
  background-color: ${({ theme }) => theme.secondary[200]};
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  margin: 0px 1px;
  white-space: nowrap;
  cursor: pointer;
  font-size: 11px;
  color: ${({ theme }) => theme.primary[500]};
  min-width: 32px;
  height: 28px;

  &:hover {
    background-color: ${({ theme }) => theme.secondary[300]};
  }

  svg {
    display: block;
  }
`;

// Styled button matching LegendButton design for navigation buttons
const NavigationButton = styled(SecondaryButton)`
  background: ${({ theme }) => theme.secondary[100]};
  border: 1px solid ${({ theme }) => theme.colors.eventGreen};
  border-radius: 4px;
  color: ${({ theme }) => theme.colors.eventGreen};
  padding: 0 20px;
  height: 30px;
`;

const StyledCalendarControlHeader = styled.div`
  display: flex;
  flex-direction: column;
  padding: 15px 15px 0px 15px;
  width: 100%;

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.laptop)} {
    display: none;
  }
`;

const StyledCalendarInteractionRow = styled.div`
  display: flex;
  justify-content: space-between;
`;

const FlexRowContainer = styled.div`
  display: flex;
  flex-direction: row;
  padding-bottom: 10px;
  gap: 8px;
  flex-wrap: nowrap;
  align-items: center;
`;

/**
 * Need left and right sides to be the same width for
 * centering the date picker and right aligning the scroll arrow
 */
const FlexFix = styled.div`
  width: 130px;
  overflow: visible;
`;

// for map view with wider right side for navigation buttons
const MapFlexFix = styled.div`
  min-width: 160px;
  overflow: visible;
  display: flex;
  justify-content: flex-end;
`;

const MobileCalendarControlHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin: 20px auto;
  gap: 10px;
  ${({ theme }) => theme.screenSize.up(theme.breakpoints.laptop)} {
    display: none;
  }
`;

const CalendarControlHeader = ({
  startDate = moment(),
  scrollCalendar = () => {},
  setRegionFilter = () => {},
  setEventTypeFilter = () => {},
  setAgentTypeFilter = () => {},
  isMonthly = false,
  showMonthYearPicker = false,
  regionTypeSelected = null,
  eventTypeSelected = '',
  agentTypeSelected = '',
  onMapViewClick = null,
  // props for map view customization
  isMapView = false,
  showFilters = true,
  showDatePicker = true,
  customDateDisplay = null,
  onBackToCalendar = null,
}) => {
  const calendarType = useRecoilValue(calendarTypeAtom);
  const showFilterBar = useRecoilValue(showFilterBarState);

  // map specific date navigation components
  const mapDateDisplay = (
    <MapDateDisplay>{customDateDisplay || startDate.format('MMM DD, YYYY')}</MapDateDisplay>
  );

  const mapScrollArrows = (
    <MapScrollArrows>
      <MapScrollButton onClick={() => scrollCalendar('-')} title="Previous Day">
        <ChevronLeft size={14} />
      </MapScrollButton>
      <MapScrollButton onClick={() => scrollCalendar()}>Today</MapScrollButton>
      <MapScrollButton onClick={() => scrollCalendar('+')} title="Next Day">
        <ChevronRight size={14} />
      </MapScrollButton>
    </MapScrollArrows>
  );

  const datePicker = showDatePicker ? (
    <DatePicker
      startDate={startDate}
      isMonthly={isMonthly}
      handleDateChange={scrollCalendar}
      showMonthYearPicker={showMonthYearPicker}
    />
  ) : null;

  const regionFilter = (
    <CalendarFilter
      value={regionTypeSelected}
      showFilterBar={showFilterBar}
      setFilter={(value) => setRegionFilter(Number(value))}
      isRegion
    />
  );
  const eventTypeFilter = (
    <CalendarFilter
      value={eventTypeSelected}
      setFilter={(value) => setEventTypeFilter(value)}
      isEventType
    />
  );
  const agentTypeFilter = (
    <CalendarFilter
      value={agentTypeSelected}
      showFilterBar={showFilterBar}
      setFilter={(value) => setAgentTypeFilter(value)}
      isRegion
    />
  );

  // Map specific action buttons
  const mapActionButtons = (
    <FlexRowContainer>
      {onBackToCalendar && (
        <NavigationButton onClick={onBackToCalendar}>Back to Calendar</NavigationButton>
      )}
    </FlexRowContainer>
  );

  const calendarActionButtons = (
    <FlexRowContainer>
      {showFilters && <FilterToggle />}
      <Legend />
      {onMapViewClick && <NavigationButton onClick={onMapViewClick}>Map</NavigationButton>}
      <RefreshButton />
    </FlexRowContainer>
  );

  const calendarScrollArrows = isMapView ? (
    mapScrollArrows
  ) : (
    <ScrollCalendarArrows scrollCalendar={scrollCalendar} isMonthly={isMonthly} />
  );

  // TODO: this doesn't really work for the monthly calendar
  // It seems like when you navigate to the page, it resets the calendarType, so it always thinks it's a MA HEA
  // It only really affects the legend and the filters, so I'm leaving it for now
  const { business: department } = decodeEventType(calendarType || '');

  const displayActionButtons =
    calendarType &&
    ['0000', '0001', '0004', '0005', '0100', '0106'].includes(calendarType?.slice(0, 4));
  const displayEventTypeFilter = calendarType && department === 'HEA'; // Both CT and MA
  const displayAgentTypeFilter = calendarType && calendarType?.slice(0, 4) === '0004';
  const displayRegionFilter = calendarType !== '000400';
  const displayManagerFilter = calendarType !== '000400' && calendarType !== '000100';

  const calendarInteractionRow = (
    <StyledCalendarInteractionRow>
      <FlexFix>
        {isMapView ? mapActionButtons : displayActionButtons && calendarActionButtons}
      </FlexFix>
      {isMapView ? mapDateDisplay : datePicker}
      {isMapView ? (
        <MapFlexFix>{calendarScrollArrows}</MapFlexFix>
      ) : (
        <FlexFix>{calendarScrollArrows}</FlexFix>
      )}
    </StyledCalendarInteractionRow>
  );
  const filterRow =
    showFilterBar && showFilters ? (
      <FlexRowContainer>
        {!isMonthly && displayManagerFilter && <ManagerTrucksFilter />}
        {displayRegionFilter && regionFilter}
        {displayEventTypeFilter && eventTypeFilter}
        {displayAgentTypeFilter && agentTypeFilter}
      </FlexRowContainer>
    ) : null;

  const showFilterRegionOnMobile = displayRegionFilter && showFilterBar;

  useEffect(() => {
    if (!showFilterBar) {
      setRegionFilter(0);
    }
  }, [showFilterBar, setRegionFilter]);

  return (
    <>
      <StyledCalendarControlHeader>
        {calendarInteractionRow}
        {filterRow}
      </StyledCalendarControlHeader>
      {/* Mobile needs drastically different structure/style, so I just built it separate from the rest of the header */}
      {/* TODO: actual mobile styles, maybe a menu to show or hide some of these options to save real estate? */}
      <MobileCalendarControlHeader>
        {datePicker}
        {calendarScrollArrows}
        {showFilterRegionOnMobile && regionFilter}
        {displayActionButtons && calendarActionButtons}
      </MobileCalendarControlHeader>
    </>
  );
};

CalendarControlHeader.propTypes = {
  startDate: PropTypes.instanceOf(moment),
  scrollCalendar: PropTypes.func,
  isMonthly: PropTypes.bool,
  showMonthYearPicker: PropTypes.bool,
  setRegionFilter: PropTypes.func,
  setEventTypeFilter: PropTypes.func,
  setAgentTypeFilter: PropTypes.func,
  regionTypeSelected: PropTypes.number,
  eventTypeSelected: PropTypes.string,
  agentTypeSelected: PropTypes.string,
  onMapViewClick: PropTypes.func,
  // props for map view customization
  isMapView: PropTypes.bool,
  showFilters: PropTypes.bool,
  showDatePicker: PropTypes.bool,
  customDateDisplay: PropTypes.string,
  onBackToCalendar: PropTypes.func,
};

export default CalendarControlHeader;
