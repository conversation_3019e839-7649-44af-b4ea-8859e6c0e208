import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useRecoilValue, useRecoilState } from 'recoil';

import { allPatternsState, selectedPatternState } from '@recoil/eventPatterns';
import { searchAdminState, selectedFiltersState } from '@recoil/admin';

import { ListCard, ScrollableList } from '@components/global/ScreenPartitionView';

const EventPatternListCell = (props) => {
  const [selectedPattern, setSelectedPattern] = useRecoilState(selectedPatternState);
  const { pattern } = props;
  const { patternId, patternName } = pattern;

  const selectPattern = () => {
    setSelectedPattern(pattern);
  };

  return (
    <ListCard
      onClick={() => selectPattern()}
      active={patternId === selectedPattern.patternId}
      title={patternName}
    />
  );
};

EventPatternListCell.propTypes = {
  pattern: PropTypes.shape({ patternId: PropTypes.string, patternName: PropTypes.string })
    .isRequired,
};

const EventPatternsList = () => {
  const allPatterns = useRecoilValue(allPatternsState);
  const [filteredPatterns, setFilteredPatterns] = useState(allPatterns);
  const selectedFilters = useRecoilValue(selectedFiltersState);
  const searchTerm = useRecoilValue(searchAdminState);

  useEffect(() => {
    const newFilteredPatterns = allPatterns.filter((pattern) => {
      let patternMatchesAllFilters = true;

      // Check to make sure pattern is matched for all filters
      const filterNames = Object.keys(selectedFilters);

      filterNames.forEach((filterName) => {
        if (pattern[filterName] !== selectedFilters[filterName].value)
          patternMatchesAllFilters = false;
      });

      // Check if search term is included in pattern name
      if (!pattern.patternName?.toLowerCase().includes(searchTerm.toLowerCase()))
        patternMatchesAllFilters = false;

      return patternMatchesAllFilters;
    });

    setFilteredPatterns(newFilteredPatterns);
  }, [selectedFilters, searchTerm, allPatterns]);

  return (
    <ScrollableList>
      {filteredPatterns.map((pattern) => {
        return <EventPatternListCell key={pattern.patternId} pattern={pattern} />;
      })}
    </ScrollableList>
  );
};

export default EventPatternsList;
