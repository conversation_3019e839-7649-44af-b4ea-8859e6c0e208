import Swal from 'sweetalert2/dist/sweetalert2';
import { decodeEventType } from '@homeworksenergy/utility-service';
import { displaySuccessMessage, throwError } from '@utils/EventEmitter';
import { salesforceUrls } from '@utils/constants';
import axios, { handleApiCall } from './utils/AxiosConfig';

const getAllRoles = async () => {
  try {
    const url = '/api/utility/getAllRoles';
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    return throwError({
      message: 'Failed to all roles from database.',
      params: 'This is a get * from roles, if it is failing there is a serious problem.',
    });
  }
};

const getAllRegions = async () => {
  try {
    const url = '/api/utility/getAllRegions';
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    return throwError({
      message: 'Failed to get all regions from database',
      params: 'This is a get * from regions, if this is failing there is a serious problem.',
    });
  }
};

const getAllStates = async () => {
  try {
    const url = '/api/utility/getAllStates';
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    return throwError({
      message: 'Failed to get all states from database',
      params: 'This is a get * from states, if this is failing there is a serious problem.',
    });
  }
};

const getAllDepartments = async () => {
  try {
    const url = '/api/utility/getAllDepartments';
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    return throwError({
      message: 'Failed to get all departments from database',
      params: 'This is a get * from departments, if this is failing there is a serious problem.',
    });
  }
};

const getDepartmentsByUserType = async (type) => {
  try {
    const url = `/api/utility/getDepartmentsByUserType/${type}`;
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    return throwError({
      message: 'Failed to get all departments from database',
      params: 'This is a get * from departments, if this is failing there is a serious problem.',
    });
  }
};

const getEventTypes = async (departmentType) => {
  try {
    const queryParams = departmentType ? `?departmentType=${departmentType}` : '';
    const url = `/api/utility/getEventTypes${queryParams}`;
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    return throwError({
      message: `Failed to get list of ${departmentType} job types.`,
      params: 'Please try again',
    });
  }
};

const getEventTypesByOid = async (oid) => {
  try {
    const url = `/api/utility/getEventTypesByOid/${oid}`;
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    return throwError({
      message: 'Failed to get list of job types for that user.',
      params: 'Please try again',
    });
  }
};

const getJobAttributesByEventType = async (eventType) => {
  try {
    const url = `/api/utility/getJobAttributesByEventType/${eventType}`;
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    return throwError({
      message: `Failed to get list of attributes for '${eventType}' job type.`,
      params: 'Please try again',
    });
  }
};

const getDocRepoBaseUrl = (dealId) => {
  if (!dealId) {
    return throwError({
      message:
        'Missing Deal Id. Please submit a ticket on FreskDesk with which event you are trying to view the doc repo for',
    });
  }
  let url = 'https://ts02.homeworksenergy.com/docRepo';
  if (window.location.origin === 'https://sch.homeworksenergy.com') {
    url = 'https://hes.homeworksenergy.com/docRepo';
  }
  return url;
};

const openDocRepo = (
  dealId,
  customerName = '',
  leadVendor = '',
  numUnit = 1,
  generateLink = false,
) => {
  const baseUrl = getDocRepoBaseUrl(dealId);
  const isSingleFam = numUnit === 1;
  const url = `${baseUrl}/${dealId}?customer_name=${encodeURI(
    customerName,
  )}&leadVendor=${encodeURIComponent(leadVendor)}&singleFamily=${encodeURI(isSingleFam)}`;
  return generateLink ? url : window.open(url, '_blank');
};

const openHVACDocRepo = (dealId, salesVisitId, customerName = '') => {
  const baseUrl = getDocRepoBaseUrl(dealId);
  return window.open(
    `${baseUrl}/hvac/${dealId}/${salesVisitId}?customer_name=${encodeURI(customerName)}`,
    '_blank',
  );
};

const openHvacImages = (dealId, customerName = '') => {
  const baseUrl = getDocRepoBaseUrl(dealId);
  return window.open(
    `${baseUrl}/ebr_images/${dealId}?customer_name=${encodeURI(customerName)}`,
    '_blank',
  );
};

const openSfPage = (sfId, sfObject) => {
  if (!sfId) {
    return throwError({
      message:
        'Missing Salesforce Id. Please submit a ticket on FreskDesk with which event you are trying to view the Salesforce page for.',
    });
  }
  let url = `https://homeworks--uatsandbox.sandbox.lightning.force.com/lightning/r/${sfObject}`;
  if (window.location.origin === 'https://sch.homeworksenergy.com') {
    url = `https://homeworks.lightning.force.com/lightning/r/${sfObject}`;
  }
  return window.open(`${url}/${sfId}/view`, '_blank');
};

const openSf2Page = (sfId) => {
  if (!sfId) {
    return throwError({
      message:
        'Missing Salesforce Id. Please submit a ticket on FreskDesk with which event you are trying to view the Salesforce page for.',
    });
  }
  let url = `${salesforceUrls.dev2}${sfId}`;
  if (window.location.origin === 'https://sch.homeworksenergy.com') {
    url = `${salesforceUrls.prod2}${sfId}`;
  }
  return window.open(url, '_blank');
};

const openOnlineWorkReceipt = (dealIds, isCap = false, isOffline = false, isMulti = false) => {
  if (!dealIds) {
    return throwError({
      message:
        'Missing Deal Id. Please submit a ticket on FreskDesk with which event you are trying to view the doc repo for',
    });
  }
  let url = 'https://ts01.homeworksenergy.com/work-receipt-dev';

  if (window.location.origin === 'https://sch.homeworksenergy.com') {
    url = 'https://sch.homeworksenergy.com/work-receipt-dev';
  }
  if (window.location.origin === 'http://localhost:8083') {
    url = 'http://localhost:8083/work-receipt-dev';
  }
  return window.open(
    `${url}?dealsParams=${dealIds}&isCap=${isCap}&isOffline=${isOffline}&isMulti=${isMulti}`,
    '_blank',
  );
};

const getRegionsForEventType = async (eventType) => {
  try {
    const url = `/api/utility/getRegionsByEventType/${eventType}`;
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    console.log('Error getting regions for eventType:', eventType);
    return false;
  }
};

const getEventTypesForEventType = async (eventType) => {
  try {
    const url = `/api/utility/getEventTypesForEventType/${eventType}`;
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    console.log('Error getting regions for eventType:', eventType);
    return false;
  }
};

const getRepairShopAddresses = async () => {
  try {
    const url = '/api/utility/getRepairShopAddresses';
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    console.log('Error getting repair shops.');
    return false;
  }
};

const getAllHolidays = async () => {
  try {
    const url = '/api/utility/getAllHolidays';
    const { data } = await axios.get(url);
    return data;
  } catch (error) {
    console.log('Error getting holidays.');
    return false;
  }
};

const getEventsForDealIdInDb = async (dealId) => {
  try {
    const url = `/api/utility/checkDealId/${dealId}`;
    const response = await handleApiCall({
      url,
      method: 'get',
      loadingMessage: 'Verifying Deal ...',
    });
    if (!response) return false;

    return response.length > 0;
  } catch (error) {
    console.log('Error checking Deal Id', error);
    return false;
  }
};

const checkEventsForAccountId = async (accountId) => {
  try {
    const url = `/api/utility/checkAccountId/${accountId}`;
    const response = await handleApiCall({
      url,
      method: 'get',
      loadingMessage: 'Verifying Account ...',
    });
    if (!response) return false;

    return response.length > 0;
  } catch (error) {
    console.log('Error checking Account Id', error);
    return false;
  }
};

const validateZipCode = async (zipcode, type) => {
  const { business: department } = decodeEventType(type);

  if (department !== 'HVAC Sales') return true;

  try {
    const url = '/api/utility/validateZipCode';
    const response = await handleApiCall({
      url,
      method: 'get',
      params: { zipcode, type },
      loadingMessage: 'Checking zipcode...',
    });

    if (response) return true;
    return throwError(`${zipcode} is not a supported zipcode for scheduling ${department} events.`);
  } catch (error) {
    console.log('Zipcode not supported!');
    return false;
  }
};

const getEventUsingOidDateStartTime = async (oid, date, startTime) => {
  try {
    const url = '/api/utility/getAgentEventForDateTime';
    const { data } = await axios.post(url, { oid, date, startTime });
    return data;
  } catch (error) {
    console.log('Failed to get event!', error);
    return false;
  }
};

const getHESDashboardDetails = async (sfId, regionAbbreviation, refresh) => {
  try {
    const url = '/api/utility/getHESDashboardDetails';
    const { data } = await axios.post(url, { sfId, regionAbbreviation, refresh });
    return data;
  } catch (error) {
    console.log('Failed to get Dashboard Details!', error);
    return false;
  }
};

const getHesPayDashboard = async () => {
  try {
    const url = '/api/utility/getHesPayDashboard';
    const { data } = await axios.post(url);
    return data;
  } catch (error) {
    console.log('Failed to get Dashboard Details!', error);
    return false;
  }
};

const getEventInfoUsingDealId = async (dealId) => {
  try {
    const url = `/api/utility/getEventInfoUsingDealId?dealId=${dealId}`;
    const { data } = await axios.post(url);
    return data;
  } catch (error) {
    console.log('Failed to get Doc Repo Link!', error);
    return false;
  }
};

const emailDocsToCustomer = async (params) => {
  try {
    const url = '/api/utility/emailDocsToCustomer';
    const { data } = await axios.post(url, params);
    return data;
  } catch (error) {
    return throwError({
      message: 'Failed to send Email Docs',
      params,
    });
  }
};
const getZipCodesByDepartment = async (departmentId) => {
  try {
    const url = '/api/utility/getZipCodesByDepartment';
    const response = await handleApiCall({
      url,
      method: 'get',
      params: { departmentId },
      loadingMessage: 'Getting zipcodes...',
    });
    return response;
  } catch (error) {
    console.log('Failed to get zipcodes: ', error);
    return [];
  }
};

const runOptimization = async (department, date) => {
  const url = '/api/utility/runOptimization';
  const optimizedCalendar = await getOptimizedCalendar(department, date);
  if (optimizedCalendar) {
    const confirm = await Swal.fire({
      icon: 'warning',
      title: 'Optimized Calendar Already Exists',
      text: `Do you want to re-run the optimization again for ${department} ${date}?`,
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirm?.value) return optimizedCalendar;
  }

  await handleApiCall({
    url,
    method: 'post',
    params: { department, date },
    successMessage:
      'Optimization has been Started, this may take few minuites. Please check you email and come back to swap events on calendar.',
  });
  return true;
};

const getOptimizedCalendar = async (department, date = null) => {
  const url = '/api/utility/getOptimizedCalendar';
  const response = await handleApiCall({
    url,
    method: 'post',
    params: { department, date },
    loadingMessage: 'Getting Optimized Calendar...',
  });
  return response.result;
};

const swapAllEvents = async (events, department) => {
  try {
    const url = '/api/events/superSwap';
    const response = await handleApiCall({
      url,
      method: 'post',
      params: { swapEvents: events, department },
      loadingMessage: 'Swapping Events...',
    });
    displaySuccessMessage('Successfully Swaped all events.');
    return response;
  } catch (error) {
    console.log('Failed to swap events: ', error);
    return [];
  }
};

export default {
  getAllRoles,
  getAllRegions,
  getAllStates,
  getAllDepartments,
  getDepartmentsByUserType,
  getEventTypes,
  getEventTypesByOid,
  getJobAttributesByEventType,
  openHvacImages,
  openDocRepo,
  openHVACDocRepo,
  openSfPage,
  openSf2Page,
  openOnlineWorkReceipt,
  getRegionsForEventType,
  getEventTypesForEventType,
  getRepairShopAddresses,
  getDocRepoBaseUrl,
  getAllHolidays,
  getEventsForDealIdInDb,
  checkEventsForAccountId,
  validateZipCode,
  getEventUsingOidDateStartTime,
  getHESDashboardDetails,
  getHesPayDashboard,
  getEventInfoUsingDealId,
  emailDocsToCustomer,
  getZipCodesByDepartment,
  runOptimization,
  getOptimizedCalendar,
  swapAllEvents,
};
