import React from 'react';
import { useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useTheme } from 'styled-components';

import { getCityAndZipcodeFromAddress, getZipcodeFromAddress } from '@utils/functions';
import { departmentZipcodesAtom } from '@recoil/utility';
import getEventColorForEventType from '@utils/colorDictWithEventType';
import Event from '@components/Calendar/EventComponents/Event';

const HEAEvent = ({ event, ...otherProps }) => {
  const theme = useTheme();
  const {
    type,
    startTime,
    address,
    notes,
    lock,
    numUnit,
    program = 'N/A',
    stateProgram,
    fuelType,
    approvalSoftware,
    townWarnings = [],
  } = event;

  const hvacSalesZipcodes = useRecoilValue(departmentZipcodesAtom);

  // TODO: do we need the customer names on the calendar events at all?
  let { customerName } = event;
  if (!customerName) customerName = 'No customer found';

  const displayTime = moment(startTime, 'hh:mm:ss').format('h:mm a');

  const cityAndZipcode = getCityAndZipcodeFromAddress(address) || 'No Address Found';
  const zipcode = getZipcodeFromAddress(address) || '';
  const isCapAndGas = event?.type === '000006' && fuelType === 'gas';
  const isMarketRateAndServiceableHVACSalesZipCode =
    (event?.type === '000000' || event?.type === '000001') && hvacSalesZipcodes?.includes(zipcode);

  const showHancockIcon = approvalSoftware === 'Hancock';
  const showGlobeIcon =
    townWarnings?.filter(({ townWarningId }) => {
      return townWarningId === 'DESIGNATED_EQUITY_COMMUNITY';
    })?.length > 0;

  return (
    <Event
      event={event}
      backgroundColor={getEventColorForEventType(event?.type, theme)}
      tooltip={notes?.officeNotes}
      headerText={`${displayTime} - ${type === '000008' ? stateProgram : program}${
        approvalSoftware === 'Hancock' ? ' - HC' : ''
      }`}
      bodyHeader={cityAndZipcode}
      bodyText={numUnit && numUnit > 1 ? `${numUnit} units` : ''}
      pinnable
      lockable
      lock={lock}
      showHeatingType={isCapAndGas || isMarketRateAndServiceableHVACSalesZipCode}
      showSolarIcon={
        notes?.fieldNotes?.includes('DO NOT set SunPower lead') ||
        notes?.fieldNotes?.includes('DO NOT set solar lead')
      }
      showGlobeIcon={showGlobeIcon}
      showNoDHWIcon={address?.city === 'Arlington' && ['000000', '000001'].includes(event?.type)}
      showHancockIcon={showHancockIcon}
      {...otherProps}
    />
  );
};

HEAEvent.propTypes = {
  event: PropTypes.shape({
    address: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        city: PropTypes.string,
        postalCode: PropTypes.string,
      }),
    ]),
    attributes: PropTypes.arrayOf(PropTypes.string),
    customerName: PropTypes.string,
    date: PropTypes.string,
    endTime: PropTypes.string,
    id: PropTypes.string,
    leadVendor: PropTypes.string,
    lock: PropTypes.bool,
    notes: PropTypes.shape({
      officeNotes: PropTypes.string,
      fieldNotes: PropTypes.string,
    }),
    numUnit: PropTypes.number,
    type: PropTypes.string,
    startTime: PropTypes.string,
    program: PropTypes.string,
    stateProgram: PropTypes.string,
    fuelType: PropTypes.string,
    approvalSoftware: PropTypes.string,
    townWarnings: PropTypes.arrayOf(
      PropTypes.shape({
        townWarningId: PropTypes.string,
      }),
    ),
  }).isRequired,
};

export default HEAEvent;
