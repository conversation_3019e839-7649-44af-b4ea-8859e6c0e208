import React, { useState } from 'react';
import styled from 'styled-components';
import { PayrollManager } from '@utils/APIManager';
import { Header } from '@components/global';

const WxPayrollDashboard = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;
const ButtonContainer = styled.div`
  border: solid black 1px;
  width: fit-content;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
`;
const ButtonDiv = styled.div``;

const WxPayroll = () => {
  const { generateCsvFilesForWxPayroll } = PayrollManager;
  const [urls, setUrls] = useState([]);

  const handleGetUrls = async () => {
    const rawUrls = await generateCsvFilesForWxPayroll();
    const urlObj = rawUrls.data.map((item) => {
      const obj = {};
      obj.name = item.split('/').pop();
      obj.url = item;
      return obj;
    });
    setUrls(urlObj);
  };

  const renderUrls = () => {
    return (
      <div style={{ marginTop: '1em' }}>
        <ul>
          {urls.map((item) => (
            <li key={item.name}>
              <a href={item.url}>{item.name}</a>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <WxPayrollDashboard>
      <Header h1>WX Payroll Dashboard</Header>
      <ButtonContainer>
        <Header h3>Generate Wx Payroll CSV Files With Salesforce Reports Data</Header>
        <ButtonDiv>
          <button onClick={handleGetUrls} type="button">
            Wx Payroll Report Data
          </button>
        </ButtonDiv>
        {renderUrls()}
      </ButtonContainer>
    </WxPayrollDashboard>
  );
};

export default WxPayroll;
