import React, { useEffect, useState } from 'react';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useDrop } from 'react-dnd';
import moment from 'moment';

import { EventsManager } from '@utils/APIManager';
import { throwError, eventChanged } from '@utils/EventEmitter';
import utilityService from '@homeworksenergy/utility-service';
import DateNumber from '@components/Calendar/MonthlyCalendar/DateNumber';
import { ErrorBoundary } from '@components/global';
import DisabledOverlay from '@components/Calendar/CalendarComponents/DisabledOverlay';
import CapacityIndicator from '@components/Calendar/CapacityIndicator';
import DayFilledIndicator from '@components/Calendar/DayFilledIndicator';
import { calendarTypeAtom, calendarIntentionAtom } from '@recoil/app';
import { showSidebarState, selectedEventState } from '@recoil/eventSidebar';
import { pinnedEventsAtom, pinnedEventSelectorFamily, allEventsAtom } from '@recoil/event';
import { useValidateCell } from '@recoil/hooks';

// eslint-disable-next-line import/no-cycle
import { DepartmentEvent, ErrorEvent } from '../EventComponents';

const StyledCalendarCell = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-grow: 1;
  flex-basis: 100%;
  word-wrap: break-word;
  background-color: ${({ status, isOver, disableCell, theme }) => {
    if (status === 'closed') return theme.secondary[900]; // Using theme colors
    if (isOver && !disableCell) return theme.secondary[400];
    return theme.secondary[100];
  }};

  ${({ theme }) => theme.screenSize.up(theme.breakpoints.tablet)} {
    border-left: 1px solid ${({ theme }) => theme.colors.calendarBorder};
    width: 0;
    min-height: 100px;
  }
`;

const MobileDate = styled.div`
  ${({ theme }) => theme.screenSize.up(theme.breakpoints.tablet)} {
    display: none;
  }
`;

const ClosedDayHeader = styled.div`
  height: 20px;
  background: ${({ theme }) => theme.secondary[800]};
  color: ${({ theme }) => theme.secondary[700]};
  width: 100%;
  text-align: center;
`;

const StartEndTimeHeader = styled.div`
  display: flex;
  height: 20px;
  background: ${({ theme }) => theme.secondary[800]};
  color: ${({ theme }) => theme.colors.formText};
  width: 100%;
  justify-content: center;
  gap: 12px;
  opacity: 0.5;

  &:hover {
    opacity: 1;
  }
`;

const StarEndTimeSlot = styled.div``;

const HolidayHeader = styled.div`
  height: 20px;
  background: ${({ theme }) => theme.colors.red};
  color: ${({ theme }) => theme.secondary[100]};
  width: 100%;
  text-align: center;
`;

const getDuration = (events) => {
  const totalHours = events.reduce(
    (acc, event) =>
      acc +
      moment
        .duration(moment(event?.endTime, 'hh:mm:ss').diff(moment(event?.startTime, 'hh:mm:ss')))
        .asHours(),
    0,
  );
  return Math.round((totalHours / 8) * 100) / 100;
};

const CalendarCell = ({
  events = [],
  status = 'open',
  oid = '',
  name = null,
  date = null, // only null for names
  isMonthly = false,
  children = '', // only used for names
  holiday = null,
  rowDisabled = false,
  maxAppt = null,
  startTime = '',
  endTime = '',
  agentName = '',
}) => {
  const dayFilled = getDuration(events);
  const togglePinnedEvent = useSetRecoilState(pinnedEventSelectorFamily());
  const allPinnedEvents = useRecoilValue(pinnedEventsAtom);
  const allEvents = useRecoilValue(allEventsAtom);
  const calendarIntention = useRecoilValue(calendarIntentionAtom);
  const calendarType = useRecoilValue(calendarTypeAtom);
  const disableCell = useValidateCell({ rowDisabled, date, status });
  const cellDateString = date.format('MM/DD/YYYY');

  const { business } = utilityService.decodeEventType(calendarType);

  const [{ isOver }, drop] = useDrop({
    accept: ['CALENDAR_EVENT'],
    drop: async (item) => {
      if (disableCell) return;

      // Dropped "item" has only the { id } of the event
      const { id } = item;
      // Fetch the full event from the allEvents variable
      const draggedEvent = allEvents[id] || allPinnedEvents[id];

      // Determine what action to take
      const canSwap = ['HEA', 'HVAC Sales'].includes(business);
      const isReassign = draggedEvent.date === cellDateString;

      // Find if there is an event scheduled in the time they are trying to drop onto
      const dropEvent = Object.values(allEvents).find((event) => {
        return (
          isReassign &&
          event.date === draggedEvent.date &&
          event.oid === oid &&
          event.startTime === draggedEvent.startTime
        );
      });

      // If swapping is allowed and an event exists in the time they are trying to drop onto
      const isSwap = canSwap && !!dropEvent;

      if (isSwap) {
        const swapParams = {
          selectedEvent: draggedEvent,
          swapEvent: dropEvent,
          type: calendarType,
        };
        await EventsManager.swapEvent(swapParams);
      } else if (isReassign) {
        const reassignParams = {
          ...draggedEvent,
          agentName,
          oids: [oid],
          oid,
          startEndTimes: utilityService.getStartEndTimesForEvent(
            { ...draggedEvent, date },
            allEvents,
          ),
        };
        await EventsManager.reassignEvent(reassignParams);
      } else {
        // Reschedule
        const rescheduleParams = {
          ...draggedEvent,
          agentName,
          oids: [oid],
          date: cellDateString,
          // TODO: This is not the ideal way to do this. if we reschedule from anywhere else, we have to make sure that these startEndTimes are populated.
          // TODO: if we could access our recoil state from our eventValidation functions, that is a much better place for this to live, so we're sure every time we reschedule it will be consistent
          startEndTimes: utilityService.getStartEndTimesForEvent(
            { ...draggedEvent, date },
            allEvents,
          ),
        };

        const rescheduled = await EventsManager.rescheduleEvent(rescheduleParams);
        if (!rescheduled) eventChanged();
      }

      const isEventPinned = allPinnedEvents[id];
      if (isEventPinned) togglePinnedEvent(id);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });
  const setShowSidebar = useSetRecoilState(showSidebarState);
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);

  const [eventComponents, setEventComponents] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      // todo: check here for weird slots not showing up in the right order for CT
      const components = events
        .sort((a, b) => (a.jobLength > b.jobLength ? -1 : 1))
        .map((event, index) => {
          // TODO: can we move this all into the generic event component?
          // The opening events are the only part that could be weird. otherwise it should all be based on the event type...
          let EventComponent;
          const { type: eventType } = event;
          if (eventType) {
            // For regular scheduled events
            EventComponent = DepartmentEvent;
          } else {
            throwError({ message: "Couldn't find an event type for that event." });
            return null;
          }
          return (
            <ErrorBoundary key={event.id || index} fallback={<ErrorEvent event={event} />}>
              <EventComponent key={event.id} event={event} />
            </ErrorBoundary>
          );
        });
      setEventComponents(components);
    };
    fetchData();
  }, [events, calendarType, business]);

  const showCreateSidebar = () => {
    // TODO: break out date format app wide
    setSelectedEvent({
      ...selectedEvent,
      oids: [oid],
      date: cellDateString,
    });
    setShowSidebar(true);
  };

  if (name) return <div className="name cell">{children}</div>;

  const cellIndicator = ['HEA', 'HVAC Sales'].includes(business) ? (
    <CapacityIndicator maxAppt={maxAppt} date={cellDateString} oid={oid} />
  ) : (
    <DayFilledIndicator dayFilled={dayFilled} />
  );

  const isOpenDay = ['HEA', 'HVAC Sales'].includes(business) && !holiday && status === 'open';
  return (
    <StyledCalendarCell
      status={status}
      ref={drop}
      isOver={isOver}
      disableCell={disableCell}
      onDoubleClick={showCreateSidebar}
      data-testid={!isMonthly && status === 'open' ? 'calendar-cell' : ''}
    >
      {(disableCell || rowDisabled) && <DisabledOverlay />}
      {status === 'closed' && <ClosedDayHeader>Closed</ClosedDayHeader>}
      {isOpenDay && (
        <StartEndTimeHeader>
          <StarEndTimeSlot>{startTime}</StarEndTimeSlot>
          <StarEndTimeSlot>-</StarEndTimeSlot>
          <StarEndTimeSlot>{endTime}</StarEndTimeSlot>
        </StartEndTimeHeader>
      )}
      {holiday && <HolidayHeader>{holiday}</HolidayHeader>}
      {isMonthly && <DateNumber date={date} />}
      <MobileDate>{date.format('MM/DD ddd')}</MobileDate>
      {eventComponents.map((e, i) => (
        // eslint-disable-next-line react/no-array-index-key
        <React.Fragment key={i}>{e}</React.Fragment>
      ))}
      {calendarIntention === 'view' && cellIndicator}
    </StyledCalendarCell>
  );
};

CalendarCell.propTypes = {
  events: PropTypes.arrayOf(PropTypes.shape({})),
  status: PropTypes.string,
  oid: PropTypes.string,
  agentName: PropTypes.string,
  name: PropTypes.bool,
  date: PropTypes.instanceOf(moment),
  isMonthly: PropTypes.bool,
  children: PropTypes.string,
  holiday: PropTypes.string,
  rowDisabled: PropTypes.bool,
  maxAppt: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
  startTime: PropTypes.string,
  endTime: PropTypes.string,
};

export default CalendarCell;
