import { atom } from 'recoil';

/**
 * This is used to keep the 'formFields' generic in the DataIntakeForm.
 * The form field definitions hold the default values of the fields.
 * These change for the 'perUnit' fields when the number of units changes (ie: [x] to [x, x, x, x])
 *
 * It is currently used in the formSettings state for the functionality described above.
 * However it may be necessary for other parts in the future
 */
const formFieldsState = atom({
  key: 'formFieldsAtom',
  default: {},
});

export default formFieldsState;
