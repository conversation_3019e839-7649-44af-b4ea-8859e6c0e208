import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const AuditIncentiveForm = ({ record = {} }) => {
  const { dealId, auditIncentiveAmount, date, subject } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="auditIncentiveAmount"
            value={auditIncentiveAmount}
            title="Audit Incentive"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput readOnly name="subject" value={subject} title="Subject" placeholder="" />
          <FormInput readOnly name="date" value={date} title="Date" placeholder="" />
        </Col>
      </Row>
    </>
  );
};

AuditIncentiveForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    auditIncentiveAmount: PropTypes.string,
    date: PropTypes.string,
    subject: PropTypes.string,
  }),
};

export default AuditIncentiveForm;
