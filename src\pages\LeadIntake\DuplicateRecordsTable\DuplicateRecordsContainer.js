import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import swal from 'sweetalert2/dist/sweetalert2';

import {
  showDuplicateRecordsAtom,
  duplicateLeadsAtom,
  selectedLeadIdAtom,
  duplicateRecordsTypeAtom,
  intakeFlowAtom,
  stateAtom,
} from '@recoil/leadIntake';
import { formValuesState } from '@recoil/dataIntakeForm';
import { SalesforceManager } from '@utils/APIManager';
import { salesforceUrls } from '@utils/constants';
import { getFormattedCustomerInfoFromLead } from '@utils/functions';
import DuplicateRecordsTable from './DuplicateRecordsTable';
import { duplicateTableSteps } from '../utils/consts';
import useGetDuplicateLeads from '../utils/getters/useGetDuplicateLeads';

const DuplicateRecordsContainer = (props) => {
  const history = useHistory();
  const location = useLocation();
  const path = location.pathname.split('/')[1];
  const { leadId } = useParams();
  const { resetDuplicateLeadRecordsTable, backTab } = props;

  const state = useRecoilValue(stateAtom);
  const [flowOfIntake, setFlowOfIntake] = useRecoilState(intakeFlowAtom);
  const tableData = useRecoilValue(duplicateLeadsAtom);
  const [tableStep, setTableStep] = useRecoilState(duplicateRecordsTypeAtom);
  const [formValues, setFormValues] = useRecoilState(formValuesState);
  const [showDuplicateRecordsTable, setShowDuplicateRecordsTable] = useRecoilState(
    showDuplicateRecordsAtom,
  );
  const setLeadId = useSetRecoilState(selectedLeadIdAtom);

  const isCT = state === 'CT';
  const sfType = isCT ? '2.0' : '1.0';

  const host = window.location.origin;

  const salesforceProdLink = isCT ? salesforceUrls.prod2 : salesforceUrls.prod;
  const salesforceDevLink = isCT ? salesforceUrls.dev2 : salesforceUrls.dev;

  const salesforceUrl =
    host === 'https://sch.homeworksenergy.com' ? salesforceProdLink : salesforceDevLink;

  useEffect(() => {
    resetTableSettings();
  }, [resetDuplicateLeadRecordsTable]);

  useEffect(() => {
    // TODO: why do we care if this is coming from salesforce? If there is a lead id in the params, we should skip the duplicate
    const isLeadIdCommingFromSalesforce = leadId && !flowOfIntake;
    if (isLeadIdCommingFromSalesforce) {
      fetchLeadById(leadId, 'SALESFORCE_FLOW');
    }
  }, [leadId, flowOfIntake]);

  const fetchDuplicateLeads = useGetDuplicateLeads(sfType);

  const fetchLeadById = async (id, flow = 'MANUAL_INTAKE_FLOW') => {
    try {
      const { sfObject } = await SalesforceManager.getCustomerInfoFromLead(id, sfType);
      if (isCT) return setLeadTwoInfo(sfObject);
      const {
        referredByAuditor,
        leadSource,
        isAuditorValueOnSf,
        referralCode,
        siteId,
        ...rest
      } = getFormattedCustomerInfoFromLead(sfObject);
      const customerInfoValues =
        flow === 'MANUAL_INTAKE_FLOW'
          ? { referredByAuditor, leadSource, isAuditorValueOnSf, referralCode, siteId }
          : {
              referredByAuditor,
              leadSource,
              isAuditorValueOnSf,
              referralCode,
              siteId,
              ...rest,
            };
      setFormValues({ ...customerInfoValues });
      history.push(`/${path}/${id}`);
      setFlowOfIntake(flow);
      return true;
    } catch (err) {
      swal.fire({
        icon: 'error',
        title: err,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      history.replace(`/${path}/`);
    }
    return false;
  };

  const setLeadTwoInfo = (sfObject) => {
    const data = getFormattedCustomerInfoFromLead(sfObject, formValues, true);
    setFormValues({ ...data });
    history.push(`/${path}/${data.leadId}`);
  };

  const handleRowClick = (e, params) => {
    if (!params?.id) {
      return swal.fire({
        title: 'Row Id is not valid.',
        confirmButtonText: 'Yes',
        showCancelButton: true,
        cancelButtonText: 'No',
      });
    }
    // TODO We need to create a custom generic table for our global components
    // Currently custom table is most of filled with hardcoded values
    // That is why we have to use such weird patch
    // This check is added to check if user clicked on row then proceed, if clicked on checkbox then don't proceed
    if (e.target.tagName.toLowerCase() !== 'input') {
      window.open(`${salesforceUrl}${params.id}`, '_blank');
    }
    return true;
  };

  // This callback will called when we select duplicate lead from duplicate lead table
  const handleDuplicateLeadSelection = async (selectedLeadId) => {
    const { value: confirmed } = await swal.fire({
      title: `Are you sure you want to update this lead: ${selectedLeadId}?`,
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirmed) return;
    await fetchLeadById(selectedLeadId);
    setLeadId(selectedLeadId);
    resetTableSettings();
  };

  const resetTableSettings = () => {
    setTableStep(duplicateTableSteps.accounts);
    setShowDuplicateRecordsTable(false);
  };

  const showDuplicateAccountsTable =
    showDuplicateRecordsTable && tableStep === duplicateTableSteps.accounts;
  const showDuplicateLeadsTable =
    showDuplicateRecordsTable && tableStep === duplicateTableSteps.leads;

  return (
    <>
      {showDuplicateAccountsTable && (
        <DuplicateRecordsTable
          tableData={tableData}
          next={fetchDuplicateLeads}
          back={backTab}
          columnFor={duplicateTableSteps.accounts}
          handleRowClick={handleRowClick}
          sfType={sfType}
        />
      )}
      {showDuplicateLeadsTable && (
        <DuplicateRecordsTable
          tableData={tableData}
          columnFor={duplicateTableSteps.leads}
          handleSelectedRow={handleDuplicateLeadSelection}
          handleRowClick={handleRowClick}
          next={resetTableSettings}
          back={backTab}
          sfType={sfType}
        />
      )}
    </>
  );
};

DuplicateRecordsContainer.propTypes = {
  resetDuplicateLeadRecordsTable: PropTypes.bool.isRequired,
  backTab: PropTypes.func.isRequired,
};

export default DuplicateRecordsContainer;
