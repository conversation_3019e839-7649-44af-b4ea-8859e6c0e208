import React from 'react';
import { useRecoilValue } from 'recoil';

import { pinnedEventsAtom } from '@recoil/event';
import { calendarTypeAtom } from '@recoil/app';
import ErrorBoundary from '@components/global/ErrorBoundary';
import ErrorEvent from './ErrorEvent';
import DepartmentEvent from './DepartmentEvent';

const PinnedEvents = () => {
  const pinnedEvents = useRecoilValue(pinnedEventsAtom);
  const calendarType = useRecoilValue(calendarTypeAtom);

  if (!calendarType) return null;

  return Object.keys(pinnedEvents).map((pinnedEventId) => {
    const event = pinnedEvents[pinnedEventId];
    return (
      <ErrorBoundary key={event?.id} fallback={<ErrorEvent event={event} />}>
        <DepartmentEvent event={event} ignoreMultiDay />
      </ErrorBoundary>
    );
  });
};

export default PinnedEvents;
