import { selectorFamily } from 'recoil';

import { UtilityManager } from '@utils/APIManager';

const jobAttributesSelectorFamily = selectorFamily({
  key: 'jobAttributes',
  get: (params) => async () => {
    const { type: eventType, group } = params;
    if (!eventType) return null;
    const attributes = await UtilityManager.getJobAttributesByEventType(eventType);
    let filterAttributes = attributes;
    if (group)
      filterAttributes = attributes.filter(({ attributeGroup }) => {
        return group === attributeGroup;
      });
    return filterAttributes.map(({ attributeName, attributeId }) => {
      return { key: attributeName, value: attributeId };
    });
  },
});

export default jobAttributesSelectorFamily;
