import React, { memo, useMemo } from 'react';
import { formatPhoneNumber } from '@utils/functions';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import CopyText from '@components/global/Icons/CopyText';
import { uniqueId } from 'lodash';
import { formValuesState } from '@recoil/dataIntakeForm';
import { useRecoilValue } from 'recoil';
import { ScriptHeader, ScriptText, ScriptTitle } from './styles';

const ScriptSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
`;
const ScriptHeaderContainer = styled.h4`
  display: flex;
  gap: 12px;
  align-items: flex-start;
`;
const CopyTextContainer = styled.h4`
  width: 30px;
`;
const CustomerInformationContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 6px;
`;

const SectionWrapper = styled.div``;

export const CustomerInformation = memo(function CustomerInformation({
  customerFirstName = '',
  customerLastName = '',
  customerAddress = '',
  customerPrimaryPhoneNumber = '',
  customerEmail = '',
}) {
  const showCustomerPersonalDetails =
    customerFirstName || customerLastName || customerAddress || customerEmail;
  if (!showCustomerPersonalDetails) return null;
  return (
    <CustomerInformationContainer>
      {customerFirstName && (
        <ScriptText>
          {customerFirstName} {customerLastName}
        </ScriptText>
      )}
      {customerAddress?.displayAddress && (
        <ScriptText block>{customerAddress?.displayAddress} </ScriptText>
      )}
      <ScriptText>
        {customerPrimaryPhoneNumber && formatPhoneNumber(customerPrimaryPhoneNumber)}
      </ScriptText>
      <ScriptText>{customerEmail} </ScriptText>
    </CustomerInformationContainer>
  );
});

export const UnitInformation = memo(function UnitInformation({
  index = 0,
  fuelType = '',
  gasProvider = [],
  gasAccountNumber = [],
  electricProvider = [],
  electricAccountNumber = [],
}) {
  return (
    <React.Fragment key={uniqueId()}>
      <ScriptHeader>{electricProvider?.[index] ? `Unit ${index + 1}` : null}</ScriptHeader>
      {electricProvider?.[index] && (
        <SectionWrapper>
          <ScriptText>{fuelType && `Heating Fuel: ${fuelType}`}</ScriptText>
          <SectionWrapper>
            <ScriptText>
              {electricProvider?.[index] && `Electric Provider: ${electricProvider?.[index]}`}
            </ScriptText>
          </SectionWrapper>
          <ScriptText>
            {electricAccountNumber?.[index] &&
              `Electric Account Number: ${electricAccountNumber?.[index]}`}
          </ScriptText>
        </SectionWrapper>
      )}
      {fuelType === 'Gas' && gasProvider?.[index] && (
        <SectionWrapper>
          <ScriptText>{gasProvider?.[index] && `Gas Provider: ${gasProvider?.[index]}`}</ScriptText>
          <SectionWrapper>
            <ScriptText>
              {gasAccountNumber?.[index] && `Gas Account Number: ${gasAccountNumber?.[index]}`}
            </ScriptText>
          </SectionWrapper>
        </SectionWrapper>
      )}
    </React.Fragment>
  );
});

export const CopyCustomerInformation = memo(function CopyCustomerInformation() {
  const formValues = useRecoilValue(formValuesState);
  const {
    customerFirstName = '',
    customerLastName = '',
    customerAddress = '',
    customerPrimaryPhoneNumber = '',
    customerEmail = '',
    heatingFuel = [],
    electricAccountNumber = [],
    electricProvider = [],
    gasProvider = [],
    gasAccountNumber = [],
  } = formValues;

  const copyTextValue = useMemo(() => {
    const isCustomerInfoAdded =
      customerFirstName ||
      customerLastName ||
      customerAddress?.displayAddress ||
      customerPrimaryPhoneNumber ||
      customerEmail;
    let value = isCustomerInfoAdded
      ? `${customerFirstName || ''} ${customerLastName || ''}\n ${customerAddress?.displayAddress ||
          ''}\n ${customerPrimaryPhoneNumber || ''}\n ${customerEmail || ''}\n `
      : '';
    heatingFuel.forEach((fuelType, index) => {
      if (electricProvider?.[index] && fuelType) {
        value += `\n Unit ${index + 1} \n Heating Fuel: ${fuelType} \n Electric Provider: ${
          electricProvider?.[index]
        }`;
      }
      if (electricAccountNumber?.[index]) {
        value += `\n Electric Account Number: ${electricAccountNumber?.[index]} \n`;
      } else {
        value += '\n';
      }

      if (fuelType === 'Gas' && gasProvider?.[index]) {
        value += `Gas Provider: ${gasProvider?.[index]}`;
      }
      if (fuelType === 'Gas' && gasAccountNumber?.[index]) {
        value += `\n Gas Account Number: ${gasAccountNumber?.[index]} \n`;
      }
    });
    return value;
  }, [
    customerFirstName,
    customerLastName,
    customerAddress,
    customerPrimaryPhoneNumber,
    customerEmail,
    electricProvider,
    electricAccountNumber,
    heatingFuel,
    gasProvider,
    gasAccountNumber,
  ]);

  return (
    <>
      <ScriptHeaderContainer>
        <ScriptTitle>Customer Information</ScriptTitle>
        <CopyTextContainer>
          <CopyText
            left="40px"
            backgroundColor="transparent"
            alignConfirm
            copyTextValue={copyTextValue}
          />
        </CopyTextContainer>
      </ScriptHeaderContainer>
      <CustomerInformation
        customerFirstName={customerFirstName}
        customerLastName={customerLastName}
        customerAddress={customerAddress}
        customerPrimaryPhoneNumber={customerPrimaryPhoneNumber}
        customerEmail={customerEmail}
      />
      <ScriptSection>
        {heatingFuel?.map((fuelType, index) => (
          <UnitInformation
            key={fuelType}
            index={index}
            fuelType={fuelType}
            gasProvider={gasProvider}
            gasAccountNumber={gasAccountNumber}
            electricProvider={electricProvider}
            electricAccountNumber={electricAccountNumber}
          />
        ))}
      </ScriptSection>
    </>
  );
});

CustomerInformation.propTypes = {
  customerFirstName: PropTypes.string,
  customerLastName: PropTypes.string,
  customerAddress: PropTypes.shape({
    displayAddress: PropTypes.string,
    street: PropTypes.string,
    postalCode: PropTypes.string,
    city: PropTypes.string,
    state: PropTypes.string,
    longitude: PropTypes.number,
    lattitude: PropTypes.number,
    addressKey: PropTypes.string,
  }),
  customerPrimaryPhoneNumber: PropTypes.string,
  customerEmail: PropTypes.string,
};

UnitInformation.propTypes = {
  index: PropTypes.number,
  fuelType: PropTypes.string,
  gasProvider: PropTypes.arrayOf(PropTypes.string),
  gasAccountNumber: PropTypes.arrayOf(PropTypes.string),
  electricProvider: PropTypes.arrayOf(PropTypes.string),
  electricAccountNumber: PropTypes.arrayOf(PropTypes.string),
};
