import { selector } from 'recoil';

import { getAuthorizedDepartments } from '@utils/AuthUtils';
import { usersSelectorFamily } from '@recoil/users';
import { searchAdminState } from '@recoil/admin';

// Sorts user obj by last name
const sortUsers = (users) => {
  return users.sort((a, b) => {
    const aUpper = a.lastname.toUpperCase();
    const bUpper = b.lastname.toUpperCase();
    // eslint-disable-next-line no-unused-expressions
    return aUpper > bUpper ? 1 : -1;
  });
};

const searchUsers = (users, searchTerm) => {
  const filteredUsers = users.filter((user) => {
    const { displayName } = user;
    return displayName?.toLowerCase().includes(searchTerm?.toLowerCase());
  });

  return filteredUsers;
};

// Filter out users the active user doesn't have admin permissions for, ie other departments.
const filterUsers = (users) => {
  const operatorDepartments = getAuthorizedDepartments();
  return users.filter((user) => {
    // If the acting user is a manager of any department
    // and the user being filtered has no departments
    // Show the user on the table
    if (operatorDepartments?.length > 0 && user.departments?.length === 0) return true;

    // Else, filter out users from departments that the acting users isn't a manager of
    return user.departments?.some((department) =>
      operatorDepartments.find(
        ({ department: operatorDepartmentt }) => operatorDepartmentt === department,
      ),
    );
  });
};

// get users by departments
const allAuthorizedUsersForUserSelector = selector({
  key: 'allAuthorizedUsersForUserSelector',
  get: async ({ get }) => {
    // let departments = getAuthorizedDepartments();
    // departments = departments.map((department) => {
    //   return department.replace('-', ' ');
    // });

    let users = get(usersSelectorFamily());
    // Search by a specific search term
    // TODO: make this atom generic to be used across all admin screens
    const searchTerm = get(searchAdminState);

    users = filterUsers(users);
    users = searchUsers(users, searchTerm);
    users = sortUsers(users);

    return users;
  },
});

export default allAuthorizedUsersForUserSelector;
