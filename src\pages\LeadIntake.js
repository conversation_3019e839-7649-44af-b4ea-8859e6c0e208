import React, { useEffect } from 'react';

const LeadIntake = () => {
  useEffect(() => {
    let scheduler2Url;
    const currentHost = window.location.origin;
    switch (currentHost) {
      case 'https://sch.homeworksenergy.com':
        scheduler2Url = 'https://hes.homeworksenergy.com/infieldSchedule';
        break;
      default:
        scheduler2Url = 'https://ts02.homeworksenergy.com/infieldSchedule';
        break;
    }
    window.open(scheduler2Url, '_blank');
    window.location.href = '/home';
  }, []);
  return <div>Redirecting...</div>;
};

export default LeadIntake;
