import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue } from 'recoil';

import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import { SecondaryButton } from '@components/global/Buttons';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormMultiselect,
  FormRadioButtons,
  FormTextBox,
  FormSelect,
} from '@components/global/Form';
import FormFieldContainer from '@components/global/Form/FormFieldContainer';
import { LoadingIndicator } from '@components/global';

import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import { agentsFormOptionsSelector } from '@recoil/agents';
import { selectedEventState } from '@recoil/eventSidebar';
import { calendarTypeAtom } from '@recoil/app';
import { leadVettedValues } from '@utils/businessLogic/hvacSalesBusinessLogic';
import { isAuthorized } from '@utils/AuthUtils';

const FindHVACSalesSlotsForm = ({ handleFindSlotsClick, handleSaveClick }) => {
  const [slotInfo, setSlotInfo] = useRecoilState(selectedEventState);
  const calendarType = useRecoilValue(calendarTypeAtom);
  const {
    includeAgents,
    type,
    jobLength,
    sfIds: { accountId },
    numUnit,
    address,
    notes,
    leadSource,
    leadVetted,
  } = slotInfo;

  const agents = useRecoilValue(agentsFormOptionsSelector);
  const isManager = isAuthorized('Manager', 'HVAC-Sales');
  const [loading, setLoading] = useState(false);
  const { department } = JSON.parse(localStorage.getItem('user'));

  if (department === 'Marketing') setSlotInfo({ ...slotInfo, leadSource: 'Field Marketing' });

  useEffect(() => {
    handleFieldChange({ target: { name: 'type', value: calendarType } });
  }, []);

  const leadSourceOptions = [
    { key: 'Auditor Referral', value: 'Auditor Referral' },
    { key: 'Customer Referral', value: 'Customer Referral' },
    { key: 'Direct HVAC', value: 'Direct HVAC' },
    { key: 'Direct Mail', value: 'Direct Mail' },
    { key: 'Email', value: 'Email' },
    { key: 'Employee Referral', value: 'Employee Referral' },
    { key: 'Facebook', value: 'Facebook' },
    { key: 'Google Ads', value: 'Google Ads' },
    { key: 'HEA Visit', value: 'HEA Visit' },
    { key: 'HomeAdvisor', value: 'HomeAdvisor' },
    { key: 'Home Energy Specialist', value: 'Home Energy Specialist' },
    { key: 'Leadify', value: 'Leadify' },
    { key: 'Mass Save', value: 'Mass Save' },
    { key: 'Multifamily', value: 'Multifamily' },
    { key: 'Paid Digital Other', value: 'Paid Digital Other' },
    { key: 'Paid Search', value: 'Paid Search' },
    { key: 'Partners', value: 'Partners' },
    { key: 'Porch', value: 'Porch' },
    { key: 'Radio', value: 'Radio' },
    { key: 'Repeat Customer', value: 'Repeat Customer' },
    { key: 'Social', value: 'Social' },
    { key: 'TV', value: 'TV' },
    { key: 'Word of mouth', value: 'Word of mouth' },
    { key: 'Inside Sales', value: 'Inside Sales' },
    { key: 'Website', value: 'Website' },
    { key: 'Service Tech', value: 'Service Tech' },
    { key: 'Wx Install', value: 'Wx Install' },
    { key: 'CAP', value: 'CAP' },
  ];

  const leadVettedOptions = leadVettedValues.map((leadVetted) => {
    return { key: leadVetted, value: leadVetted };
  });

  const handleNumUnitChange = (e) => {
    const { name, value } = e.target;

    handleFieldChange({ target: { name, value: Number(value) } });
  };

  const handleFieldChange = (e, updatedEvent = slotInfo) => {
    handleFormFieldChange(e, updatedEvent, setSlotInfo);
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col size={2}>
            <HeaderTitle>Book Appointment</HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            <SfIdInputs sfObjectType="account" setLoading={setLoading} />
            {accountId && (
              <>
                <FormRadioButtons
                  title="How many units?"
                  name="numUnit"
                  value={numUnit}
                  options={[
                    { key: '1', value: 1 },
                    { key: '2', value: 2 },
                    { key: '3', value: 3 },
                    { key: '4', value: 4 },
                  ]}
                  onChange={handleNumUnitChange}
                />
                <FormFieldContainer fieldName="virtualVisit">
                  <FormRadioButtons
                    name="type"
                    required
                    title="Visit Type"
                    options={[
                      { key: 'Sales Visit', value: calendarType },
                      { key: 'Virtual Sales Visit ', value: '000104' },
                    ]}
                    value={type}
                    onChange={handleFieldChange}
                    maxGap
                    weight={650}
                  />
                </FormFieldContainer>
                {address?.displayAddress && (
                  <FormInput
                    readOnly
                    title="Address"
                    name="address"
                    value={address.displayAddress}
                  />
                )}
                {accountId && (
                  <>
                    {department !== 'Marketing' && (
                      <FormSelect
                        required
                        title="HVAC Lead Source"
                        placeholder="Select HVAC Lead Source"
                        name="leadSource"
                        value={leadSource}
                        onChange={handleFieldChange}
                        options={leadSourceOptions}
                      />
                    )}
                    <FormSelect
                      title="Lead Vetted"
                      placeholder="Select Lead Vetted"
                      name="leadVetted"
                      value={leadVetted}
                      readOnly={!isManager}
                      onChange={handleFieldChange}
                      options={leadVettedOptions}
                    />
                    <FormMultiselect
                      title="Include Crew(s)"
                      name="includeAgents"
                      value={includeAgents}
                      options={agents}
                      onChange={handleFieldChange}
                    />
                    <FormTextBox
                      name="notes.fieldNotes"
                      value={notes.fieldNotes}
                      title="Notes"
                      placeholder=""
                      onChange={handleFieldChange}
                    />
                  </>
                )}
              </>
            )}
          </Col>
        </Row>
        <Row>
          <Col>
            <AvailableSlots />
            <BookSlotsButton handleBookSlots={() => handleSaveClick()} />
          </Col>
        </Row>
      </EventSidebarBody>
      <EventSidebarFooter>
        {type && jobLength ? (
          <>
            <SecondaryButton left onClick={() => handleFindSlotsClick()}>
              View Available Slots
            </SecondaryButton>
          </>
        ) : null}
      </EventSidebarFooter>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

FindHVACSalesSlotsForm.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default FindHVACSalesSlotsForm;
