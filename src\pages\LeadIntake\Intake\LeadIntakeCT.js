import React, { useEffect } from 'react';
import { useRecoilValue, useSetRecoilState, useRecoilState } from 'recoil';
import { useParams } from 'react-router-dom';

import { throwError } from '@utils/EventEmitter';
import validateRequiredParams from '@utils/validateRequiredParams';
import { activeTabIndexAtom, calendarTypeAtom } from '@recoil/app';
import { formValuesState, activeTabState } from '@recoil/dataIntakeForm';
import { selectedEventState } from '@recoil/eventSidebar';
import { showDuplicateRecordsAtom, duplicateLeadsAtom, stateAtom } from '@recoil/leadIntake';
import { hesAgentFormOptionsState } from '@recoil/agents';
import { SalesforceManager, AgentsManager } from '@utils/APIManager';
import IntakeContainer from './IntakeContainer';
import { getLeadIntakeRequiredFieldsCT as getLeadIntakeRequiredFields } from '../FormSchema/requiredFields';
import { useFieldsAndTabsForIntake } from '../utils/getters/useTabsAndFieldsForIntake';
import useAccountsCreation from '../utils/setters/useAccountsCreation2.0';
import useGetDuplicateLeads from '../utils/getters/useGetDuplicateLeads';

const LeadIntakeCT = () => {
  const { leadId } = useParams();
  const setActiveTab = useSetRecoilState(activeTabState);
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);
  const [activeTabIndex, setActiveTabIndex] = useRecoilState(activeTabIndexAtom(['tabs']));
  const formValues = useRecoilValue(formValuesState);
  const [state, setState] = useRecoilState(stateAtom);
  const requiredFields = getLeadIntakeRequiredFields(formValues);
  const setShowDuplicateRecordsTable = useSetRecoilState(showDuplicateRecordsAtom);
  const setDuplicateLeadsTableData = useSetRecoilState(duplicateLeadsAtom);
  const setCalendarType = useSetRecoilState(calendarTypeAtom);
  const setHesAgents = useSetRecoilState(hesAgentFormOptionsState);

  const { heaOrHvac, atAnEvent, isCampaignIdValid, isAuditorValueOnSf } = formValues;

  useEffect(() => {
    setState('CT');
    setCalendarType('010000');
    setSelectedEvent({ ...selectedEvent, type: '010000' }); // TODO: not sure if this is doing anything
    const getEmployees = async () => {
      try {
        const response = await AgentsManager.getCrewsByStateAndDepartment({ CT: ['HEA'] });
        setHesAgents(response?.crews || []);
        return response;
      } catch (err) {
        console.error(err);
        return throwError(err);
      }
    };
    getEmployees();
  }, []);

  const nextTab = async () => {
    if (activeTabIndex === 0) {
      fetchDuplicateAccounts();
    }
    setActiveTabIndex(activeTabIndex + 1);
    setActiveTab(leadIntakeTabs[activeTabIndex + 1]?.name);
  };

  const { leadIntakeTabs } = useFieldsAndTabsForIntake(
    heaOrHvac,
    atAnEvent,
    isCampaignIdValid,
    leadId,
    isAuditorValueOnSf,
    nextTab,
    'CT',
  );

  const validateNextTab = () => {
    if (requiredFields[leadIntakeTabs[activeTabIndex]?.name]) {
      const missingParams = validateRequiredParams(
        requiredFields[leadIntakeTabs[activeTabIndex]?.name],
      );
      if (missingParams.length)
        return throwError(`Error on following fields: ${missingParams.join(', ')}.`);
    }
    return nextTab();
  };

  const fetchDuplicateLeads = useGetDuplicateLeads('2.0');

  const fetchDuplicateAccounts = async () => {
    try {
      const duplicateAccounts = await SalesforceManager.getExistingDuplicateRecords(
        formValues,
        'Accounts',
        '2.0',
      );
      if (duplicateAccounts.length) {
        setDuplicateLeadsTableData(duplicateAccounts);
        setShowDuplicateRecordsTable(true);
      } else {
        return fetchDuplicateLeads();
      }
      return duplicateAccounts;
    } catch (error) {
      console.log(error);
      return throwError(error);
    }
  };

  const proceedWithAccountsCreation = useAccountsCreation(leadId, nextTab);

  const handleSubmit = async () => {
    try {
      return proceedWithAccountsCreation(formValues);
    } catch (err) {
      console.error(err);
      return throwError(err);
    }
  };

  if (!state) return null;

  return (
    <IntakeContainer
      state="CT"
      departments={['HEA']}
      handleSubmit={handleSubmit}
      validateNextTab={validateNextTab}
      nextTab={nextTab}
    />
  );
};

export default LeadIntakeCT;
