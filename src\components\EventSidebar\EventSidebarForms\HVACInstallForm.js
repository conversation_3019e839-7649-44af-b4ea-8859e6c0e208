import React, { Suspense, useEffect, useState } from 'react';
import PropTypes from 'prop-types';

import moment from 'moment';
import { useRecoilState, useSetRecoilState, useRecoilValue } from 'recoil';

import { decodeEventType } from '@homeworksenergy/utility-service';

import useStartEndTimes from '@hooks/useStartEndTimes';

import { selectedEventState, isSchedulingViewAtom, schedulingTypeAtom } from '@recoil/eventSidebar';
import { allAgentsFormOptionsState } from '@recoil/agents';
import { eventTypesByOidSelectorFamily } from '@recoil/app';

import { DropDownMenu } from '@components/global';
import ThreeVerticalDots from '@components/global/Icons/ThreeVerticalDots';
import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import {
  Row,
  Col,
  FormInput,
  handleFormFieldChange,
  FormTextBox,
  FormSelect,
  AddRemoveButtonContainer,
  FormInfoField,
  FormInfo,
  FormStartEndDateTimePickers,
  FormAddRemoveDayButtons,
} from '@components/global/Form';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarHeader, {
  HeaderLabel,
  HeaderTitle,
} from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';

import { isAuthorized } from '@utils/AuthUtils';
import { UsersManager } from '@utils/APIManager';
import {
  openPhoneCall,
  openEmailClient,
  openNewTabWithUrl,
  getSalesforceUrl,
  displayPhoneNumber,
  getGoogleMapsDirectionsUrl,
  getEventDuration,
} from '@utils/functions';

const HVACInstallForm = ({ handleCancelClick, handleSaveClick }) => {
  const [installer, setInstaller] = useState('');
  const [event, setEvent] = useRecoilState(selectedEventState);
  const setIsSchedulingView = useSetRecoilState(isSchedulingViewAtom);
  const setSchedulingType = useSetRecoilState(schedulingTypeAtom);
  const agents = useRecoilValue(allAgentsFormOptionsState);

  const {
    id,
    sfIds,
    customerName,
    startTime,
    endTime,
    numUnit,
    type,
    eventTypeName,
    address,
    phoneNumber,
    email,
    notes,
    oid,
    oids,
    equipmentOrderStatus,
    date,
    scheduledBy,
    scheduledDate,
    jobLength,
    startEndTimes = [], // [{start: '', end: ''}, {start: '', end: ''}]
    projectManager,
    concierge,
  } = event;

  const isCreate = !id;

  const eventTypesForUser = useRecoilValue(eventTypesByOidSelectorFamily(oid || oids[0]));

  let defaultType = null;
  if (oid || oids[0])
    defaultType = eventTypesForUser?.filter(({ value }) => {
      return value === '000433';
    })?.[0]?.value;

  const createEventTypes =
    type?.slice(0, 4) === '0004' || defaultType
      ? [{ key: '', value: type || defaultType }]
      : [{ key: 'Custom Block', value: '999999' }];

  const getEventUsersCompany = async (eventOwnerOid) => {
    const { company } = await UsersManager.getUserInfo(eventOwnerOid);
    return company;
  };

  useEffect(() => {
    const getInstaller = async () => {
      const installer = await getEventUsersCompany(oid || oids[0]);
      setInstaller(installer);
    };

    if (!oid && !oids[0]) return;
    getInstaller();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [oid, oids[0]]);

  useEffect(() => {
    if (!date || !isCreate || type) return; // If no type for create event, push to the returnVisit form

    if (isCreate) setEvent({ ...event, type: createEventTypes[0].value });
  }, [date, createEventTypes, type, isCreate, setEvent, event]);

  // For some reason, this needs to be below the above useEffect, or else its value gets overwritten with the previous event value
  const handleTimeChange = useStartEndTimes();

  if (!type) return null;
  const eventDuration = getEventDuration(startTime, endTime);

  const { state, business } = decodeEventType(type);
  const eventDepartment = business.replace(' ', '_');

  const canEdit = isAuthorized('Scheduler', 'All');

  const isCreateEvent = !id;

  const salesForceUrl = getSalesforceUrl();

  const uniqueId = sfIds?.contractId || sfIds.accountId;

  const agent = agents.find((agent) => [oid, oids[0]].includes(agent?.value));

  const agentName = agent?.key;

  const dropDownValues = [
    {
      text: 'Doc Repo link',
      onClick: () => openDocRepoTab(`/doc-repo/${state}/${eventDepartment}/${uniqueId}`),
    },
  ];

  const openDocRepoTab = async (url) => {
    openNewTabWithUrl(`${url}/${installer}`);
  };

  const handleFieldChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  const handleSchedulingActionClick = (action) => {
    setSchedulingType(action);
    setIsSchedulingView(true);
  };

  const orderStatusOptions = [
    'Not Ordered',
    'Backordered',
    'Partially Delivered',
    'Ordered',
    'Order Confirmed',
    'Delivered',
  ].map((option) => {
    return { key: option, value: option };
  });

  const formattedStartDate = moment(date).format('M/D/YYYY');
  const formattedEndDate =
    jobLength > 1
      ? moment(startEndTimes?.[startEndTimes.length - 1]?.start).format('M/D/YYYY')
      : null;

  const formattedDate = `${formattedStartDate}${formattedEndDate ? ` - ${formattedEndDate}` : ''}`;

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col size={2}>
              <HeaderLabel>Customer Name:</HeaderLabel>
              <HeaderTitle>{customerName}</HeaderTitle>
              <HeaderLabel>{`Date: ${formattedDate}`}</HeaderLabel>
            </Col>
            <Col size={1} right>
              <HeaderLabel>Duration: {eventDuration} hours</HeaderLabel>
              <HeaderLabel>Number of Units: {numUnit}</HeaderLabel>
              <HeaderLabel>
                {`Time: ${moment(startTime, 'HH:mm:ss').format('h:mm')} - ${moment(
                  endTime,
                  'HH:mm:ss',
                ).format('h:mm')}`}
              </HeaderLabel>
            </Col>
            {!isCreateEvent && uniqueId && (
              <Col size={0} left>
                <DropDownMenu DropDownIcon={<ThreeVerticalDots />} listItems={dropDownValues} />
              </Col>
            )}
          </Row>
        </EventSidebarHeader>
        <EventSidebarBody>
          <Row>
            <Col>
              <FormInput
                readOnly
                name="eventTypeName"
                value={eventTypeName}
                title="Event Type"
                placeholder="Event Type"
              />
              <FormInput
                readOnly
                name="address"
                value={address?.displayAddress}
                title="Address"
                onClick={() =>
                  openNewTabWithUrl(getGoogleMapsDirectionsUrl(address?.displayAddress))
                }
                placeholder="Address"
              />
              <FormInput
                readOnly
                name="agentName"
                value={agentName}
                title="Crew Name"
                placeholder="Crew Name"
              />
              {installer && (
                <FormInput
                  readOnly
                  name="installer"
                  value={installer}
                  title="Installer"
                  placeholder="Installer"
                />
              )}
            </Col>
          </Row>
          <Row>
            <Col>
              <FormInput
                readOnly
                name="phoneNumber"
                value={displayPhoneNumber(phoneNumber)}
                title="Phone Number"
                onClick={() => openPhoneCall(phoneNumber)}
                placeholder="Phone Number"
              />
              <FormInput
                readOnly
                name="sfIds.accountId"
                value={`${sfIds.accountId}`}
                title="SF Account Id"
                onClick={() => openNewTabWithUrl(`${salesForceUrl}${sfIds.accountId}`)}
                placeholder="SF Account Id"
              />
              <FormInput
                readOnly
                name="projectManager"
                value={projectManager}
                title="Project Manager"
                placeholder="Project Manager"
              />
              <FormTextBox
                name="notes.officeNotes"
                value={notes.officeNotes}
                title="Office Notes"
                onChange={handleFieldChange}
                disabled={canEdit}
                placeholder="Office Notes"
              />
              <FormSelect
                required
                name="equipmentOrderStatus"
                value={equipmentOrderStatus}
                title="Order Status"
                placeholder="Order Status"
                onChange={handleFieldChange}
                options={orderStatusOptions}
              />
              {!isCreateEvent && (
                <FormInfo>
                  <FormInfoField title="Scheduled By :" body={scheduledBy} />
                  <FormInfoField
                    title="Scheduled On :"
                    body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
                  />
                </FormInfo>
              )}
            </Col>
            <Col>
              <FormInput
                readOnly
                name="email"
                value={`${email}`}
                title="Email"
                onClick={() => openEmailClient(email)}
                placeholder="Email"
              />
              {sfIds?.contractId ? (
                <FormInput
                  readOnly
                  name="sfIds.contractId"
                  value={`${sfIds.contractId}`}
                  title="SF Contract Id"
                  onClick={() => openNewTabWithUrl(`${salesForceUrl}${sfIds.contractId}`)}
                  placeholder="SF Contract Id"
                />
              ) : (
                !isCreateEvent && (
                  <FormInput
                    readOnly
                    name="sfIds.workVisitId"
                    value={`${sfIds.workVisitId}`}
                    title="SF Work Visit Id"
                    onClick={() => openNewTabWithUrl(`${salesForceUrl}${sfIds.workVisitId}`)}
                    placeholder="SF Work Visit Id"
                  />
                )
              )}
              <FormInput
                readOnly
                name="concierge"
                value={concierge}
                title="Concierge"
                placeholder="Concierge"
              />
              <FormTextBox
                name="notes.fieldNotes"
                value={notes.fieldNotes}
                title="Field Notes"
                onChange={handleFieldChange}
                disabled={canEdit}
                placeholder="Field Notes"
              />
              <FormTextBox
                name="notes.equipmentOrderNotes"
                value={notes.equipmentOrderNotes}
                title="Order Notes"
                onChange={handleFieldChange}
                disabled={canEdit}
                placeholder="Order Notes"
              />
              <FormStartEndDateTimePickers
                allowDateSelect={false}
                startEndTimes={startEndTimes}
                onChange={handleTimeChange}
              />
              <AddRemoveButtonContainer>
                <FormAddRemoveDayButtons
                  name="jobLength"
                  value={jobLength}
                  onChange={handleFieldChange}
                  amount={1}
                  testid="1"
                >
                  1 Day
                </FormAddRemoveDayButtons>
                <FormAddRemoveDayButtons
                  name="jobLength"
                  value={jobLength}
                  onChange={handleFieldChange}
                  amount={0.5}
                  testid="0-5"
                >
                  1/2 Day
                </FormAddRemoveDayButtons>
                <FormAddRemoveDayButtons
                  amount={0.25}
                  value={jobLength}
                  name="jobLength"
                  onChange={handleFieldChange}
                  testid="0-25"
                >
                  1/4 Day
                </FormAddRemoveDayButtons>
              </AddRemoveButtonContainer>
            </Col>
          </Row>
        </EventSidebarBody>
        <EventSidebarFooter
          leftButtons={
            isAuthorized('Scheduler', 'HVAC-Install') && canEdit ? (
              <>
                <PrimaryButton onClick={() => handleSaveClick()}>Save</PrimaryButton>
                {!isCreate && (
                  <CancelButton onClick={() => handleCancelClick()}>Cancel Event</CancelButton>
                )}
              </>
            ) : null
          }
          rightButtons={
            isAuthorized('Scheduler', 'HVAC-Install') && canEdit && !isCreateEvent ? (
              <>
                <PrimaryButton onClick={() => handleSchedulingActionClick('reschedule')}>
                  Reschedule
                </PrimaryButton>
                <PrimaryButton onClick={() => handleSchedulingActionClick('reassign')}>
                  Reassign
                </PrimaryButton>
              </>
            ) : null
          }
        />
      </Suspense>
    </SidebarForm>
  );
};

HVACInstallForm.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default HVACInstallForm;
