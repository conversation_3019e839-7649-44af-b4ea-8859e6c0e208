/* eslint-disable react/jsx-indent-props */
import React, { useState, useEffect, useRef } from 'react';
import { useRecoilValue, useSetRecoilState, useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import _ from 'lodash';
import { isHWEUser } from '@homeworksenergy/utility-service';

import { programOptions } from '@utils/constants';
import { parseGoogleAutocomplete } from '@utils/functions';
import { isAuthorized } from '@utils/AuthUtils';

import {
  Container,
  Row,
  Col,
  FormInput,
  FormSelect,
  handleFormFieldChange,
  FormCheckboxes,
  GoogleAddressInput,
  FormMultiselect,
} from '@components/global/Form';
import {
  regionsSelector,
  companiesSelector,
  authorizedDepartmentsSelector,
  statesSelector,
} from '@recoil/app';
import { selectedAgentAtom } from '@recoil/admin/agents';
import { adminInfoChangesState } from '@recoil/admin';
import { agentAddressesAreValidAtom } from '@recoil/agents';
import { Checkbox } from '@components/global';

const ProgramAttributesContainer = styled.div`
  margin-bottom: 1.5em;
`;

const Title = styled.div`
  font-weight: 500;
  font-size: 20px;
`;

const EditAgentInfoForm = ({ title = '' }) => {
  const [agent, setAgent] = useRecoilState(selectedAgentAtom);
  const {
    firstname,
    lastname,
    displayName = '',
    department,
    region,
    regionId,
    company,
    state,
    number,
    sfId,
    sfId2,
    isReserve,
    isManager,
    homeAddress,
    dayStartAddress,
    programs,
    travelTime,
    travelTimeTotal,
    eventTypes,
    phoneNumber,
    email,
    sendNotification,
    notificationChannel = [],
    personalEmail,
    agentType,
  } = agent;

  const isPartner = [8, 9].includes(department);
  const isHVACAgent = department === 3;
  const hweUser = isHWEUser({ company });
  const isHVACPartner = !hweUser && isHVACAgent;
  const displayAddress =
    [1, 2, 12].includes(department) || _.intersection(eventTypes, ['000433', '000434']).length > 0;

  const labelsAndPlaceholdersByDept = {
    // Insulation
    6: {
      managerCheckboxLabel: 'Manager truck?',
      reserveCheckboxLabel: 'Reserve truck?',
      displayNumberLabel: 'Truck Number',
      displayNamePlaceholder: 'Enter Truck Number',
    },
    // HVAC-Install
    3: {
      managerCheckboxLabel: 'Manager truck?',
      reserveCheckboxLabel: 'Reserve truck?',
      displayNumberLabel: 'List Order',
      displayNamePlaceholder: 'Enter List Order',
    },
    default: {
      managerCheckboxLabel: 'Is Manager?',
      reserveCheckboxLabel: 'Is Reserve?',
      displayNumberLabel: 'List Order',
      displayNamePlaceholder: 'Enter List Order',
    },
  };

  const notificationChannelOptions = [
    { key: `HWE Email (${email})`, value: email },
    { key: `Personal Email (${personalEmail})`, value: personalEmail },
    { key: `Phone Number (${phoneNumber})`, value: phoneNumber },
  ].filter((channel) => {
    return channel?.value;
  });

  const agentTypeOptions = [
    { key: 'Electrician', value: 'electrician' },
    { key: 'Installer', value: 'installer' },
    { key: 'Installer / Electrician', value: 'installerNElectrician' },
    { key: 'Plumbing Installer', value: 'plumbingInstaller' },
    { key: 'Project Manager', value: 'projectManager' },
  ];

  const regionIdOptions = [
    { key: 'North Shore', value: 100 },
    { key: 'South Shore', value: 200 },
    { key: 'Metro West', value: 300 },
    { key: 'Western MA', value: 400 },
    { key: 'Cape Cod', value: 500 },
  ];

  const prevState = useRef();
  const regionsOptions = useRecoilValue(regionsSelector);
  const companyOptions = useRecoilValue(companiesSelector(state));
  const statesOptions = useRecoilValue(statesSelector);
  const [homeAddressInvalid, setHomeAddressInvalid] = useState(false);
  const [dayStartAddressInvalid, setDayStartAddressInvalid] = useState(false);
  // This is used in the Details.js file to determine if addresses are valid to save
  const setAgentsAddressesAreValid = useSetRecoilState(agentAddressesAreValidAtom);
  const setAgentInfoChanges = useSetRecoilState(adminInfoChangesState);

  const regionsByStateOptions = regionsOptions.filter(({ state: regionState }) => {
    return regionState === state;
  });

  useEffect(() => {
    prevState.agent = agent;
  }, [agent]);

  const handleFieldChange = (e, updatedAgent = prevState.agent) => {
    const { name } = e.target;
    let { value } = e.target;

    handleFormFieldChange(e, updatedAgent, setAgent);
    // stores only those values in an atom whose value is changed
    if (value?.[0]?.value) value = value.map((value) => value.value);

    // Only track regionId changes if the value actually changed
    if (name === 'regionId') {
      const currentValue = (agent.regionId || []).slice().sort();
      const newValue = (value || []).slice().sort();
      // Compare arrays - if they're the same, don't track the change
      if (JSON.stringify(currentValue) === JSON.stringify(newValue)) {
        return;
      }
      setAgentInfoChanges({ name, value });
      return;
    }

    setAgentInfoChanges({ name, value });
  };

  // Only used for address change for google auto complete
  const handleAddressChange = (e) => {
    const { name, value } = e.target;
    handleFieldChange(e);
    setAgentInfoChanges({ name, value });
    if (name === 'homeAddress') setHomeAddressInvalid(true);
    if (name === 'dayStartAddress') setDayStartAddressInvalid(true);
    setAgentsAddressesAreValid(false);
  };

  // Used when you click an address in the google auto complete
  const handleGoogleLocationInput = (autocomplete, name) => {
    const updateObject = parseGoogleAutocomplete(autocomplete);

    handleFieldChange({ target: { name, value: updateObject } });
    setAgentInfoChanges({ name, value: updateObject });
    if (name === 'homeAddress') setHomeAddressInvalid(false);
    if (name === 'dayStartAddress') setDayStartAddressInvalid(false);
    setAgentsAddressesAreValid(true);
  };

  return (
    <Container>
      {title ? <Title>Crew Information</Title> : null}
      <Row>
        <Col size={1}>
          <FormInput
            required
            title="First name"
            placeholder="Enter Firstname"
            name="firstname"
            value={firstname}
            onChange={handleFieldChange}
          />
          <FormInput
            required
            title="Display Name"
            placeholder="Enter display name"
            name="displayName"
            value={displayName}
            onChange={handleFieldChange}
          />
          <FormSelect
            required
            title="Department"
            type="number" // number type since this is returning dept id
            placeholder="Select Department"
            name="department"
            value={department}
            onChange={handleFieldChange}
            recoilOptions={authorizedDepartmentsSelector}
          />
          <FormInput
            title="Primary Number"
            placeholder="Enter Primary Number, Format: 1234567897"
            name="phoneNumber"
            type="tel"
            maxLength={10}
            value={phoneNumber}
            onChange={handleFieldChange}
          />
          {displayAddress && (
            <>
              <GoogleAddressInput
                title="Home Address"
                name="homeAddress"
                value={homeAddress}
                onPlaceChange={(autocomplete) =>
                  handleGoogleLocationInput(autocomplete, 'homeAddress')
                }
                onChange={handleAddressChange}
                isInvalid={homeAddressInvalid}
                uniqueId="homeAddress"
                required
              />
              <FormInput
                required
                title="One-way Drive Time Limit"
                placeholder="Enter One-way Drive Time Limit"
                name="travelTime"
                value={travelTime}
                onChange={handleFieldChange}
              />
            </>
          )}
          <FormSelect
            required
            title="Company Name"
            placeholder="Enter Company Name"
            name="company"
            value={company}
            onChange={handleFieldChange}
            options={companyOptions}
          />
          {!isPartner && (
            <>
              <FormInput
                title={
                  labelsAndPlaceholdersByDept?.[department]?.displayNumberLabel ||
                  labelsAndPlaceholdersByDept.default.displayNumberLabel
                }
                placeholder={
                  labelsAndPlaceholdersByDept?.[department]?.displayNamePlaceholder ||
                  labelsAndPlaceholdersByDept.default.displayNamePlaceholder
                }
                name="number"
                value={number || ''}
                type="number"
                onChange={handleFieldChange}
              />
              <Checkbox
                required
                label={
                  labelsAndPlaceholdersByDept?.[department]?.reserveCheckboxLabel ||
                  labelsAndPlaceholdersByDept.default.reserveCheckboxLabel
                }
                name="isReserve"
                value={isReserve}
                onChange={handleFieldChange}
              />
              <Checkbox
                required
                label={
                  labelsAndPlaceholdersByDept?.[department]?.managerCheckboxLabel ||
                  labelsAndPlaceholdersByDept.default.managerCheckboxLabel
                }
                name="isManager"
                value={isManager}
                onChange={handleFieldChange}
              />
            </>
          )}
        </Col>
        <Col size={1}>
          <FormInput
            required
            title="Last name"
            placeholder="Enter Lastname"
            name="lastname"
            value={lastname}
            onChange={handleFieldChange}
          />
          <FormSelect
            required
            title="States"
            placeholder="Select States"
            name="state"
            value={state}
            onChange={handleFieldChange}
            options={statesOptions}
          />
          {!isPartner && (
            <FormSelect
              required
              title="Regions"
              placeholder="Select Regions"
              name="region"
              value={Number(region)}
              onChange={handleFieldChange}
              options={regionsByStateOptions}
            />
          )}

          {isAuthorized('Super User', 'Partners', false, 'MA') && isPartner && (
            <FormMultiselect
              title="Regions"
              placeholder="Select Regions"
              name="regionId"
              value={regionId || []}
              onChange={handleFieldChange}
              options={regionIdOptions}
            />
          )}

          <FormInput
            required
            readOnly
            title="HWE Email"
            name="email"
            value={email}
            onChange={handleFieldChange}
          />

          {displayAddress && (
            <>
              <GoogleAddressInput
                title="Day Start Address"
                name="dayStartAddress"
                uniqueId="dayStartAddress"
                value={dayStartAddress}
                onPlaceChange={(autocomplete) =>
                  handleGoogleLocationInput(autocomplete, 'dayStartAddress')
                }
                onChange={handleAddressChange}
                isInvalid={dayStartAddressInvalid}
                required
              />
              <FormInput
                required
                title="Round-trip Drive Time Limit"
                placeholder="Enter Round-trip Drive Time Limit"
                name="travelTimeTotal"
                value={travelTimeTotal}
                onChange={handleFieldChange}
              />
            </>
          )}
          {isHVACPartner && (
            <FormInput
              title="Personal Email"
              name="personalEmail"
              value={personalEmail}
              onChange={handleFieldChange}
            />
          )}
          {isHVACAgent && (
            <FormSelect
              required
              title="Agent Type"
              placeholder="Select Agent Type"
              name="agentType"
              value={agentType}
              onChange={handleFieldChange}
              options={agentTypeOptions}
            />
          )}
          {!isPartner && (
            <>
              <FormInput
                title="Salesforce 1.0 ID"
                placeholder="User or Employee ID"
                name="sfId"
                value={sfId}
                onChange={handleFieldChange}
              />
              <FormInput
                title="Salesforce 2.0 ID"
                placeholder="User or Employee ID"
                name="sfId2"
                value={sfId2}
                onChange={handleFieldChange}
              />
            </>
          )}
          {isHVACPartner && (
            <>
              <Checkbox
                required
                label="Send Event Status Notification"
                name="sendNotification"
                value={sendNotification}
                onChange={handleFieldChange}
              />
              {sendNotification && (
                <FormMultiselect
                  required
                  title="Notification Channel"
                  name="notificationChannel"
                  options={notificationChannelOptions}
                  onChange={handleFieldChange}
                  value={notificationChannel}
                />
              )}
            </>
          )}
        </Col>
      </Row>
      {department === 1 && state === 'MA' && (
        <ProgramAttributesContainer>
          <Row>
            <Col size={1}>
              <FormCheckboxes
                title="programs"
                name="programs"
                options={programOptions}
                value={programs}
                onChange={handleFieldChange}
                border
              />
            </Col>
          </Row>
        </ProgramAttributesContainer>
      )}
    </Container>
  );
};

EditAgentInfoForm.propTypes = {
  title: PropTypes.string,
};

export default EditAgentInfoForm;
