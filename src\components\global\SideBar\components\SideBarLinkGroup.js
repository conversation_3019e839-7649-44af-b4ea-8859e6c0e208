import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import Clickable from '@components/global/Clickable';

const StyledTitle = styled.div``;
const StyledSidebarCell = styled(Clickable)`
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 10px 15px;
  user-select: none;
  cursor: pointer;
  &.cell {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 10px 15px;
    user-select: none;
    cursor: pointer;
  }
`;
const StyledLinkedGroup = styled.div`
  display: flex;
  flex-direction: column;
`;
const StyledArrow = styled.div`
  transition: 0.3s;
  &.down {
    transform: rotate(90deg);
  }
`;
const StyledLinks = styled.div`
  height: 0px;
  margin-left: 10px;
  overflow: hidden;
  transition: 0.3s;
`;

const SideBarLinkGroup = (props) => {
  const { children, title, linkId } = props;
  const [isOpen, setIsOpen] = useState(false);

  const toggleGroup = () => {
    const [linksDomNode] = document.getElementsByClassName(linkId);
    const linkGroup = linksDomNode.closest('.side-bar-link-group');
    if (isOpen) {
      linksDomNode.style.height = '0px';
    } else {
      linksDomNode.style.height = `${linksDomNode.scrollHeight}px`;

      // When the link group is nested, set the parent height to auto so when it opens it is visible
      let parentLinkGroup = linkGroup.parentElement.closest('.side-bar-link-group');
      while (parentLinkGroup) {
        const [parentLinks] = parentLinkGroup.getElementsByClassName('links');
        parentLinks.style.height = 'auto';
        parentLinkGroup = parentLinkGroup.parentElement.closest('.side-bar-link-group');
      }
    }

    setIsOpen(!isOpen);
  };

  // Filter out nulls for routes that are unauthorized for the user
  // Route gets set to "null" in the renderMenuItems function in SideBar.js if they're not authorized
  if (!children.filter((child) => child).length) return null;

  return (
    <StyledLinkedGroup className="side-bar-link-group">
      <StyledSidebarCell className="cell" onClick={toggleGroup}>
        <StyledTitle className="title">{title}</StyledTitle>
        <StyledArrow
          className={`glyphicon glyphicon-chevron-right arrow ${isOpen ? 'down' : ''}`}
        />
      </StyledSidebarCell>
      <StyledLinks className={`links ${linkId}`}>{children}</StyledLinks>
    </StyledLinkedGroup>
  );
};

SideBarLinkGroup.propTypes = {
  children: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.node), PropTypes.shape({})])
    .isRequired,
  title: PropTypes.string.isRequired,
  linkId: PropTypes.string.isRequired,
};

export default SideBarLinkGroup;
