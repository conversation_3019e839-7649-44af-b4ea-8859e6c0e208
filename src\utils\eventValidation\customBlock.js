/* eslint-disable no-unused-vars */
import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';

const create = (params) => {
  const {
    eventName,
    startTime,
    endTime,
    date,
    oids,
    type,
    jobLength,
    startEndTimes,
    notes,
    shadow,
    address,
  } = params;

  const requiredFields = {
    'Event name': eventName,
    'Start Time': startTime,
    'End Time': endTime,
    Date: date,
    'HES or Crew': oids,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  // TODO: multiple day events

  const customBlockData = {
    eventName,
    startTime,
    endTime,
    date,
    oids,
    type,
    jobLength,
    shadow,
    startEndTimes,
    notes,
    address,
  };

  return customBlockData;
};
const update = (params) => {
  const {
    eventName,
    startTime,
    endTime,
    date,
    oids,
    type,
    jobLength,
    startEndTimes,
    notes,
    associatedEventsId,
    associatedEventIds,
    shadow,
    address,
  } = params;

  const requiredFields = {
    eventName,
    startTime,
    endTime,
    date,
    oids,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  // TODO: multiple day events

  const customBlockData = {
    eventName,
    startTime,
    endTime,
    date,
    oids,
    type,
    jobLength,
    shadow,
    startEndTimes,
    associatedEventsId,
    associatedEventIds,
    notes,
    address,
  };

  return customBlockData;
};

const reschedule = (params) => params;

const cancel = (params) => true;

const customBlock = { create, update, cancel, reschedule };
export default customBlock;
