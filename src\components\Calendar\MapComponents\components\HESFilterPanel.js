import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilValue } from 'recoil';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { SecondaryButton } from '@components/global/Buttons';
import { searchAdminState } from '@recoil/admin';
import HESAgentSearchBox from './HESAgentSearchBox';
import ScheduleCard from './ScheduleCard';
import {
  FilterContainer,
  FilterTitle,
  SearchBarWrapper,
  FilterActions,
  FilterButton,
  HESFilterItem,
  ColorIndicator,
  HESName,
  ScrollableAgentList,
} from '../HEAMapViewStyles';

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 6px;
`;

const LegendToggleButton = styled.button`
  width: 100%;
  padding: 8px;
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: 11px;
  font-weight: 600;
  border: 1px solid ${({ theme }) => theme.colors.calendarBorder};
  background: ${({ theme }) => theme.secondary[100]};
  color: ${({ theme }) => theme.colors.formText};
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;

  &:hover {
    background: ${({ theme }) => theme.secondary[200]};
  }
`;

const LegendButtonText = styled.span`
  flex: 1;
  text-align: left;
`;

const LegendButtonIconWrapper = styled.span`
  font-size: 10px;
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
`;

const LegendContent = styled.div`
  display: ${({ isOpen }) => (isOpen ? 'flex' : 'none')};
  flex-direction: column;
  gap: 6px;
  padding: 8px;
  background: ${({ theme }) => theme.secondary[50]};
  border: 1px solid ${({ theme }) => theme.secondary[200]};
  border-radius: 4px;
  margin-bottom: 8px;
`;

const LegendItemRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 10px;
`;

const LegendNumberCircle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #4a90e2;
  color: white;
  border-radius: 50%;
  font-weight: bold;
  font-size: 10px;
  flex-shrink: 0;
`;

const LegendTimeText = styled.div`
  font-size: 10px;
  color: ${({ theme }) => theme.secondary[600]};
`;

/** HES Filter Panel Component - Displays filter controls and agent list */
const HESFilterPanel = ({
  hesAgents,
  activeFilters,
  selectedHesOid,
  onToggleFilter,
  onSelectAll,
  onDeselectAll,
  onReset,
  onRefresh,
}) => {
  const [isLegendOpen, setIsLegendOpen] = useState(false);

  // Get search term from Recoil
  const searchTerm = useRecoilValue(searchAdminState);

  // Filter agents based on search term and exclude agents with 0 appointments
  const filteredHesAgents = React.useMemo(() => {
    if (!searchTerm) {
      return hesAgents.filter((agent) => agent.appointmentCount > 0);
    }
    return hesAgents.filter((agent) => {
      if (agent.appointmentCount === 0) return false;
      const searchableText = [
        agent.name,
        agent.displayName,
        agent.firstname,
        agent.lastname,
        `${agent.firstname} ${agent.lastname}`.trim(),
      ]
        .filter(Boolean)
        .join(' ')
        .toLowerCase();
      return searchableText.includes(searchTerm.toLowerCase());
    });
  }, [searchTerm, hesAgents]);

  const timeSlots = [
    { number: 1, time: '8:00 - 9:59 AM' },
    { number: 2, time: '10:00 - 11:59 AM' },
    { number: 3, time: '12:00 - 1:59 PM' },
    { number: 4, time: '2:00 - 3:59 PM' },
    { number: 5, time: '4:00 - 5:59 PM' },
    { number: 6, time: '6:00 - 7:59 PM' },
  ];

  // Handle agent selection from dropdown
  const handleAgentSelect = (agent) => {
    // When an agent is selected from dropdown, toggle their filter
    onToggleFilter(agent.oid);
  };

  return (
    <FilterContainer>
      <TitleWrapper>
        <FilterTitle>
          HES Agents ({filteredHesAgents.length}
          {searchTerm ? ` of ${hesAgents.length}` : ''})
        </FilterTitle>
        <ButtonGroup>
          <SecondaryButton shrink onClick={onDeselectAll}>
            Deselect All
          </SecondaryButton>
          {onRefresh && (
            <SecondaryButton shrink onClick={onRefresh}>
              Refresh
            </SecondaryButton>
          )}
        </ButtonGroup>
      </TitleWrapper>
      <SearchBarWrapper>
        <HESAgentSearchBox
          hesAgents={hesAgents}
          placeholder="Search or select HES agents..."
          onAgentSelect={handleAgentSelect}
          activeFilters={activeFilters}
        />
      </SearchBarWrapper>

      <LegendToggleButton onClick={() => setIsLegendOpen(!isLegendOpen)}>
        <LegendButtonText>Time Slot Legend</LegendButtonText>
        <LegendButtonIconWrapper>
          <FontAwesomeIcon icon={isLegendOpen ? faChevronDown : faChevronRight} />
        </LegendButtonIconWrapper>
      </LegendToggleButton>

      <LegendContent isOpen={isLegendOpen}>
        {timeSlots.map((slot) => (
          <LegendItemRow key={slot.number}>
            <LegendNumberCircle>{slot.number}</LegendNumberCircle>
            <LegendTimeText>{slot.time}</LegendTimeText>
          </LegendItemRow>
        ))}
      </LegendContent>

      {searchTerm && (
        <>
          <FilterActions>
            <FilterButton onClick={onSelectAll}>All</FilterButton>
            <FilterButton onClick={onDeselectAll}>None</FilterButton>
            <FilterButton onClick={onReset}>Reset</FilterButton>
          </FilterActions>

          <ScrollableAgentList>
            {filteredHesAgents.map((agent) => (
              <div key={agent.oid}>
                <HESFilterItem onClick={() => onToggleFilter(agent.oid)}>
                  <ColorIndicator color={agent.color} active={activeFilters.has(agent.oid)} />
                  <HESName active={activeFilters.has(agent.oid)}>
                    {agent.name} ({agent.appointmentCount})
                  </HESName>
                </HESFilterItem>
                {selectedHesOid === agent.oid && (
                  <ScheduleCard agent={agent} selectedHesOid={selectedHesOid} />
                )}
              </div>
            ))}
          </ScrollableAgentList>
        </>
      )}
    </FilterContainer>
  );
};

HESFilterPanel.propTypes = {
  hesAgents: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  activeFilters: PropTypes.instanceOf(Set).isRequired,
  selectedHesOid: PropTypes.string,
  onToggleFilter: PropTypes.func.isRequired,
  onSelectAll: PropTypes.func.isRequired,
  onDeselectAll: PropTypes.func.isRequired,
  onReset: PropTypes.func.isRequired,
  onRefresh: PropTypes.func,
};

export default HESFilterPanel;
