import styled from 'styled-components';

const StyledListHeader = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: ${({ theme }) => theme.secondary[500]};
  padding: 4px 0;
  width: 100%;
  border-bottom: 1px solid ${({ theme }) => theme.secondary[300]};
  color: ${({ theme }) => theme.colors.regionHeader};
`;

export default StyledListHeader;
