import { useRecoilValue, useSetRecoilState } from 'recoil';
import { throwError } from '@utils/EventEmitter';
import swal from 'sweetalert2/dist/sweetalert2';

import { SalesforceManager } from '@utils/APIManager';
import { activeTabIndexAtom } from '@recoil/app';
import { availableSlotsAtom } from '@recoil/eventSidebar';
import { formValuesState, activeTabState } from '@recoil/dataIntakeForm';
import {
  selectedLeadIdAtom,
  duplicateRecordsTypeAtom,
  showDuplicateRecordsAtom,
} from '@recoil/leadIntake';
import { useGetNextAvailableSlots } from '../getters/useGetNextAvailableSlots';
import { useSalesforceAccountsCreationAndDetails } from '../getters/useSalesforceAccountsCreationAndDetails';
import { duplicateTableSteps } from '../consts';

const useAccountsCreation = (leadId, nextTab) => {
  const formValues = useRecoilValue(formValuesState);
  const selectedLeadId = useRecoilValue(selectedLeadIdAtom);
  const setAvailableSlots = useSetRecoilState(availableSlotsAtom);
  const setFormValues = useSetRecoilState(formValuesState);
  const setTableStep = useSetRecoilState(duplicateRecordsTypeAtom);
  const setActiveTab = useSetRecoilState(activeTabState);
  const setActiveTabIndex = useSetRecoilState(activeTabIndexAtom(['tabs']));
  const setShowDuplicateRecordsTable = useSetRecoilState(showDuplicateRecordsAtom);

  const { getNextAvailableSlots } = useGetNextAvailableSlots();
  const { getDealsDetails } = useSalesforceAccountsCreationAndDetails();

  const { customerAddress, numUnitsSchedulingToday, siteId, secondaryLeadVendor } = formValues;

  const fetchSiteIdDuplicationStatus = async (list) => {
    const checkedSiteIds = await SalesforceManager.checkSiteIdDuplication({ siteIds: list });
    const duplicateSiteIds = [];
    if (Array.isArray(siteId)) {
      siteId.forEach((id) => {
        if (checkedSiteIds[id]) {
          duplicateSiteIds.push(id);
        }
      });
    }
    if (duplicateSiteIds.length) {
      swal.fire({
        title: 'Duplicate Site ID(s)',
        text: `The following ID(s) are duplicates in Salesforce: ${duplicateSiteIds.join(
          ', ',
        )}. Please correct or remove duplicate site ids`,
        icon: 'error',
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      setActiveTabIndex(2);
      setActiveTab('sourceInfo');
      return true;
    }
    return false;
  };

  // This callback is called when users is on Review Section Page
  // This callback is responsible for update/converting lead if lead is selected from
  // Duplicate Lead table, If not then create Parent and SubAccounts on SF,
  // On Success Anyone of two conditions, Insert User into DB
  // Then we call available slots to get slots and show on Schedule Event Component
  // And we set formIsSubmitted true so User can't call this callback again
  const proceedWithAccountsCreation = async (updatedFormValues) => {
    try {
      let accountIdList = [];
      const leadIdForConversion = selectedLeadId || leadId;
      const isSingleFamWithLead = Number(numUnitsSchedulingToday) === 1 && leadIdForConversion;
      if (isSingleFamWithLead) {
        const updateLeadResponse = await SalesforceManager.updateLead({
          ...updatedFormValues,
          selectedLeadId: leadIdForConversion,
          Id: leadIdForConversion,
          ...customerAddress,
        });
        if (updateLeadResponse?.error) return throwError(updateLeadResponse?.error);
        const convertedLeadResponse = await SalesforceManager.convertLead(leadIdForConversion);
        if (convertedLeadResponse?.errors?.length) {
          setActiveTabIndex(1);
          setActiveTab('customerInfo');
          return throwError(convertedLeadResponse?.errors[0]?.message);
        }
        setFormValues({ isFormSubmitted: true });
        accountIdList.push(convertedLeadResponse.accountId);
      } else {
        // Salesforce has a trigger that activates if there is any site ID duplication or duplicate values that we pass,
        // and those values already exist in Salesforce.
        // The fetchSiteIdDuplicationStatus function accepts an array of site IDs (string[]) and returns an object where
        // each key is a site ID and its value is true or false, indicating if the site ID is a duplicate.
        const isValidSiteId = siteId && siteId.length && siteId?.[0] !== '';
        if (isValidSiteId) {
          const isSiteIdDuplicate = await fetchSiteIdDuplicationStatus(siteId);
          if (isSiteIdDuplicate) return false;
        }
        accountIdList = await SalesforceManager.createMainAccountAndSubAccount({
          ...updatedFormValues,
          ...customerAddress,
          secondaryLeadVendor,
        });
        if (leadIdForConversion)
          await SalesforceManager.updateLead({ Id: leadIdForConversion, Status: 'Duplicate' });
        const invalidAccount =
          !accountIdList || accountIdList?.error || accountIdList?.length === 0;
        if (invalidAccount) {
          return swal.fire({
            title: accountIdList.error.message,
            icon: 'error',
            confirmButtonText: 'OK',
            showCancelButton: false,
          });
        }
        setFormValues({ isFormSubmitted: true });
      }
      await getDealsDetails({ accountIdList, setFormValues });
      const availableSlots = await getNextAvailableSlots(updatedFormValues);
      setAvailableSlots(availableSlots);
      setTableStep(duplicateTableSteps.accounts);
      setShowDuplicateRecordsTable(false);
      nextTab();
      return availableSlots;
    } catch (err) {
      console.error(err);
      return throwError(err);
    }
  };

  return proceedWithAccountsCreation;
};

export default useAccountsCreation;
