import styled from 'styled-components';
import { updateNestedObjectFieldWithPath } from '@utils/functions';

export const handleFormFieldChange = (event, state, setState) => {
  const { name: path } = event.target;
  let { value } = event.target;
  // Work around for multipicklists.
  // Just want to save the id value to state, not the name
  if (value?.[0]?.value) value = value.map((value) => value.value);

  // Clear out any error messages that may be on that form field when they change it
  const stateWithoutCurrentFieldError = updateNestedObjectFieldWithPath(
    state,
    `formFieldErrors.${path}`,
    null,
  );

  // Update the event state
  const updatedEvent = updateNestedObjectFieldWithPath(stateWithoutCurrentFieldError, path, value);
  setState(updatedEvent);
};

export const Container = styled.div`
  padding: 1.5em;
`;

export const Row = styled.div`
  display: flex;
  width: 100%;
`;

export const Col = styled.div`
  margin-right: 0.75em;
  margin-left: 0.75em;
  &:first-child {
    margin-left: 0;
  }
  &:last-child {
    margin-right: 0;
  }
  flex: ${(props) =>
    props.size || props.size === 0 ? props.size : 1}; // Default to 1 if not supplied
  ${({ left }) => left && 'margin-left: auto;'};
  ${({ right }) => right && 'margin-right: auto;'};
`;

// TODO: is this a good name/the right place for this?
// looks like it's specifically for the addremoveday stuff, maybe it can stay in that file
export const AddRemoveButtonContainer = styled.div`
  display: flex;
  width: 100%;
  font-size: 10px;
  justify-content: space-around;
  padding-bottom: 5%;
`;

export const ButtonContainer = styled.div`
  display: flex;
  height: 100%;
  align-items: center;

  > button {
    ${({ marginDirections = ['left'], marginSize = '10px' }) => {
      let css = '';
      marginDirections.forEach((direction) => {
        css += `margin-${direction}: ${marginSize};`;
      });
      return css;
    }}
  }
`;
