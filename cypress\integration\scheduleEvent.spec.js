before(() => {
  cy.login();
  cy.saveLocalStorage();
});

beforeEach(() => {
  cy.restoreLocalStorage();
});

describe('Insulation Schedule | schedule event workflow', () => {
  it('can navigate to the insulation scheduling page', () => {
    cy.visit('/');
    cy.contains('View Schedule').click();
    cy.contains('Insulation Install').click();
    cy.get('[data-testid=insulation-schedule-calendar]').contains('Insulation Schedule');
    cy.get('[data-testid=insulation-schedule-calendar]').contains('Book Appointment');
  });

  it('loads insulation scheduling page without errors', () => {
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(2000); // waiting for events to load

    cy.get('[data-testid=insulation-schedule-calendar]').contains('Insulation Schedule');
    cy.get('[data-testid=insulation-schedule-calendar]').contains('Book Appointment');
  });

  it('clicks on the first available open calendar day', () => {
    cy.get('[data-testid=calendar-cell]')
      .first()
      .dblclick();
  });

  it('enters operation id', () => {
    cy.get('[data-testid=insulation-operations-id-input]')
      .click()
      .type('a0o0y00000NRWPT');
  });

  it('adjusts fields, dates, times', () => {
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(2000);
    // Changes times
    cy.get('[data-testid=remove-0-5-time-button]').click();
    cy.get('[data-testid=remove-0-25-time-button]').click();
    cy.get('input[name=day1End]').should('include.value', '11:00 AM');
    cy.get('[data-testid=add-0-5-time-button]').click();
    cy.get('[data-testid=add-0-25-time-button]').click();
    cy.get('input[name=day1End]').should('include.value', '5:00 PM');
    // Adds requirements
    cy.get('[data-testid=insulation-requirements-multi-select]')
      .children('div')
      .children('div')
      .children('div')
      .children('input')
      .click();

    // Adds first requirement
    cy.get('.rw-list-option')
      .first()
      .click();

    // Adds second requirement
    cy.get('.rw-list-option')
      .first()
      .click();

    // removes first requirement
    cy.get('.rw-multiselect-tag-btn')
      .first()
      .click();

    // Adds Trucks
    // Adds Job Type
    cy.get('[data-testid=insulation-job-type-input]').select('Insulation Callback');
  });

  it('saves an event', () => {
    cy.contains('Save').click();
  });

  it('refreshes page and verifies event information', () => {
    // cy.visit('/view-insulation-install-schedule');
    cy.reload();
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(8000); // waiting for events to load
    cy.get('[data-testid=calendar-cell]')
      .first()
      .children('[role=button]')
      .dblclick();
  });

  it('deletes event', () => {
    cy.contains('Cancel Event').click();
    cy.contains('Yes').click();
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(8000);
    cy.contains('OK').click();
  });
});
