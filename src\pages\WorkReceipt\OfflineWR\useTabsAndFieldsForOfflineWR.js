import React, { useMemo } from 'react';
import DataIntakeFormPage from '@components/DataIntakeForm/components/DataIntakeFormPage';
import { cloneDeep } from 'lodash';
import { offlineWorkReceiptMap, offlineWorkReceiptFields } from '../FormSchema/fieldsMap';
import { offlineWorkReceiptTabs as wrIntakeTabs } from '../consts';

export const useTabsAndFieldsForOfflineWR = ({
  isDealLoaded,
  docRepoStatus,
  hasReturnVisit,
  isBadSpec,
  activeTab,
}) => {
  const tabs = useMemo(() => {
    let currentTabs = wrIntakeTabs;
    if (isDealLoaded) {
      if (docRepoStatus > 0 && !currentTabs.some((tab) => tab.name === 'postHea')) {
        currentTabs.push({ name: 'postHea', title: 'Post HEA' });
      }
      if (hasReturnVisit && !currentTabs.some((tab) => tab.name === 'returnVisitResulting')) {
        currentTabs.push({ name: 'returnVisitResulting', title: 'Return Visit Resulting' });
      }
      if (isBadSpec && !currentTabs.some((tab) => tab.name === 'goodSpecBadSpec')) {
        currentTabs.push({ name: 'goodSpecBadSpec', title: 'Specs' });
      }
    }

    // When the forms are reset, the 'postHea' tab needs to be removed.
    // The 'postHea' tab is conditionally shown only after deals are loaded.
    // Since the form is being reset (likely before deals are fully loaded).
    if (currentTabs.some((tab) => tab.name === 'postHea') && !docRepoStatus) {
      currentTabs = currentTabs.filter((tab) => tab.name !== 'postHea');
    }
    currentTabs = currentTabs.filter((tab) => tab.name !== 'readOnlyFields');
    currentTabs.push({ name: 'readOnlyFields', title: 'Additional Project Information' });
    return currentTabs;
  }, [isDealLoaded, docRepoStatus]);

  const offlineWorkReceiptTabs = useMemo(
    () =>
      tabs?.map(({ name, title }) => {
        const map = cloneDeep(offlineWorkReceiptMap[name]);

        return {
          name,
          title,
          component: (
            <DataIntakeFormPage map={map} readOnlyForReview={activeTab === 'readOnlyFields'} />
          ),
        };
      }),
    [isDealLoaded, docRepoStatus, activeTab],
  );
  return {
    offlineWorkReceiptMap,
    offlineWorkReceiptFields,
    offlineWorkReceiptTabs,
  };
};
