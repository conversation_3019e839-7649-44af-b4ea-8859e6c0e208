import React, { memo } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const ScriptTitle = styled.h4`
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 5px;
  text-transform: none;
  color: #201d1d;
  margin-bottom: 24px;
`;

const ScriptText = styled.h6`
  font-style: normal;
  font-size: 12px;
  line-height: 20px;
  margin-bottom: 5px;
  color: #201d1d;
  text-decoration: ${({ underline }) => (underline ? 'underline' : 'unset')};
  font-weight: ${({ bold }) => (bold ? 600 : 400)};
`;

const ScriptContainer = styled.div`
  display: flex;
  flex-direction: ${({ flexColumn }) => (flexColumn ? 'column' : 'row')};
  gap: ${({ gap }) => (gap ? '72px' : 0)};
  margin-bottom: 24px;
`;

const EventScript = memo(function EventScript({ title, arrivalDetails }) {
  const renderArrivalDetails = () => {
    return arrivalDetails.map(
      ({ eventTypeHeader, appointmentTimingDetails, arrivalWindowDetails }) => {
        return (
          <>
            <ScriptTitle>{eventTypeHeader}</ScriptTitle>
            <ScriptContainer gap>
              {renderTimings([appointmentTimingDetails, arrivalWindowDetails])}
            </ScriptContainer>
          </>
        );
      },
    );
  };

  const renderTimings = (timingDetails) => {
    return timingDetails
      .filter((timingDetail) => {
        return timingDetail;
      })
      .map(({ header, timings }) => {
        return (
          <ScriptContainer key={`${header}${timings}`} flexColumn>
            <ScriptText underline>{header}</ScriptText>
            {timings.map((time) => {
              return <ScriptText key={time}>{time}</ScriptText>;
            })}
          </ScriptContainer>
        );
      });
  };

  return (
    <>
      <ScriptTitle>{title}</ScriptTitle>
      {renderArrivalDetails()}
    </>
  );
});

EventScript.propTypes = {
  title: PropTypes.string.isRequired,
  /* eslint-disable react/forbid-prop-types */
  arrivalDetails: PropTypes.arrayOf(PropTypes.any).isRequired,
};

export default EventScript;
