import styled from 'styled-components';

export const Wrapper = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  height: 500px;

  .react-datepicker__day--highlighted {
    border-radius: 50%;
    background-color: #f3b6aa;
    color: black;
  }

  .react-datepicker__day--outside-month {
    visibility: hidden;
  }
`;

export const CalendarWrapper = styled.div`
  padding: 10px;
`;

export const InfoWrapper = styled.div`
  padding: 10px;
  text-align: center;
`;

export const TextWrapper = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;

  font-weight: bold;
`;

export const DateCircle = styled.div`
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background-color: #f3b6aa;
  margin: 1px;
`;
