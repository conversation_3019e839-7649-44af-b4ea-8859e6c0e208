import React, { Suspense } from 'react';
import PropTypes from 'prop-types';
import {
  useRecoilState,
  useRecoilValue,
  useSetRecoilState,
  useRecoilCallback,
  // eslint-disable-next-line camelcase
  useRecoilBridgeAcrossReactRoots_UNSTABLE,
} from 'recoil';
import { withRouter } from 'react-router-dom';
import { useTheme } from 'styled-components';

import Swal from 'sweetalert2/dist/sweetalert2';
import { ErrorBoundary } from '@components/global';
import { EventsManager, SlotsManager } from '@utils/APIManager';
import eventValidation from '@utils/eventValidation';
import {
  showSidebarState,
  isSlotsSearchAtom,
  selectedEventState,
  availableSlotsAtom,
  isLegendViewAtom,
  isSchedulingViewAtom,
  schedulingTypeAtom,
} from '@recoil/eventSidebar';
import { pinnedEventSelectorFamily, allEventsAtom } from '@recoil/event';
import { calendarTypeAtom } from '@recoil/app';
import { isEventsSearchState } from '@recoil/eventsSearch';
import { allAgentsFormOptionsState } from '@recoil/agents';

import { fireCancelConfirmation } from '@components/confirmations/CancelEventConfirmation';

import { decodeEventType, getStartEndTimesForEvent } from '@homeworksenergy/utility-service';
import { displaySuccessMessage } from '@utils/EventEmitter';
import { hasRole } from '@utils/AuthUtils';
import EventSidebarErrorBoundary from './EventSidebarErrorBoundary';
import EventSidebarBase from './EventSidebarBase';

import {
  HEAForm,
  HVACSalesForm,
  HVACSalesRescheduleForm,
  HVACInstallForm,
  HESScheduleHVACForm,
  InsulationInstallForm,
  CustomBlockForm,
  FindInsulationSlotsForm,
  FindHEASlotsForm,
  FindHVACSlotsForm,
  FindHVACSalesSlotsForm,
  Legend,
  EventsSearchForm,
  HEARescheduleForm,
  HEAReturnVisitForm,
  HVACInstallRescheduleForm,
  ReassignAndSwapForm,
  HVACInstallReassignForm,
  HVACSiteEvalForm,
  SubHubForm,
} from './EventSidebarForms';

import {
  FindHEASlotsCT,
  FindSubSlotsCT,
  HEAFormCT,
  SubFormCT,
  HEARescheduleFormCT,
  ScheduleSealingServiceCT,
} from './EventSidebarForms/CT';

const EventSidebar = (props) => {
  const theme = useTheme();
  // RecoilBridge allows the cancelEventConfirmation to share the same recoil state as the rest of our application
  // This is necessary since sweetalert2 creates its own ReactRoot for each modal
  // https://recoiljs.org/docs/api-reference/core/useRecoilBridgeAcrossReactRoots
  const RecoilBridge = useRecoilBridgeAcrossReactRoots_UNSTABLE();
  const [showSidebar, setShowSidebar] = useRecoilState(showSidebarState);
  const [pinnedEvent, setPinnedEvent] = useRecoilState(pinnedEventSelectorFamily(event?.id));
  const isSlotsSearch = useRecoilValue(isSlotsSearchAtom);
  const isEventsSearch = useRecoilValue(isEventsSearchState);
  const isSchedulingView = useRecoilValue(isSchedulingViewAtom);
  const isLegendView = useRecoilValue(isLegendViewAtom);
  const event = useRecoilValue(selectedEventState);
  const calendarType = useRecoilValue(calendarTypeAtom);
  const schedulingType = useRecoilValue(schedulingTypeAtom);
  const agents = useRecoilValue(allAgentsFormOptionsState);
  const allEvents = useRecoilValue(allEventsAtom);
  const setAvailableSlots = useSetRecoilState(availableSlotsAtom);

  // useRecoilCallback necessary to get updated cancelReason and notes from cancelEventConfirmation
  // This is because the cancel reason gets populated on the confirmation modal, but the handleCancelClick
  // function only has access to the old values
  const cancelEvent = useRecoilCallback(
    ({ snapshot }) => async (eventObject) => {
      const isPartner = ['0088', '0089'].includes(eventObject.type.slice(0, 4));
      const event = isPartner ? eventObject : await snapshot.getPromise(selectedEventState);
      const agents = await snapshot.getPromise(allAgentsFormOptionsState);
      const {
        id,
        associatedEventsId,
        cancelReason,
        notes,
        createMileageEvent,
        startEndTimes,
      } = event;
      // Checks to see if event is pinned. If it pinned, call the function to unpin
      if (pinnedEvent?.event?.id) setPinnedEvent(id);

      let agentName = '';
      const agent = agents.find((agent) => agent.value === event?.oid);

      if (agent) ({ key: agentName } = agent);
      const action = isPartner ? 'cancelPartnerEvent' : 'cancelEvent';
      await EventsManager[action]({
        id,
        associatedEventsId,
        cancelReason,
        notes,
        createMileageEvent,
        startEndTimes,
        agentName,
      });
      setShowSidebar(false);
    },
    [],
  );
  const {
    location: { pathname },
  } = props;
  if (!event || pathname.includes('doc-repo')) return null;

  const { id } = event;
  const type = event.type || calendarType;
  if (!type) return null;
  const isSiteEval = ['000433', '000434'].includes(type);
  let { business } = decodeEventType(type);
  const { state } = decodeEventType(type);
  if (isSiteEval) business = 'Site Eval';
  const isHeaPerformingAction = calendarType === '000000' || hasRole('Agent', 1);
  const hasCustomDuration = ['010003', '010005', '010006'].includes(type);

  const formsMap = {
    MA: {
      HEA: HEAForm,
      'HVAC Sales': HVACSalesForm,
      'HVAC Install': HVACInstallForm,
      Insulation: InsulationInstallForm,
      Partners: SubHubForm,
      'Site Eval': HVACSiteEvalForm,
    },
    CT: {
      HEA: HEAFormCT,
      Subcontractor: SubFormCT,
    },
  };

  const rescheduleMap = {
    MA: {
      HEA: HEARescheduleForm,
      'HVAC Install': HVACInstallRescheduleForm,
      'HVAC Sales': HVACSalesRescheduleForm,
    },
    CT: { HEA: HEARescheduleFormCT },
  };

  const reassignMap = {
    MA: {
      HEA: ReassignAndSwapForm,
      'HVAC Install': HVACInstallReassignForm,
      'HVAC Sales': ReassignAndSwapForm,
    },
    CT: { HEA: ReassignAndSwapForm },
  };

  const returnMap = {
    MA: {
      HEA: HEAReturnVisitForm,
    },
  };

  const slotsSearchFormsMap = {
    MA: {
      Insulation: FindInsulationSlotsForm,
      HEA: FindHEASlotsForm,
      'HVAC Install': FindHVACSlotsForm,
      'HVAC Sales': isHeaPerformingAction ? HESScheduleHVACForm : FindHVACSalesSlotsForm,
      Partners: SubHubForm,
      'Site Eval': FindHVACSlotsForm,
    },
    CT: {
      // TODO: These can probably be reduced to a single form
      HEA: hasCustomDuration ? ScheduleSealingServiceCT : FindHEASlotsCT,
      Subcontractor: FindSubSlotsCT,
    },
  };

  let Form = business === 'N/A' ? CustomBlockForm : null;
  if (!Form)
    Form = isSlotsSearch ? slotsSearchFormsMap[state][business] : formsMap[state][business];
  if (isLegendView) Form = Legend;
  if (isEventsSearch) Form = EventsSearchForm;
  if (isSchedulingView) {
    if (schedulingType === 'reschedule') Form = rescheduleMap[state][business];
    if (schedulingType === 'reassign') Form = reassignMap[state][business];
    if (schedulingType === 'return') Form = returnMap[state][business];
  }

  if (!Form) return null;

  const handleCancelClick = async (eventObject = event) => {
    const { type: eventType } = eventObject;
    const { state, business: department } = decodeEventType(eventType);
    const isSiteEvalOrFinalSiteEval = ['000433', '000434'].includes(eventType);
    const isCTEvent = state === 'CT';
    const showMileageEventOption = !isCTEvent && !isSiteEvalOrFinalSiteEval;

    if (['HEA', 'HVAC Sales', 'Subcontractor'].includes(department) || isSiteEvalOrFinalSiteEval) {
      const { value: confirmed } = await fireCancelConfirmation(
        RecoilBridge,
        showMileageEventOption,
        !isSiteEvalOrFinalSiteEval,
        theme,
      );
      if (!confirmed) return false;
    }
    if (['Insulation'].includes(department)) {
      const { value: confirmed } = await Swal.fire({
        title: 'Are you sure you want to cancel this appointment?',
        confirmButtonText: 'Yes',
        showCancelButton: true,
        cancelButtonText: 'No',
      });
      if (!confirmed) return false;
    }
    return cancelEvent(eventObject);
  };

  // TODO: handle case where nothing has changed
  const handleSaveClick = async (eventObject = event) => {
    const isPartner = ['0088', '0089'].includes(eventObject.type?.slice(0, 4));
    let action = id ? 'update' : 'create';
    if (isPartner) action = eventObject?.id ? 'updatePartnerEvent' : 'createPartnerEvent';
    // TODO: this is messy, if we add to this we'll have to clean it up. An eventual refactor of this file will be necessary as lots of business specific logic is being added to all these functions
    let insertObjects = eventValidation[state][business][action](eventObject);
    // If the validation fails, insert objects will be falsy
    if (!insertObjects) return false;

    // TODO: agentName can be set before getting to handleSaveClick into the selectedEvent and then we can skip this
    // For CT Multislot booking, the insertobjects will be an array and we already set the agent name (in AgentSlot component). so we can skip this
    if (!Array.isArray(insertObjects)) {
      let agentName = null;
      const agentDetail = agents?.find((agent) => agent.value === insertObjects?.oids[0]);
      if (agentDetail) ({ key: agentName } = agentDetail);
      insertObjects = { ...insertObjects, agentName };
    }

    // If the status is berier service then append the 'invoiceSubcontractor' key.
    const insertPayload = { ...insertObjects };
    if (insertPayload.status === 'Barrier Cleared') {
      insertPayload.invoiceSubcontractor = 'Yes';
    }
    const success = await EventsManager[action](insertPayload);
    if (success) setShowSidebar(false);
    return success;
  };

  const handleRescheduleClick = async (params) => {
    const { rescheduleReason, notes } = event;
    // For HEA Reschedule later (i.e params = rescheduleType)
    if (params && params === 'later') {
      await handleRescheduleLater(rescheduleReason, notes);
      return true;
    }
    await handleReschedule();
    return true;
  };

  const handleReassignClick = async (params) => {
    const { type: eventType } = event;
    const { business: department } = decodeEventType(eventType);

    if (department === 'HVAC Install') {
      const response = await EventsManager.reassignEvent(params);
      if (!response) return false;
      setShowSidebar(false);
      return displaySuccessMessage('Successfully reassigned event');
    }

    if (['HEA', 'HVAC Sales'].includes(department)) {
      const { key: agentName, value: oid } = params;

      const { value: confirmed } = await Swal.fire({
        title: `Are you sure you want to reassign this appointment to ${agentName}?`,
        confirmButtonText: 'Yes',
        showCancelButton: true,
        cancelButtonText: 'No',
      });
      if (!confirmed) return false;
      // Checks to see if event is pinned. If it pinned, call the function to unpin
      if (pinnedEvent?.event?.id) setPinnedEvent(id);
      const { id: eventId, sfIds, type, associatedEventIds, numUnit, startEndTimes } = event;

      const reassignParams = {
        id: eventId,
        oids: [oid],
        oid,
        sfIds,
        type,
        associatedEventIds,
        numUnit,
        startEndTimes,
        agentName,
      };
      const response = await EventsManager.reassignEvent(reassignParams);
      if (!response) return false;
      setShowSidebar(false);
    }
    return false;
  };

  const handleSwapClick = async (swapEvent) => {
    const { type: eventType } = event;
    const { business: department } = decodeEventType(eventType);

    if (['HEA', 'HVAC Sales'].includes(department)) {
      // This logic was put above the Swal fire so the Swal will have the display name of the agent they are trying to swap with
      const swapParams = {
        // Set event to swap with data
        selectedEvent: event,
        swapEvent,
        type,
      };

      const response = await EventsManager.swapEvent(swapParams);
      if (!response) return false;
      // Checks to see if event is pinned. If it pinned, call the function to unpin
      if (pinnedEvent?.event?.id) setPinnedEvent(id);
      return setShowSidebar(false);
    }
    return false;
  };

  const handleRescheduleLater = async (reason, notes) => {
    const { id, associatedEventsId, createMileageEvent } = event;
    const { value: confirmed } = await Swal.fire({
      title: 'Are you sure you want to reschedule this appointment later?',
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirmed) return;
    const isValid = eventValidation[state][business].rescheduleLater(event);
    if (!isValid) return;
    // Checks to see if event is pinned. If it pinned, call the function to unpin
    if (pinnedEvent?.event?.id) setPinnedEvent(id);
    await EventsManager.rescheduleLater({
      id,
      associatedEventsId,
      reason,
      notes,
      createMileageEvent,
    });
    setShowSidebar(false);
  };

  const handleReschedule = async () => {
    const { value: confirmed } = await Swal.fire({
      title: 'Are you sure you want to reschedule this appointment?',
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirmed) return;
    const isValid = eventValidation[state][business].reschedule(event);
    if (!isValid) return;
    // Checks to see if event is pinned. If it pinned, call the function to unpin
    if (pinnedEvent?.event?.id) setPinnedEvent(id);
    const {
      id,
      oids,
      associatedEventsId,
      date,
      type,
      sfIds,
      rescheduleReason,
      notes,
      createMileageEvent,
      numUnit,
      associatedEventIds,
      oldAgentOid,
      arrivalWindow,
    } = event;
    const { key: agentName } = agents.find((agent) => agent.value === oids[0]);

    const rescheduleObject = {
      id,
      oids,
      associatedEventsId,
      date,
      type,
      sfIds,
      rescheduleReason,
      notes,
      startEndTimes: getStartEndTimesForEvent({ ...event, date }, allEvents),
      createMileageEvent,
      numUnit,
      agentName,
      associatedEventIds,
      oldAgentOid,
      arrivalWindow,
    };
    await EventsManager.rescheduleEvent(rescheduleObject);
    setShowSidebar(false);
  };

  const handleReturnClick = async () => {
    const { id } = event;
    // Checks to see if event is pinned. If it pinned, call the function to unpin
    if (pinnedEvent?.event?.id) setPinnedEvent(id);
    const insertObjects = eventValidation[state][business].returnVisit(event);
    if (!insertObjects) return;

    await EventsManager.create(insertObjects);
    setShowSidebar(false);
  };

  const handleFindSlotsClick = async (findPast = false) => {
    const {
      sfIds: { operationsId, dealId = '' },
      jobLength,
      eventDuration,
      startEndTimes,
      attributes,
      regions,
      numUnit,
      virtual,
      includeAgents,
      address,
      date,
      program,
      amount,
      squareFeet,
    } = event;

    const isValid = eventValidation[state][business].getSlots(event);
    if (!isValid) return false;
    const slots = await SlotsManager.getNextAvailableSlots({
      operationsId,
      dealId,
      type,
      jobLength,
      eventDuration,
      startEndTimes,
      attributes,
      regions,
      numUnit,
      virtual,
      includeAgents,
      address,
      date,
      findPast,
      program: business === 'HEA' ? program : null,
      amount,
      squareFeet,
    });

    setAvailableSlots(slots);
    return true;
  };

  const handleFindReassignSwapSlots = async (action) => {
    const { type, date, startTime, endTime, address, numUnit, id, oid } = event;
    const params = {
      type,
      date,
      startTime,
      endTime,
      address,
      numUnit,
      id,
      oid,
    };
    const getSlotsFunc =
      action === 'swap' ? SlotsManager.getSlotsForSwap : SlotsManager.getAvailableUsersForReassign;
    const { returnSlots } = await getSlotsFunc(params);
    setAvailableSlots(returnSlots);
    return returnSlots;
  };

  return (
    <EventSidebarBase show={showSidebar} {...props}>
      <ErrorBoundary fallback={<EventSidebarErrorBoundary />}>
        {/* TODO: Better suspense support */}
        <Suspense fallback={<div>loading...</div>}>
          {/* TODO: I don't really like how we send down all these functions to all forms,
            even forms that don't need it. There might be a better way to go about this
           */}
          <Form
            handleCancelClick={handleCancelClick}
            handleSaveClick={handleSaveClick}
            handleFindSlotsClick={handleFindSlotsClick}
            handleRescheduleClick={handleRescheduleClick}
            handleReassignClick={handleReassignClick}
            handleReturnClick={handleReturnClick}
            handleSwapClick={handleSwapClick}
            handleFindReassignSwapSlots={handleFindReassignSwapSlots}
            business={business}
            state={state}
          />
        </Suspense>
      </ErrorBoundary>
    </EventSidebarBase>
  );
};

EventSidebar.propTypes = {
  location: PropTypes.shape({
    pathname: PropTypes.string,
  }).isRequired,
};

export default withRouter(EventSidebar);
