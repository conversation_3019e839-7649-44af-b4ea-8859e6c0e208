import React, { useEffect, useState } from 'react';
import { ThemeProvider } from 'styled-components';
import PropTypes from 'prop-types';
import { PageHeader } from '@pages/Components';
import { HorizontalLine, Header as Title } from '@components/global';
import { CustomerManager } from '@utils/APIManager';
import { urlParamsToJson } from '@utils/functions';
import { formTheme } from '@style/variables';
import ReferralList from './List';
import { CardContainer, FlexRow } from '../Customers/styles';
import ReferralForm from './Form';

const { colors, primary, secondary } = formTheme;
const theme = {
  colors,
  primary,
  secondary,
};

const Referral = (props) => {
  const { location } = props;
  const queryParams = location.search;
  const { customerId = '' } = urlParamsToJson(queryParams);
  const [referralList, setReferralList] = useState([]);
  const [employeeList, setEmployeeList] = useState([]);

  useEffect(() => {
    const referralList = async () => {
      const response = await CustomerManager.getReferralList({ limit: 20 });
      setReferralList(response.referrals);
      setEmployeeList(response.employees);
    };
    referralList();
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <PageHeader>Referral</PageHeader>
      <CardContainer>
        <FlexRow gap="4px">
          <Title h2>
            Have a friend who you think would enjoy a Home Energy Assessment as much as you did ?
            Sign them up to receive a call from us today !
          </Title>
        </FlexRow>
        <ReferralForm
          customerId={customerId}
          referralList={referralList}
          employeeList={employeeList}
          setReferralList={setReferralList}
        />
        <HorizontalLine />
        <ReferralList list={referralList} />
      </CardContainer>
    </ThemeProvider>
  );
};

Referral.propTypes = {
  location: PropTypes.shape({ search: PropTypes.string }),
};

Referral.defaultProps = {
  location: { search: null },
};

export default Referral;
