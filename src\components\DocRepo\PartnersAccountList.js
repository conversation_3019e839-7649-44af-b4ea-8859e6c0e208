import React, { Suspense, useState, useEffect } from 'react';
import { useRecoilState, useSetRecoilState, useRecoilValue } from 'recoil';
import { useLocation } from 'react-router-dom';
import styled from 'styled-components';

import { PageHeader } from '@pages/Components';
import { Row } from '@components/global/Form';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import Tabs from '@components/global/Tabs/Tabs';
import { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import CapDocRepo from '@components/DocRepo/CAPHEA';
import { calendarTypeAtom, activeTabIndexAtom } from '@recoil/app';
import { selectedEventState, selectedPartnerEventState } from '@recoil/eventSidebar';
import { EventsManager } from '@utils/APIManager';
import { getAlphanumericString } from '@utils/functions';
import { barrierTypeOptions } from '@utils/businessLogic/partnersBusinessLogic';
import PartnersDocRepo from './PartnersDocRepo';

const CustomerNameStyled = styled(HeaderTitle)`
  margin-bottom: 30px;
  margin-top: 5px;
  ${({ theme }) => theme.screenSize.up(theme.breakpoints.tablet)} {
    display: flex;
    justify-content: space-between;
  }
`;
const DocumentContainer = styled.div``;
const AccountPanelContainer = styled(Row)`
  cursor: pointer;
`;
const Divider = styled.hr``;
const DealIdStyled = styled.div``;
const AddressStyled = styled.div``;

const PartnersAccountList = () => {
  const [events, setEvents] = useState([]);
  const [tabs, setTabs] = useState([]);
  const [displayPanel, setDisplayPanel] = useState(null);
  const setSelectedPartnerEvent = useSetRecoilState(selectedPartnerEventState);
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);
  const [activeUnitTab, setActiveUnitTab] = useRecoilState(activeTabIndexAtom(['tabs']));
  const setCalendarType = useSetRecoilState(calendarTypeAtom);
  const activeDepartmentTab = useRecoilValue(activeTabIndexAtom(['department']));

  const pathArr =
    useLocation()
      .pathname.split('/view-doc-repo/MA/')?.[1]
      ?.split('/') || [];

  const isPartner = pathArr?.[0].includes('Partner');

  useEffect(() => {
    setCalendarType('008800');
    const getCustomerInfo = async (paramsAccountId, department) => {
      await getCustomer(paramsAccountId, department);
    };
    getCustomerInfo(pathArr?.[1] || '', pathArr?.[0] || '');
  }, []);

  const getCustomer = async (paramsAccountId, department) => {
    if (department.includes('HEA')) {
      const searchResults = await EventsManager.searchEvents({
        searchTerm: getAlphanumericString(paramsAccountId),
        fieldsToSearch: ['type'],
        departmentEventTypes: ['0000'],
      });
      if (searchResults.length > 0) {
        const event = searchResults[0];
        const {
          accountId,
          accountId2,
          accountId3,
          accountId4,
          dealId,
          dealId2,
          dealId3,
          dealId4,
        } = event.sfIds;
        const dealIdsArr = [dealId, dealId2, dealId3, dealId4];
        const accountIdsArr = [accountId, accountId2, accountId3, accountId4];
        const filteredDealIds = dealIdsArr.filter((dealId) => {
          return dealId;
        });
        const formatAccountIds = accountIdsArr
          ?.filter((accountId) => accountId)
          ?.map((id) => {
            return id.slice(0, 15);
          });
        let tabs = [];
        filteredDealIds.forEach((dealId, index) => {
          const secondaryTab = [
            {
              name: `docRepo_hea_${dealId}`,
              title: 'HEA',
              component: <CapDocRepo uniqueId={dealId} department="HEA-CAP" />,
            },
            {
              name: `docRepo_hvac_${dealId}`,
              title: 'HVAC',
              component: <CapDocRepo uniqueId={dealId} department="HVAC_Sales-CAP" />,
            },
          ];
          tabs = [
            ...tabs,
            {
              name: `docRepo_${dealId}`,
              title: filteredDealIds.length > 1 ? `Doc Repo (Unit ${index + 1})` : 'Doc Repo',
              component: <Tabs tabs={secondaryTab} id="department" />,
              children: secondaryTab,
            },
          ];
        });
        const paramsAccountIdIndex = formatAccountIds?.indexOf(paramsAccountId) || 0;
        setTabs(tabs);
        setSelectedEvent(event);
        setActiveUnitTab(paramsAccountIdIndex);
      }
      return;
    }
    //  Get all the events, including archive jobs
    const events = await EventsManager.loadPartnerEvents({ storageClass: 'all' });
    const filteredEvents = Object.values(events?.rows)?.filter(({ accountId }) => {
      return accountId.slice(0, 15) === paramsAccountId.slice(0, 15);
    });

    if (filteredEvents.length > 0) setEvents(filteredEvents);
  };

  const handleOnCLickEvent = (event) => {
    setSelectedPartnerEvent(event);
    // Open Panel to display documents
    setDisplayPanel(event.id);
  };

  const renderList = () => {
    return (
      <>
        {events.map((event) => {
          const {
            accountName,
            sfIds: { barrierId },
            type,
          } = event;
          const { key: typeName } = barrierTypeOptions.find(({ value }) => {
            return value === type;
          });
          return (
            <>
              <AccountPanelContainer>
                <CustomerNameStyled key={barrierId} onClick={() => handleOnCLickEvent(event)}>
                  {accountName} - {typeName}
                </CustomerNameStyled>
              </AccountPanelContainer>
              {displayPanel === event.id && <PartnersDocRepo />}
              <Divider />
            </>
          );
        })}
      </>
    );
  };

  if (isPartner)
    return (
      <>
        <PageHeader buttons={[]}>Sub Hub Doc Repo</PageHeader>
        {events.length > 0 ? (
          <EventSidebarBody>
            <DocumentContainer>{renderList()}</DocumentContainer>
          </EventSidebarBody>
        ) : (
          <CustomerNameStyled>No Barriers Created</CustomerNameStyled>
        )}
      </>
    );

  return (
    <>
      <SidebarForm>
        <Suspense fallback={<div>loading...</div>}>
          <PageHeader buttons={[]}>Doc Repo</PageHeader>
          {selectedEvent && tabs.length > 0 ? (
            <>
              <CustomerNameStyled>
                <DealIdStyled>
                  {`Deal Id : ${
                    selectedEvent?.sfIds?.[`dealId${activeUnitTab > 0 ? activeUnitTab + 1 : ''}`]
                  }`}
                </DealIdStyled>
                <AddressStyled>
                  Address : {selectedEvent?.address?.displayAddress || '-'}
                </AddressStyled>
              </CustomerNameStyled>
              <Tabs tabs={tabs} />
              {tabs?.[activeUnitTab]?.component}
              {tabs?.[activeUnitTab]?.children?.[activeDepartmentTab]?.component}
            </>
          ) : (
            <CustomerNameStyled>
              {`Sorry, There is no event scheduled for account id '${pathArr?.[1]}'.`}
            </CustomerNameStyled>
          )}
        </Suspense>
      </SidebarForm>
    </>
  );
};

export default PartnersAccountList;
