import { useParams } from 'react-router-dom';
import { useRecoilValue, useSetRecoilState } from 'recoil';
import { throwError } from '@utils/EventEmitter';

import {
  showDuplicateRecordsAtom,
  duplicateLeadsAtom,
  selectedLeadIdAtom,
  duplicateRecordsType<PERSON>tom,
  intakeFlowAtom,
} from '@recoil/leadIntake';
import { formValuesState } from '@recoil/dataIntakeForm';
import { SalesforceManager } from '@utils/APIManager';
import { duplicateTableSteps } from '../consts';

const useGetDuplicateLeads = (sfType = '1.0') => {
  const { leadId: leadIdFromParams } = useParams();
  const leadId = useRecoilValue(selectedLeadIdAtom);
  const formValues = useRecoilValue(formValuesState);
  const flowOfIntake = useRecoilValue(intakeFlowAtom);
  const setTableData = useSetRecoilState(duplicateLeadsAtom);
  const setShowDuplicateRecordsTable = useSetRecoilState(showDuplicateRecordsAtom);
  const setTableStep = useSetRecoilState(duplicateRecordsTypeAtom);
  const fetchDuplicateLeads = async () => {
    try {
      // TODO: why is this "flowOfIntake" field necessary?
      if (leadId && flowOfIntake === 'SALESFORCE_FLOW') {
        return resetTableSettings();
      }

      // If they are already trying to convert an existing lead, don't run duplicate check
      if (leadIdFromParams) {
        return resetTableSettings();
      }

      const duplicateLeads = await SalesforceManager.getExistingDuplicateRecords(
        formValues,
        'Leads',
        sfType,
      );
      if (duplicateLeads.length) {
        setTableData(duplicateLeads);
        setShowDuplicateRecordsTable(true);
        setTableStep(duplicateTableSteps.leads);
      } else {
        resetTableSettings();
      }
      return duplicateLeads;
    } catch (error) {
      console.log(error);
      return throwError(error);
    }
  };

  const resetTableSettings = () => {
    setTableStep(duplicateTableSteps.accounts);
    setShowDuplicateRecordsTable(false);
  };

  return fetchDuplicateLeads;
};

export default useGetDuplicateLeads;
