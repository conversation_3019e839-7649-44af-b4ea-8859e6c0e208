import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue, useResetRecoilState } from 'recoil';

import { isAuthorized, hasRole } from '@utils/AuthUtils';

import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import { SecondaryButton } from '@components/global/Buttons';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  FormMultiselect,
  FormRadioButtons,
  BackButton,
  FormTextBox,
} from '@components/global/Form';
import { LoadingIndicator, Checkbox } from '@components/global';

import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import {
  eventTypeOptionsSelectorFamily,
  jobAttributesSelectorFamily,
  calendarTypeAtom,
} from '@recoil/app';
import { agentsFormOptionsSelector } from '@recoil/agents';
import { selectedEventState, availableSlotsAtom } from '@recoil/eventSidebar';

const FindHEASlotsFormCT = (props) => {
  const { handleFindSlotsClick, handleSaveClick } = props;

  const [slotInfo, setSlotInfo] = useRecoilState(selectedEventState);
  const {
    includeAgents,
    type,
    address,
    lock = false,
    notes,
    attributes,
    squareFeet,
    houseBuilt,
    numUnit,
  } = slotInfo;

  const agents = useRecoilValue(agentsFormOptionsSelector);
  const calendarType = useRecoilValue(calendarTypeAtom);
  const resetAvailableSlots = useResetRecoilState(availableSlotsAtom);
  const eventTypes = useRecoilValue(
    eventTypeOptionsSelectorFamily({
      departmentName: 'HEA',
      showGroups: false,
      stateCode: calendarType?.slice(0, 2),
    }),
  );
  const [loading, setLoading] = useState(false);
  const [displayForm, setDisplayForm] = useState(true);

  const isHES = hasRole('Agent', 'HEA', 'CT') && !isAuthorized('Scheduler', 'HEA', 'CT');

  useEffect(() => {
    if (!type) setSlotInfo({ ...slotInfo, type: calendarType });
  }, [type, calendarType, setSlotInfo, slotInfo]);

  const onViewSlotsButtonClick = async () => {
    const isValid = await handleFindSlotsClick();
    if (isValid) setDisplayForm(false);
  };

  const onClickBackButton = () => {
    resetAvailableSlots();
    setDisplayForm(true);
  };

  const handleFieldChange = useCallback(
    (e, updatedEvent = slotInfo) => {
      handleFormFieldChange(e, updatedEvent, setSlotInfo);
    },
    [slotInfo, setSlotInfo],
  );

  const handleCheckbox = (e) => {
    const { name } = e.target;
    handleFieldChange({ target: { name, value: !slotInfo[name] } });
  };

  const handleHouseBuiltChange = (e) => {
    const { name, value } = e.target;
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    if (value > currentYear) return;
    handleFieldChange({ target: { name, value } });
  };

  const handleNumUnitChange = (e) => {
    const { name, value } = e.target;

    handleFieldChange({ target: { name, value: Number(value) } });
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col size={2}>
            <HeaderTitle>
              {!displayForm && <BackButton onClick={onClickBackButton} />}
              Book Appointment
            </HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            {displayForm ? (
              <>
                <SfIdInputs sfObjectType="account" setLoading={setLoading} />
                <FormSelect
                  title="job type"
                  name="type"
                  value={type}
                  options={eventTypes}
                  onChange={handleFieldChange}
                  placeholder="Event Type"
                />

                <FormRadioButtons
                  title="How many units?"
                  name="numUnit"
                  value={numUnit}
                  options={[1, 2, 3, 4]}
                  onChange={handleNumUnitChange}
                />
                {address?.displayAddress && (
                  <FormInput
                    readOnly
                    title="Address"
                    name="address"
                    value={address.displayAddress}
                  />
                )}
                <FormInput
                  required
                  title="Square Feet"
                  name="squareFeet"
                  type="number"
                  value={squareFeet}
                  onChange={handleFieldChange}
                />
                <FormInput
                  required
                  title="House Built"
                  name="houseBuilt"
                  type="number"
                  value={houseBuilt}
                  onChange={handleHouseBuiltChange}
                />
                <FormMultiselect
                  title="requirement(s)"
                  name="attributes"
                  value={attributes}
                  recoilOptions={jobAttributesSelectorFamily({ type })}
                  onChange={handleFieldChange}
                />
                {!isHES && (
                  <FormMultiselect
                    title="Include HES(s)"
                    name="includeAgents"
                    value={includeAgents}
                    options={agents}
                    onChange={handleFieldChange}
                  />
                )}
              </>
            ) : (
              <>
                <AvailableSlots singleAgent allowAgentSelect={false} />
                <Row>
                  <Col>
                    <Checkbox
                      label="Lock Event"
                      name="lock"
                      value={lock}
                      onChange={handleCheckbox}
                    />
                    <FormTextBox
                      required={lock}
                      name="notes.officeNotes"
                      value={notes.officeNotes}
                      title="Notes"
                      placeholder=""
                      onChange={handleFieldChange}
                    />
                  </Col>
                </Row>
                <BookSlotsButton handleBookSlots={() => handleSaveClick()} />
              </>
            )}
          </Col>
        </Row>
      </EventSidebarBody>
      <EventSidebarFooter>
        {displayForm && (
          <SecondaryButton left onClick={onViewSlotsButtonClick}>
            View Available Slots
          </SecondaryButton>
        )}
      </EventSidebarFooter>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

FindHEASlotsFormCT.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default FindHEASlotsFormCT;
