import React, { useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import { PrimaryButton } from '@components/global/Buttons';
import { FormInput } from '@components/global/Form';
import { PageHeader } from '@pages/Components';
import { HorizontalLine, Skeleton, Header as Title } from '@components/global';
import { throwError } from '@utils/EventEmitter';
import { useHistory } from 'react-router-dom';
import { formatPhoneNumber } from '@utils/functions';
import Swal from 'sweetalert2/dist/sweetalert2';
import CustomerManager from '../../utils/APIManager/CustomerManager';
import {
  CardContainer,
  InformationIcon,
  Subtitle,
  FlexRow,
  MailIcon,
  HomeIcon,
  PhoneIcon,
} from './styles';

const FieldsContainer = styled.div`
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
  padding: 16px;
`;
const ListContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 34px;
`;
const ListItem = styled.div`
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease;
  padding: 8px;
  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }
`;

const Customers = () => {
  const history = useHistory();
  const [name, setName] = useState('');
  const [isSearched, setSearched] = useState(false);
  const [customerList, setCustomerList] = useState([]);

  const getCustomerList = useCallback(async () => {
    try {
      const response = await CustomerManager.getCustomersList({ limit: 20 });
      setCustomerList(response);
    } catch (err) {
      console.log(err);
      throwError('Error Occured while fetching Customers List');
    }
  }, []);

  const handleSearch = useCallback(async () => {
    try {
      if (!name) return false;
      const customerName = name.trim();
      const response = await CustomerManager.searchCustomer({ name: customerName });
      setSearched(true);
      setCustomerList(response);
      return false;
    } catch (err) {
      console.log(err);
      return throwError(err);
    }
  }, [name]);

  const handleItemClick = (accountId = '') => {
    if (!accountId) {
      return Swal.fire({ icon: 'error', title: 'Invalid Customer Account ID' });
    }
    return history.push(`/customer-details/${accountId}`);
  };

  const isSearchedReturnedNoRecords = isSearched && !customerList.length;
  const informativeText = 'No records found against this search.';

  useEffect(() => {
    if (!name) {
      getCustomerList();
    }
  }, [name]);

  if (!customerList.length && !isSearched)
    return (
      <>
        <PageHeader>Customer</PageHeader>
        <CardContainer>
          <Skeleton />
        </CardContainer>
      </>
    );
  return (
    <>
      <PageHeader>Customers</PageHeader>
      <CardContainer>
        <FlexRow gap="4px">
          <InformationIcon />
          <Title h4>
            Enter either the first name or last name for the search. Minimum 2 characters required.
          </Title>
        </FlexRow>
        <FieldsContainer>
          <FormInput
            value={name}
            placeholder="Enter Name"
            onChange={(e) => setName(e.target.value)}
          />
          <PrimaryButton onClick={handleSearch} disabled={!(name?.length > 1)}>
            Search
          </PrimaryButton>
        </FieldsContainer>
        {isSearchedReturnedNoRecords && (
          <Title h5>
            <InformationIcon /> {informativeText}
          </Title>
        )}
        <HorizontalLine />
        <ListContainer>
          {customerList?.map(({ accountId, customerName, address, phoneNumber, email }) => (
            <ListItem key={accountId} onClick={() => handleItemClick(accountId)}>
              <Title h4>{customerName}</Title>
              <Subtitle>
                <HomeIcon /> {address}
              </Subtitle>
              <Subtitle>
                <PhoneIcon />
                {formatPhoneNumber(phoneNumber)}
              </Subtitle>
              <Subtitle>
                <MailIcon /> {email}
              </Subtitle>
            </ListItem>
          ))}
        </ListContainer>
      </CardContainer>
    </>
  );
};

export default Customers;
