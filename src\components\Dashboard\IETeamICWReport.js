import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput, FormTextBox } from '@components/global/Form';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const IETeamICWReport = ({ record = {} }) => {
  const {
    dealId,
    timeStampClosedWon,
    timestampIncorrectlyClosedWonByIam,
    hesOriginalContractAmount,
    daysSinceIcw,
    icwFixedBy_Old_FullName: icwFixedByOldFullName,
    reasonForIncorrectlyCwByIam,
    incorrectlyCwNotes,
    isCap,
  } = record;

  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" isCap={isCap} />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="timeStampClosedWon"
            value={timeStampClosedWon}
            title="Time Stamp Closed Won"
            placeholder=""
          />
          <FormInput
            readOnly
            name="daysSinceIcw"
            value={daysSinceIcw}
            title="Days since ICW"
            placeholder=""
          />
          <FormInput
            readOnly
            name="reasonForIncorrectlyCwByIam"
            value={reasonForIncorrectlyCwByIam}
            title="Reason for Incorrectly CW by IAM"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="timestampIncorrectlyClosedWonByIam"
            value={timestampIncorrectlyClosedWonByIam}
            title="Timestamp Incorrectly Closed Won by IAM"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hesOriginalContractAmount"
            value={hesOriginalContractAmount}
            title="HES Original Contract Amount"
            placeholder=""
          />
          <FormInput
            readOnly
            name="icwFixedByOldFullName"
            value={icwFixedByOldFullName}
            title="ICW Fixed by (Old): Full Name"
            placeholder=""
          />
        </Col>
      </Row>
      <FormTextBox
        readOnly
        name="incorrectlyCwNotes"
        value={incorrectlyCwNotes}
        title="Incorrectly CW Notes"
        placeholder=""
      />
    </>
  );
};

IETeamICWReport.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    timeStampClosedWon: PropTypes.string,
    timestampIncorrectlyClosedWonByIam: PropTypes.string,
    hesOriginalContractAmount: PropTypes.string,
    daysSinceIcw: PropTypes.string,
    icwFixedBy_Old_FullName: PropTypes.string,
    reasonForIncorrectlyCwByIam: PropTypes.string,
    incorrectlyCwNotes: PropTypes.string,
    isCap: PropTypes.string,
  }),
};

export default IETeamICWReport;
