import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const PartitionScreenContainer = styled.div`
  display: flex;
  flex-direction: row;
  background-color: white;
  width: 100%;
  display: flex;
  flex: 1 1 auto;
  overflow-y: auto;
`;

const Column = styled.div`
  display: flex;
  flex: 1 1 auto;
  overflow-y: auto;
  width: ${({ width }) => `${10 * width}%`};
  border: ${({ showBorder }) => (showBorder ? 'solid 0.5px #DFE1E6' : '')};
  border-style: ${({ showBorder }) => (showBorder ? 'none none none solid' : '')};
`;

const ScreenPartitionView = ({ ratio = [10], children }) => {
  const childArray = !Array.isArray(children) ? [children] : children;

  return (
    <PartitionScreenContainer>
      {childArray.map((child, index) => {
        return (
          <Column width={ratio[index]} showBorder={index !== 0} key={ratio[index]}>
            {child}
          </Column>
        );
      })}
    </PartitionScreenContainer>
  );
};

ScreenPartitionView.propTypes = {
  ratio: PropTypes.arrayOf(PropTypes.number),
  children: PropTypes.node.isRequired,
};

export default ScreenPartitionView;
