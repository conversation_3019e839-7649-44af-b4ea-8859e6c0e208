import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';

const getSlots = (params) => {
  const { sfIds } = params;

  const requiredFields = {
    'Salesforce Id': sfIds.workOrderId,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return [params];
};

const create = (slot) => {
  const {
    type,
    date,
    oids,
    agentName,
    startEndTimes,
    startTime,
    endTime,
    sfIds,
    notes,
    numUnit,
  } = slot;
  const requiredFields = {
    HES: oids,
    'Start Time': startTime,
    Date: date,
    'Salesforce Id': sfIds.workOrderId || sfIds.accountId,
  };
  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  return {
    type,
    date,
    oids,
    agentName,
    startEndTimes,
    startTime,
    endTime,
    sfIds,
    notes,
    numUnit,
  };
};

const update = (params) => {
  const {
    id,
    associatedEventIds,
    associatedEventsId,
    type,
    date,
    oids,
    startEndTimes,
    startTime,
    sfIds,
    notes,
    lock,
  } = params;

  const requiredFields = {
    HES: oids,
    'Start Time': startTime,
    Date: date,
    'Work Order ID': sfIds.workOrderId,
    'Account ID': sfIds.accountId,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const updateData = {
    id,
    associatedEventIds,
    associatedEventsId,
    date,
    oids,
    startTime,
    type,
    startEndTimes,
    sfIds,
    notes,
    lock,
    jobLength: 1,
  };

  return updateData;
};

const subCTVist = { getSlots, create, update };

export default subCTVist;
