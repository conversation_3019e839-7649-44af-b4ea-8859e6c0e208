import { getDaysInCurrentMonth, isDayWeekend, checkLastUpdatedSwapMonth } from '@utils/dateUtils';
import moment from 'moment';

const desiredResult = [
  '2021-12-01',
  '2021-12-02',
  '2021-12-03',
  '2021-12-04',
  '2021-12-05',
  '2021-12-06',
  '2021-12-07',
  '2021-12-08',
  '2021-12-09',
  '2021-12-10',
  '2021-12-11',
  '2021-12-12',
  '2021-12-13',
  '2021-12-14',
  '2021-12-15',
  '2021-12-16',
  '2021-12-17',
  '2021-12-18',
  '2021-12-19',
  '2021-12-20',
  '2021-12-21',
  '2021-12-22',
  '2021-12-23',
  '2021-12-24',
  '2021-12-25',
  '2021-12-26',
  '2021-12-27',
  '2021-12-28',
  '2021-12-29',
  '2021-12-30',
  '2021-12-31',
];

// For updating weekends in Feburary 2022
const lastUpdated = moment('2022-01-01T00:00:00');
const firstOfMonth = moment('2021-12-01T00:00:00');
const lastOfMonth = moment('2022-01-31T00:00:00');
const firstDayToTriggerUpdate = moment('2022-01-16T00:00:00');
const lastDayToNotTriggerUpdate = moment('2022-01-15T00:00:00');

describe('Utils | dateUtils', () => {
  test('getDaysInCurrentMonth | first of month', () => {
    const result = getDaysInCurrentMonth('Thu Dec 1 2021').map(
      (date) => date.toISOString().split('T')[0],
    );
    expect(result).toStrictEqual(desiredResult);
  });

  test('getDaysInCurrentMonth | last of month', () => {
    const result = getDaysInCurrentMonth('Fri Dec 31 2021').map(
      (date) => date.toISOString().split('T')[0],
    );
    expect(result).toStrictEqual(desiredResult);
  });

  test('getDaysInCurrentMonth | early timestamp', () => {
    const result = getDaysInCurrentMonth('2021-12-17T01:49:29.916Z').map(
      (date) => date.toISOString().split('T')[0],
    );
    expect(result).toStrictEqual(desiredResult);
  });
  test('getDaysInCurrentMonth | late timestamp', () => {
    const result = getDaysInCurrentMonth('2021-12-17T23:49:29.916Z').map(
      (date) => date.toISOString().split('T')[0],
    );
    expect(result).toStrictEqual(desiredResult);
  });

  test('isDayWeekend | thursday', () => {
    expect(isDayWeekend(moment('Thu Dec 1 2021'))).toBe(false);
  });

  test('isDayWeekend | saturday', () => {
    expect(isDayWeekend(moment('Sat Dec 4 2021'))).toBe(true);
  });

  test('isDayWeekend | sunday', () => {
    expect(isDayWeekend(moment('Sun Dec 5 2021'))).toBe(true);
  });

  test('checkLastUpdatedSwapMonth | updates when lastUpdated is null', () => {
    expect(checkLastUpdatedSwapMonth(null)).toBe(true);
  });

  test('checkLastUpdatedSwapMonth | does not update with first of month', () => {
    expect(checkLastUpdatedSwapMonth(lastUpdated, firstOfMonth)).toBe(false);
  });

  test('checkLastUpdatedSwapMonth | updates with last of month', () => {
    expect(checkLastUpdatedSwapMonth(lastUpdated, lastOfMonth)).toBe(true);
  });

  test('checkLastUpdatedSwapMonth | does not update 14 days from end of month', () => {
    expect(checkLastUpdatedSwapMonth(lastUpdated, lastDayToNotTriggerUpdate)).toBe(false);
  });

  test('checkLastUpdatedSwapMonth | updates 15 days from end of month', () => {
    expect(checkLastUpdatedSwapMonth(lastUpdated, firstDayToTriggerUpdate)).toBe(true);
  });
});
