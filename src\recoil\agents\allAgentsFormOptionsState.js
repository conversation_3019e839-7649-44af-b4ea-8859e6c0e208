import { selector } from 'recoil';
import { decodeEventType } from '@homeworksenergy/utility-service';
import agentsSelectorFamily from '@recoil/agents/agentsSelectorFamily';
import { calendarTypeAtom } from '@recoil/app';

const allAgentsFormOptionsState = selector({
  key: 'allAgentsFormOptions',
  get: ({ get }) => {
    const calendarType = get(calendarTypeAtom);
    // TODO: There a is bug where calendarType is coming in as null which makes decodeEventTypes return an error. Defaulting a value resolves the issue.
    const { business: department, state } = decodeEventType(calendarType || '000000');
    // Get all agents for the business type
    const allAgents = get(agentsSelectorFamily({ [state]: [department] }));

    return allAgents.map(({ displayName, oid, region }) => {
      return { key: displayName, value: oid, region };
    });
  },
});

export default allAgentsFormOptionsState;
