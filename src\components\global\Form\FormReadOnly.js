import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import FormFieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';

const StyledFormFieldSecondaryLabel = styled.label`
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  text-transform: ${({ textTransform }) => textTransform};
  color: ${({ color }) => color};
`;

const FormReadOnly = ({
  title,
  name,
  compact = false,
  lgFont = false,
  secondaryText = '',
  lineHeight = false,
}) => {
  return (
    <FormFieldContainer fieldName={name} compact={compact}>
      <FormFieldLabel lgFont={lgFont} lineHeight={lineHeight} fieldType="readonly">
        {title}
      </FormFieldLabel>
      {secondaryText && (
        <StyledFormFieldSecondaryLabel>{secondaryText}</StyledFormFieldSecondaryLabel>
      )}
    </FormFieldContainer>
  );
};

FormReadOnly.propTypes = {
  title: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  compact: PropTypes.bool,
  lgFont: PropTypes.bool,
  secondaryText: PropTypes.string,
  lineHeight: PropTypes.bool,
};

export default FormReadOnly;
