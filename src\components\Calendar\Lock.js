import React, { useState } from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled, { css } from 'styled-components';
import { Lock as LockIcon } from '@styled-icons/boxicons-solid/Lock';
import { LockOpen as LockOpenIcon } from '@styled-icons/boxicons-solid/LockOpen';

import { preventParentElementClick } from '@utils/functions';
import { EventsManager } from '@utils/APIManager';
import { pinnedEventSelectorFamily } from '@recoil/event';

const lockIconStyle = css`
  height: 20px;
  margin-bottom: 5px;
  margin-left: 5px;
  & :hover {
    color: ${({ theme }) => theme.secondary[700]};
  }
`;

const LockedIcon = styled(LockIcon)`
  ${lockIconStyle}
`;
const UnlockedIcon = styled(LockOpenIcon)`
  ${lockIconStyle}
`;

const Lock = ({ event }) => {
  const { id, associatedEventsId, lock } = event;
  const [isLocked, setIsLocked] = useState(lock);
  const [pinnedEvent, setPinnedEvent] = useRecoilState(pinnedEventSelectorFamily(id));

  if (!id) return null;

  const onClickLock = async (e) => {
    preventParentElementClick(e);
    const response = await EventsManager.update({ associatedEventsId, lock: !isLocked });
    if (!response) return;
    if (pinnedEvent) setPinnedEvent(id);
    setIsLocked(!isLocked);
  };

  return isLocked ? (
    <LockedIcon onClick={(event) => onClickLock(event)} />
  ) : (
    <UnlockedIcon onClick={(event) => onClickLock(event)} />
  );
};

Lock.propTypes = {
  event: PropTypes.shape({
    id: PropTypes.string,
    associatedEventsId: PropTypes.string,
    lock: PropTypes.bool,
  }).isRequired,
};

export default Lock;
