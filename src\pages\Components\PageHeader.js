import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import Header from '@components/global/Header';
import { PinnedEvents } from '@components/Calendar/EventComponents';
import { ButtonContainer } from '@components/global/Form';

const StyledHeader = styled.div`
  display: flex;
  justify-content: space-between;
  flex: 0 1 auto;
  width: 100%;
  margin: 3px auto;
`;

const HeaderLeftContainer = styled.div`
  display: flex;
  flex-direction: row;
`;

const PageHeader = ({ buttons = [], filters = null, children = '' }) => {
  return (
    <StyledHeader>
      <HeaderLeftContainer>
        <Header h2>{children}</Header>
        {filters && filters}
        <PinnedEvents />
      </HeaderLeftContainer>
      {buttons.length > 0 && <ButtonContainer>{buttons}</ButtonContainer>}
    </StyledHeader>
  );
};

PageHeader.propTypes = {
  children: PropTypes.string,
  filters: PropTypes.node,
  buttons: PropTypes.arrayOf(PropTypes.node),
};

export default PageHeader;
