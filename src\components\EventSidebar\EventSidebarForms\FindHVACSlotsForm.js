import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useResetRecoilState } from 'recoil';

import { Row, Col, FormSelect } from '@components/global/Form';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import { availableSlotsAtom } from '@recoil/eventSidebar';

import { FindHVACInstallSlotsForm, FindHVACSiteEvalSlotsForm } from './index';

const FindHVACSlotsForm = ({ handleFindSlotsClick }) => {
  const resetAvailableSlots = useResetRecoilState(availableSlotsAtom);
  const [type, setType] = useState(null);

  const searchId = useLocation().pathname.split('/find-slots/')[1];

  useEffect(() => {
    if (searchId) {
      setType('000400');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchId]);

  const formTypeOption = [
    { key: 'HVAC Install', value: '000400' },
    { key: 'HVAC Site Eval', value: '000433' },
  ];

  const onClickBackButton = () => {
    resetAvailableSlots();
    setType(null);
  };

  const displayHVACInstallForm = type && type === '000400';
  const displayHVACSiteEvalForm = type && type !== '000400';

  return (
    <>
      {!type && (
        <EventSidebarBody>
          <Row>
            <Col size={2}>
              <FormSelect
                required
                title="Form Type"
                placeholder="Select Form Type"
                name="type"
                value={type}
                onChange={(e) => setType(e.target.value)}
                options={formTypeOption}
              />
            </Col>
          </Row>
        </EventSidebarBody>
      )}
      {displayHVACInstallForm && (
        <FindHVACInstallSlotsForm
          handleFindSlotsClick={handleFindSlotsClick}
          onClickBackButton={onClickBackButton}
          searchId={searchId}
        />
      )}
      {displayHVACSiteEvalForm && (
        <FindHVACSiteEvalSlotsForm
          handleFindSlotsClick={handleFindSlotsClick}
          onClickBackButton={onClickBackButton}
        />
      )}
    </>
  );
};

FindHVACSlotsForm.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
};

export default FindHVACSlotsForm;
