import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { ErrorCircle } from '@styled-icons/boxicons-solid/ErrorCircle';

const WarningIcon = styled(ErrorCircle)`
  height: 45px;
`;

const WarningsContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const WarningContainer = styled.div`
  display: flex;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  background-color: ${({ theme }) => theme.colors.warningLight};
  color: ${({ theme }) => theme.colors.warningDark};
`;

const WarningContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 10px;
`;

const WarningName = styled.div`
  text-transform: uppercase;
  font-weight: bold;
`;

const WarningDescription = styled.div`
  color: ${({ theme }) => theme.colors.regionHeader};
`;

const TownWarnings = ({ townWarnings = [] }) => {
  if (!townWarnings) return null;
  return (
    <WarningsContainer>
      {townWarnings.map(({ townWarningName, townWarningDescription }) => (
        <WarningContainer key={townWarningName}>
          <WarningIcon />
          <WarningContentContainer>
            <WarningName>{townWarningName}</WarningName>
            <WarningDescription>{townWarningDescription}</WarningDescription>
          </WarningContentContainer>
        </WarningContainer>
      ))}
    </WarningsContainer>
  );
};

TownWarnings.propTypes = {
  townWarnings: PropTypes.arrayOf(PropTypes.shape({})),
};

export default TownWarnings;
