import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { toggleSidebar } from '@utils/functions';
import Clickable from '@components/global/Clickable';
import { getUserCookie, isAuthorized, hasRole } from '@utils/AuthUtils';
import { Menu } from '@styled-icons/boxicons-regular/Menu';
import { Ghost } from '@styled-icons/boxicons-regular/Ghost';
import { Heart } from '@styled-icons/boxicons-regular/Heart';
import { Tree } from '@styled-icons/boxicons-solid/Tree';
import moment from 'moment';

import UserAccount from './UserAccount';

const MenuBarContainer = styled.div`
  display: flex;
  flex: 0 1 auto;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 10px 20px;
  background-color: white;
`;

const MenuIcon = styled(Menu)`
  height: 22px;
`;
const GhostIcon = styled(Ghost)`
  height: 22px;
`;
const HeartIcon = styled(Heart)`
  height: 22px;
`;
const TreeIcon = styled(Tree)`
  height: 22px;
`;

const holidayIcons = {
  '02-14': HeartIcon, // Valentine's Day
  '10-31': GhostIcon, // Halloween
  '12-24': TreeIcon, // Christmas
  '12-25': TreeIcon, // Christmas
};

const MenuBar = () => {
  const [userName, setUserName] = useState('');
  const date = moment().format('MM-DD');
  const Icon = holidayIcons[date] || MenuIcon;
  // Partner Agents are of Two Types i.e NON HWE Agent and HWE Agent
  // Non HWE Agent must view only Sub Hub Page
  const isPartner =
    !isAuthorized('Agent') &&
    (hasRole('Agent', 'Partners') || hasRole('Agent', 'Insulation Partners'));
  // We have Partner company that are now on the Insulation calendar
  // Insulation crews are created manually but a logged in user needs access to view those trucks
  const isAllowedAccess = hasRole('Scheduler', 'Insulation');
  useEffect(() => {
    const { name } = getUserCookie();
    setUserName(name);
  }, []);

  return (
    <MenuBarContainer>
      <Clickable className="hamburger-button" onClick={toggleSidebar}>
        {(!isPartner || isAllowedAccess) && <Icon />}
      </Clickable>
      <UserAccount userName={userName} />
    </MenuBarContainer>
  );
};

MenuBar.propTypes = {
  location: PropTypes.shape({
    pathname: PropTypes.string,
  }).isRequired,
};

export default MenuBar;
