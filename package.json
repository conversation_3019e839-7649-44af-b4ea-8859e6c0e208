{"name": "scheduler2-frontend", "version": "1.0.0", "description": "frontend code for scheduler 2.0", "main": "index.js", "scripts": {"dev": "webpack --mode development --watch --progress", "build": "webpack --mode production --progress", "prettier": "prettier --write \"**/*.js\"", "lint": "eslint src", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/HomeWorksEnergy/scheduler2-frontend.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/HomeWorksEnergy/scheduler2-frontend/issues"}, "homepage": "https://github.com/HomeWorksEnergy/scheduler2-frontend#readme", "dependencies": {"@babel/plugin-transform-runtime": "^7.12.1", "@babel/runtime": "^7.12.5", "@emotion/is-prop-valid": "^1.2.1", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@fortawesome/react-fontawesome": "^0.1.13", "@homeworksenergy/utility-service": "^1.2.4", "@styled-icons/bootstrap": "^10.33.0", "@styled-icons/boxicons-regular": "^10.23.0", "@styled-icons/boxicons-solid": "^10.23.0", "@styled-icons/fa-brands": "^10.47.0", "axios": "^1.8.4", "css-loader": "^7.1.2", "file-loader": "^6.2.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.29.4", "popper.js": "^1.16.1", "prop-types": "^15.7.2", "react": "^18.2.0", "react-bootstrap": "^2.10.9", "react-csv": "^2.2.2", "react-datepicker": "^8.3.0", "react-dnd": "^14.0.2", "react-dnd-html5-backend": "^14.0.0", "react-dom": "^18.2.0", "react-is": "^16.13.1", "react-router-dom": "^5.2.0", "react-widgets": "^5.0.3", "recoil": "^0.7.7", "style-loader": "^1.3.0", "styled-components": "^5.2.1", "styled-normalize": "^8.0.7", "sweetalert2": "^11.4.19", "sweetalert2-react-content": "^5.0.0"}, "devDependencies": {"@babel/cli": "^7.12.8", "@babel/core": "^7.12.9", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-syntax-jsx": "^7.14.5", "@babel/preset-env": "^7.14.8", "@babel/preset-react": "^7.14.5", "@babel/register": "^7.12.1", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.2", "babel-plugin-styled-components": "^1.12.0", "core-js": "^3.15.2", "eslint": "^6.8.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^6.15.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.2.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^1.7.0", "husky": "^7.0.2", "identity-obj-proxy": "^3.0.0", "jsdom": "^20.0.0", "prettier": "^1.19.1", "react-test-renderer": "^18.2.0", "regenerator-runtime": "^0.13.7", "sass": "^1.49.9", "svg-url-loader": "^8.0.0", "webpack": "^5.65.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.7.2", "webpack-manifest-plugin": "^4.1.1"}}