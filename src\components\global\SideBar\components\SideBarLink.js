import React from 'react';
import { useResetRecoilState, useSetRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { withRouter } from 'react-router-dom';
import { toggleSidebar } from '@utils/functions';
import Clickable from '@components/global/Clickable';
import { showSidebarState } from '@recoil/eventSidebar';
import { selectedPatternState } from '@recoil/eventPatterns';

const StyledSidebarCell = styled(Clickable)`
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 10px 15px;
  user-select: none;
  cursor: pointer;
`;

const SideBarLink = ({ children, history, route = null, url = null }) => {
  const resetSelectedPattern = useResetRecoilState(selectedPatternState);
  const setShowSidebarState = useSetRecoilState(showSidebarState);

  const goTo = () => {
    if (url) {
      return window.open(url, '_blank');
    }
    return history.push(`/${route}`);
  };
  return (
    <StyledSidebarCell
      onClick={() => {
        goTo();
        resetSelectedPattern();
        setShowSidebarState(false);
        toggleSidebar();
      }}
    >
      {children}
    </StyledSidebarCell>
  );
};

SideBarLink.propTypes = {
  children: PropTypes.string.isRequired,
  history: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
  route: PropTypes.string,
  url: PropTypes.string,
};

export default withRouter(SideBarLink);
