import React, { useEffect } from 'react';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';

import { formValuesState, activeTabState } from '@recoil/dataIntakeForm';
import { throwError } from '@utils/EventEmitter';
import swal from 'sweetalert2/dist/sweetalert2';
import validateRequiredParams from '@utils/validateRequiredParams';
import { parseJSXContentForSwalPopup } from '@utils/functions';
import { activeTabIndexAtom } from '@recoil/app';
import { hesAgentFormOptionsState } from '@recoil/agents';
import { showDuplicateRecordsAtom, duplicateLeadsAtom, stateAtom } from '@recoil/leadIntake';
import { useParams } from 'react-router-dom';
import { AgentsManager, SalesforceManager, CustomerManager } from '@utils/APIManager';
import { getLeadIntakeRequiredFields } from '../FormSchema/requiredFields';
import { useFieldsAndTabsForIntake } from '../utils/getters/useTabsAndFieldsForIntake';
import {
  doYouHaveHeatSwalPopup,
  isCustomerWorkingWithLocalAgencySwalPopup,
} from '../utils/setters/LeadIntakeEffect';
import { useLeadVendor } from '../utils/setters/useLeadVendor';
import { ScriptText } from '../LeadIntakeScript/styles';
import IntakeContainer from './IntakeContainer';
import useGetDuplicateLeads from '../utils/getters/useGetDuplicateLeads';
import useAccountsCreation from '../utils/setters/useAccountsCreation';

const LeadIntake = () => {
  const { leadId } = useParams();
  const formValues = useRecoilValue(formValuesState);
  const [activeTab, setActiveTab] = useRecoilState(activeTabState);
  const setHesAgents = useSetRecoilState(hesAgentFormOptionsState);

  const {
    dealId,
    numUnitsSchedulingToday,
    campaignId,
    doYouHaveHeat,
    heaOrHvac,
    atAnEvent,
    isCampaignIdValid,
    isAuditorValueOnSf,
    numUnitsInBuilding,
    howManyUnitsAreInAssociation,
    condoAssociation,
    isWorkingWithLocalCapAgency,
    discountedRateCode,
  } = formValues;
  const { setLeadVendorAndApprovalSoftware } = useLeadVendor();
  const { leadIntakeTabs } = useFieldsAndTabsForIntake(
    heaOrHvac,
    atAnEvent,
    isCampaignIdValid,
    leadId,
    isAuditorValueOnSf,
    nextTab,
    'MA',
  );
  const requiredFields = getLeadIntakeRequiredFields(formValues);
  const [activeTabIndex, setActiveTabIndex] = useRecoilState(activeTabIndexAtom(['tabs']));
  const setShowDuplicateRecordsTable = useSetRecoilState(showDuplicateRecordsAtom);
  const setDuplicateLeadsTableData = useSetRecoilState(duplicateLeadsAtom);
  const [state, setState] = useRecoilState(stateAtom);

  const fetchDuplicateLeads = useGetDuplicateLeads();

  useEffect(() => {
    setState('MA');
    const getAuditors = async () => {
      try {
        const response = await AgentsManager.getCrewsByStateAndDepartment({ MA: ['HEA'] });
        setHesAgents(response?.crews || []);
        return response;
      } catch (err) {
        console.error(err);
        return throwError(err);
      }
    };

    getAuditors();
  }, []);

  useEffect(() => {
    try {
      if (dealId) {
        CustomerManager.insertCustomer({
          dealId,
        });
      }
      return;
    } catch (error) {
      console.error(error);
      throwError(error);
    }
  }, [dealId]);

  // This function is hoisted, passed in hook above as well thats why its created with function keyword
  // Our Lead Intake Form runs on object based schema and sections but we can inject other React components as well
  // We Injected Schedule Event and Wrap up Components Manualy so we can reuse there functionality. Thus that components should
  // also have next and prev buttons with same functionality like we have on object based schema components.
  const nextTab = async () => {
    // We fetch duplicate accounts and duplicate leads when campaign id is not entered means its CIA
    const isCIA = activeTabIndex === 1 && !campaignId;
    if (isCIA) {
      await fetchDuplicateAccounts();
    }
    setActiveTabIndex(activeTabIndex + 1);
    setActiveTab(leadIntakeTabs[activeTabIndex + 1]?.name);
  };

  const validateNextTab = () => {
    if (requiredFields[leadIntakeTabs[activeTabIndex]?.name]) {
      // BA's doesnt fill Source Info Screen, So this validations below are only for CIAs
      // doYouHaveHeat Should be Yes if Customer is on discounted rate code otherwise we can't proceed!.
      // Raise Swal Popup Only If user is on SourceInfo Tab Screen
      const isIncomeEligibleAndNoHeatAvailable =
        (!doYouHaveHeat || doYouHaveHeat === 'No') &&
        activeTab === 'sourceInfo' &&
        discountedRateCode === 'Yes';

      if (isIncomeEligibleAndNoHeatAvailable) {
        return doYouHaveHeatSwalPopup();
      }
      // BA's doesnt fill Source Info Screen, So this validations below are only for CIAs
      // isWorkingWithLocalCapAgency Should be No, if Customer is working with local agency, We should not schedule an HEA.
      // Raise Swal Popup Only If user is on SourceInfo Tab Screen
      const isIncomeEligibleAndWorkingWithLocalAgency =
        (!isWorkingWithLocalCapAgency || isWorkingWithLocalCapAgency === 'Yes') &&
        activeTab === 'sourceInfo' &&
        discountedRateCode === 'Yes';

      if (isIncomeEligibleAndWorkingWithLocalAgency) {
        return isCustomerWorkingWithLocalAgencySwalPopup();
      }
      const missingParams = validateRequiredParams(
        requiredFields[leadIntakeTabs[activeTabIndex]?.name],
      );
      if (
        numUnitsInBuilding === '5+' ||
        numUnitsSchedulingToday === '5+' ||
        (howManyUnitsAreInAssociation === '5+' && condoAssociation === 'Yes')
      ) {
        return swal.fire({
          title: 'Customer Not Eligible',
          html: parseJSXContentForSwalPopup(
            <ScriptText>
              Unfortunately, the MassSave program does not allow HomeWorks to perform assessments at
              homes that are part of a 5 or more unit multi-family. The good news is that you should
              still be able to get an assessment through MassSave directly. Would you like the
              number for that? (You can reach them directly at ************).
            </ScriptText>,
          ),
          info: 'OK',
          icon: 'info',
        });
      }
      if (missingParams.length)
        return throwError(`Error on following fields: ${missingParams.join(', ')}.`);
    }
    return nextTab();
  };

  const fetchDuplicateAccounts = async () => {
    try {
      const duplicateAccounts = await SalesforceManager.getExistingDuplicateRecords(
        formValues,
        'Accounts',
      );
      if (duplicateAccounts.length) {
        setDuplicateLeadsTableData(duplicateAccounts);
        setShowDuplicateRecordsTable(true);
      } else {
        return fetchDuplicateLeads();
      }
      return duplicateAccounts;
    } catch (error) {
      console.log(error);
      return throwError(error);
    }
  };

  const proceedWithAccountsCreation = useAccountsCreation(leadId, nextTab);
  const handleSubmit = async () => {
    try {
      const updatedFormValues = setLeadVendorAndApprovalSoftware();
      return proceedWithAccountsCreation(updatedFormValues);
    } catch (err) {
      console.error(err);
      return throwError(err);
    }
  };

  if (!state) return null;

  return (
    <IntakeContainer
      state="MA"
      departments={['HEA']}
      handleSubmit={handleSubmit}
      validateNextTab={validateNextTab}
      nextTab={nextTab}
    />
  );
};

export default LeadIntake;
