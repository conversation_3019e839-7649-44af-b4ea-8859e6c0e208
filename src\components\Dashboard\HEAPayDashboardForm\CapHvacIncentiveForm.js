import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const CapHvacIncentiveForm = ({ record = {} }) => {
  const { finalContractPrice, opportunityId, hvacIncentive, contractNumber } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput
            readOnly
            name="opportunityId"
            value={opportunityId}
            title="Opportunity Id"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hvacIncentive"
            value={hvacIncentive}
            title="HVAC Incentive"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="finalContractPrice"
            value={finalContractPrice}
            title="Final Contract Price"
            placeholder=""
          />
          <FormInput
            readOnly
            name="contractNumber"
            value={contractNumber}
            title="Contract Number"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

CapHvacIncentiveForm.propTypes = {
  record: PropTypes.shape({
    finalContractPrice: PropTypes.string,
    opportunityId: PropTypes.string,
    hvacIncentive: PropTypes.string,
    contractNumber: PropTypes.string,
  }),
};

export default CapHvacIncentiveForm;
