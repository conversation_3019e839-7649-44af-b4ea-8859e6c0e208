import React, { useState, useMemo, useCallback } from 'react';
import styled from 'styled-components';
import moment from 'moment';
import { useSetRecoilState } from 'recoil';
import { searchAdminState } from '@recoil/admin';
import { InstallationScheduledCalendar } from '@components/global';
import HEAMapView from '@components/Calendar/MapComponents/HEAMapView';
import CalendarControlHeader from '@components/Calendar/CalendarComponents/CalendarControlHeader';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
`;

const ContentContainer = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const Container = styled.div`
  flex: 1;
  overflow: ${({ isMap }) => (isMap ? 'hidden' : 'auto')};
  padding: ${({ isMap }) => (isMap ? '16px' : '0')};
  display: ${({ show }) => (show ? 'flex' : 'none')};
  flex-direction: column;
`;

const MapViewContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const MapViewHeader = styled.div`
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid ${({ theme }) => theme.colors.calendarBorder};
`;

const ScheduleTitle = styled.h1`
  margin: 0;
  padding: 15px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.calendarBorder};
`;

const SCHEDULE_TYPE = '010000';
const SCHEDULE_TITLE = 'HEA Schedule - CT';

const getDateRange = (date) => {
  const startOfDay = date.clone().startOf('day');
  return {
    start: startOfDay,
    end: startOfDay
      .clone()
      .add(1, 'day')
      .endOf('day'),
  };
};

const ViewHEAScheduleCT = () => {
  const [currentView, setCurrentView] = useState('calendar');
  const [selectedDate, setSelectedDate] = useState(moment().startOf('day'));
  const setSearchTerm = useSetRecoilState(searchAdminState);

  // memoize date range
  const mapDateRange = useMemo(() => getDateRange(selectedDate), [selectedDate]);

  // memoize callbacks
  const handleMapViewClick = useCallback(() => {
    setCurrentView('map');
    // Clearing search terms when switching to map view
    setSearchTerm('');
  }, [setSearchTerm]);

  const handleBackToCalendar = useCallback(() => {
    setCurrentView('calendar');
    // Clearing search terms when switching back to calendar view
    setSearchTerm('');
    if (selectedDate.isSame(moment(), 'day')) {
      setSelectedDate(moment().startOf('day'));
    }
  }, [setSearchTerm, selectedDate]);

  // Date navigation handler for CalendarControlHeader
  const handleDateChange = useCallback((direction) => {
    if (direction === '+') {
      // Next day
      setSelectedDate((prev) => prev.clone().add(1, 'day'));
    } else if (direction === '-') {
      // Previous day
      setSelectedDate((prev) => prev.clone().subtract(1, 'day'));
    } else {
      // Today (when no direction or empty string)
      setSelectedDate(moment().startOf('day'));
    }
  }, []);

  return (
    <PageContainer>
      <ContentContainer>
        <Container show={currentView === 'calendar'}>
          <InstallationScheduledCalendar
            title={SCHEDULE_TITLE}
            type={SCHEDULE_TYPE}
            includeRegionHeaders={false}
            onMapViewClick={handleMapViewClick}
          />
        </Container>

        {currentView === 'map' && (
          <Container show isMap>
            <MapViewContainer>
              <MapViewHeader>
                <ScheduleTitle>{SCHEDULE_TITLE}</ScheduleTitle>
                <CalendarControlHeader
                  startDate={selectedDate}
                  scrollCalendar={handleDateChange}
                  isMonthly={false}
                  showMonthYearPicker={false}
                  isMapView
                  showFilters={false}
                  showDatePicker={false}
                  customDateDisplay={selectedDate.format('MMM DD, YYYY')}
                  onBackToCalendar={handleBackToCalendar}
                />
              </MapViewHeader>
              <HEAMapView
                startDate={mapDateRange.start}
                endDate={mapDateRange.end}
                type={SCHEDULE_TYPE}
                selectedDate={selectedDate}
              />
            </MapViewContainer>
          </Container>
        )}
      </ContentContainer>
    </PageContainer>
  );
};

export default ViewHEAScheduleCT;
