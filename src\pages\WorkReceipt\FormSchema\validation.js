import Swal from 'sweetalert2/dist/sweetalert2';
import { capWorkReceiptValidations } from '../CAPWR/FormSchema/validation';

const isValidYear = (year) => {
  if (year.toString().length === 2) {
    return Swal.fire({
      icon: 'error',
      title: 'Invalid Error on Roadblock Section',
      html: 'A 4-digit year is required. The year should be at least 1400 and less than 2099.',
      confirmButtonText: 'OK',
      showCancelButton: false,
    });
  }
  return Number(year) >= 1400 && Number(year) <= 2099;
};

const marketingRateValidations = (formValues, deals) => {
  let isFormValid = true;
  const errorMessage = {
    customerInfo: 'Invalid Field Entry on Customer Info',
    roadblocksInfo: 'Invalid Field Entry on Roadblocks Info',
    roadblocksInfoPrelim: 'Invalid Year Entry on Roadblocks Info',
    basInfo: 'Invalid Field Entry on BAS Info',
    hvacInfo: 'Invalid Field Entry on HVAC Info',
    massSaveInfo: 'Invalid Field Entry on Mass Save Info',
    wxbiInfo: 'Invalid Field Entry on Incentive Info',
    dhwInfo: 'Invalid Field Entry on DHW Quoting Info',
    postHea: 'Invalid Field Entry on Post HEA Info',
  };

  const showRoadBlockInfoError = (dealId) => {
    return Swal.fire({
      icon: 'error',
      title: `${errorMessage.roadblocksInfo}: Deal #${dealId}`,
      confirmButtonText: 'OK',
      showCancelButton: false,
    });
  };

  for (let iterator = 0; iterator < formValues.length; iterator++) {
    const {
      customerName,
      customerAddress,
      cityStateZip,
      customerEmail,
      customerPhone,
      heaNotes,
      siteId,
      whatRegionIsThis,
      auditorName,
      auditorEmail,
      auditorCell,
      intakeIssue,
      hvacQuality,
      heaHvacNotes,
      hvacInterest,
      gasAvailable,
      yearHouseBuilt,
      heatingSystemType,
      houseAirConditioned,
      squareFootage,
      heightPerStory,
      correctedHouseVolume,
      numberOfOccupants,
      numberOfBedrooms,
      notesForCrew,
      interestedInHvac,
      disclosureCstFaileddraftAstmosphericNaturalConditions,
      disclosureCstFailedSpillageSealedSystem,
      disclosureCstFailedCoAtmostphericSystem,
      disclosureCstFailedCoSealedSystem,
      disclosureCstDraftSpillageDetail,
      disclosureCstHighCoDetail,
      incentiveAmtAtticFloor,
      massSaveWorkAmtAirSealingNoSync,
      massSaveWorkAmtWeatherizationNoSync,
      massSaveWorkAmtDuctSealingNoSync,
      massSaveWorkAmtDuctInsulationNoSync,
      massSaveWorkAmtInsulationRemoval,
      massSaveWorkAmtInsulationRemomval,
      massSaveProgramIncentive,
      wxbiFloorRemovalSqFt,
      wxbiFlooringType,
      wxbiNailedUnsecured,
      wxbiCanBeDensePacked,
      disclosureWallsTempAccess,
      systemsFailingCoMultiselect,
      heatingSystemCO,
      domesticHotWaterCarbonMonoxide,
      failedCstOther,
      otherCombustionAppliance,
      areasOnWorkscope,
      failedDraftDetailMulti,
      failedSpillageDetailMulti,
      disclosureCstFailedCoGasOvenLevel2,
      hesDhwVisitResult,
      dhwHesEmp,
      hesDhwQuoteAmount,
      hesDhwProductQuoted,
      hesDhwFollowUpDate,
      hesDhwOfficeInstallNotes,
      wasDhwQuoted,
      heaVisitResult,
      preferredLanguage,
    } = formValues[iterator];
    if (
      !customerName ||
      !customerAddress ||
      !cityStateZip ||
      !customerEmail ||
      !customerPhone ||
      !heaNotes ||
      !siteId ||
      !whatRegionIsThis ||
      !auditorName ||
      !auditorEmail ||
      !auditorCell ||
      !intakeIssue
    ) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.customerInfo}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (
      [
        massSaveWorkAmtAirSealingNoSync,
        massSaveWorkAmtWeatherizationNoSync,
        massSaveWorkAmtDuctSealingNoSync,
      ].filter((value) => parseInt(value, 10) > 0)?.length > 0 &&
      areasOnWorkscope?.length === 0
    ) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.customerInfo}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (!notesForCrew) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.massSaveInfo}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (
      (massSaveWorkAmtAirSealingNoSync > 0 ||
        massSaveWorkAmtWeatherizationNoSync > 0 ||
        massSaveWorkAmtDuctSealingNoSync > 0 ||
        massSaveWorkAmtDuctInsulationNoSync > 0) &&
      (!massSaveProgramIncentive || massSaveProgramIncentive <= 0)
    ) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.massSaveInfo}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (!yearHouseBuilt || !isValidYear(yearHouseBuilt)) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.roadblocksInfoPrelim}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (disclosureCstFailedCoGasOvenLevel2 === 'Yes' && !otherCombustionAppliance) {
      showRoadBlockInfoError(deals[iterator]);
      isFormValid = false;
      break;
    }
    if (
      [
        disclosureCstFaileddraftAstmosphericNaturalConditions,
        disclosureCstFailedSpillageSealedSystem,
      ].includes('Yes')
    ) {
      if (!disclosureCstDraftSpillageDetail) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }

      if (
        disclosureCstFaileddraftAstmosphericNaturalConditions === 'Yes' &&
        (!failedDraftDetailMulti.length || !failedSpillageDetailMulti.length)
      ) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
      if (disclosureCstFailedSpillageSealedSystem === 'Yes' && !failedSpillageDetailMulti.length) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
    }
    if (
      [disclosureCstFailedCoAtmostphericSystem, disclosureCstFailedCoSealedSystem].includes('Yes')
    ) {
      if (!disclosureCstHighCoDetail) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }

      if (!systemsFailingCoMultiselect.length) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
      }

      if (systemsFailingCoMultiselect.includes('Heating System') && !heatingSystemCO) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
      if (systemsFailingCoMultiselect.includes('DHW') && !domesticHotWaterCarbonMonoxide) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
      if (
        systemsFailingCoMultiselect.includes('Other') &&
        (!otherCombustionAppliance || !failedCstOther)
      ) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
    }

    if (
      [
        massSaveWorkAmtAirSealingNoSync,
        massSaveWorkAmtWeatherizationNoSync,
        massSaveWorkAmtDuctSealingNoSync,
        massSaveWorkAmtDuctInsulationNoSync,
        massSaveWorkAmtInsulationRemoval,
        massSaveWorkAmtInsulationRemomval,
      ].some((field) => field > 0) &&
      [
        heatingSystemType,
        houseAirConditioned,
        squareFootage,
        heightPerStory,
        correctedHouseVolume,
        numberOfOccupants,
        numberOfBedrooms,
      ].some((field) => !field)
    ) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.basInfo}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (
      incentiveAmtAtticFloor > 0 &&
      [wxbiFloorRemovalSqFt, wxbiFlooringType, wxbiNailedUnsecured, wxbiCanBeDensePacked].some(
        (option) => !option,
      )
    ) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.wxbiInfo}: Deal #${deals[iterator]}`,
        text: 'Fill the required fields on Flooring Removal Incentive Section as well',
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (interestedInHvac !== 'No - Out of Territory') {
      if (!hvacQuality || !heaHvacNotes || !hvacInterest || !gasAvailable) {
        Swal.fire({
          icon: 'error',
          title: `${errorMessage.hvacInfo}: Deal #${deals[iterator]}`,
          confirmButtonText: 'OK',
          showCancelButton: false,
        });
        isFormValid = false;
        break;
      }
    }

    if (
      wasDhwQuoted &&
      wasDhwQuoted === 'Yes' &&
      (!hesDhwVisitResult ||
        !dhwHesEmp ||
        !hesDhwQuoteAmount ||
        !hesDhwProductQuoted ||
        !hesDhwFollowUpDate ||
        !hesDhwOfficeInstallNotes)
    ) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.dhwInfo}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (!disclosureWallsTempAccess) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.roadblocksInfo}: Deal #${deals[iterator]}`,
        text: 'Temp Access field on roadblock should be filled.',
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (heaVisitResult === 'HEA Performed' && !preferredLanguage) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.postHea}: Deal #${deals[iterator]}`,
        text:
          'Please select a preferred language from the picklist. This field cannot be left empty.',
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }
  }
  return isFormValid;
};

const offlineWorkReceiptValidations = (formValues, deals) => {
  let isFormValid = true;
  const errorMessage = {
    customerInfo: 'Invalid Field Entry on Customer Info',
    roadblocksInfo: 'Invalid Field Entry on Roadblocks Info',
    roadblocksInfoPrelim: 'Invalid Year Entry on Roadblocks Info',
    basInfo: 'Invalid Field Entry on BAS Info',
    hvacInfo: 'Invalid Field Entry on HVAC Info',
    postHea: 'Invalid Field Entry on Post HEA Info',
  };

  const showRoadBlockInfoError = (dealId) => {
    return Swal.fire({
      icon: 'error',
      title: `${errorMessage.roadblocksInfo}: Deal #${dealId}`,
      confirmButtonText: 'OK',
      showCancelButton: false,
    });
  };

  for (let iterator = 0; iterator < formValues.length; iterator++) {
    const {
      customerName,
      customerAddress,
      cityStateZip,
      customerEmail,
      customerPhone,
      heaNotes,
      siteId,
      whatRegionIsThis,
      auditorName,
      auditorEmail,
      auditorCell,
      intakeIssue,
      hvacQuality,
      heaHvacNotes,
      hvacInterest,
      gasAvailable,
      yearHouseBuilt,
      interestedInHvac,
      disclosureCstFaileddraftAstmosphericNaturalConditions,
      disclosureCstFailedSpillageSealedSystem,
      disclosureCstFailedCoAtmostphericSystem,
      disclosureCstFailedCoSealedSystem,
      disclosureCstDraftSpillageDetail,
      disclosureCstHighCoDetail,
      systemsFailingCoMultiselect,
      heatingSystemCO,
      domesticHotWaterCarbonMonoxide,
      failedCstOther,
      otherCombustionAppliance,
      failedDraftDetailMulti,
      failedSpillageDetailMulti,
      disclosureCstFailedCoGasOvenLevel2,
      heaVisitResult,
      preferredLanguage,
    } = formValues[iterator];
    if (
      !customerName ||
      !customerAddress ||
      !cityStateZip ||
      !customerEmail ||
      !customerPhone ||
      !heaNotes ||
      !siteId ||
      !whatRegionIsThis ||
      !auditorName ||
      !auditorEmail ||
      !auditorCell ||
      !intakeIssue
    ) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.customerInfo}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (!yearHouseBuilt || !isValidYear(yearHouseBuilt)) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.roadblocksInfoPrelim}: Deal #${deals[iterator]}`,
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }

    if (disclosureCstFailedCoGasOvenLevel2 === 'Yes' && !otherCombustionAppliance) {
      showRoadBlockInfoError(deals[iterator]);
      isFormValid = false;
      break;
    }
    if (
      [
        disclosureCstFaileddraftAstmosphericNaturalConditions,
        disclosureCstFailedSpillageSealedSystem,
      ].includes('Yes')
    ) {
      if (!disclosureCstDraftSpillageDetail) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }

      if (
        disclosureCstFaileddraftAstmosphericNaturalConditions === 'Yes' &&
        (!failedDraftDetailMulti.length || !failedSpillageDetailMulti.length)
      ) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
      if (disclosureCstFailedSpillageSealedSystem === 'Yes' && !failedSpillageDetailMulti.length) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
    }
    if (
      [disclosureCstFailedCoAtmostphericSystem, disclosureCstFailedCoSealedSystem].includes('Yes')
    ) {
      if (!disclosureCstHighCoDetail) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }

      if (!systemsFailingCoMultiselect.length) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
      }

      if (systemsFailingCoMultiselect.includes('Heating System') && !heatingSystemCO) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
      if (systemsFailingCoMultiselect.includes('DHW') && !domesticHotWaterCarbonMonoxide) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
      if (
        systemsFailingCoMultiselect.includes('Other') &&
        (!otherCombustionAppliance || !failedCstOther)
      ) {
        showRoadBlockInfoError(deals[iterator]);
        isFormValid = false;
        break;
      }
    }

    if (interestedInHvac !== 'No - Out of Territory') {
      if (!hvacQuality || !heaHvacNotes || !hvacInterest || !gasAvailable) {
        Swal.fire({
          icon: 'error',
          title: `${errorMessage.hvacInfo}: Deal #${deals[iterator]}`,
          confirmButtonText: 'OK',
          showCancelButton: false,
        });
        isFormValid = false;
        break;
      }
    }

    if (heaVisitResult === 'HEA Performed' && !preferredLanguage) {
      Swal.fire({
        icon: 'error',
        title: `${errorMessage.postHea}: Deal #${deals[iterator]}`,
        text:
          'Please select a preferred language from the picklist. This field cannot be left empty.',
        confirmButtonText: 'OK',
        showCancelButton: false,
      });
      isFormValid = false;
      break;
    }
  }

  return isFormValid;
};

export const workReceiptValidations = (workReceiptMode) => {
  if (workReceiptMode === 'Offline') {
    return offlineWorkReceiptValidations;
  }
  if (workReceiptMode === 'CAP') {
    return capWorkReceiptValidations;
  }
  return marketingRateValidations;
};
