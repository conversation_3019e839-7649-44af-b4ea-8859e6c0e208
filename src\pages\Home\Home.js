/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import styled from 'styled-components';
import moment from 'moment';
import { Header } from '@components/global';
import { agentEventsAtom } from '@recoil/home';
import { isAuthorized, hasRole } from '@utils/AuthUtils';
import { UsersManager } from '@utils/APIManager';
import { checkLastUpdatedSwapMonth } from '@utils/dateUtils';
import SwapDaysSidebar from '@components/SwapDays/SwapDaysSidebar';
import { SecondaryButton } from '@components/global/Buttons';
import SwapDaysNotificationModal from '@components/SwapDays/SwapDaysNotificationModal';
import { Container } from './sharedStyledComponents';
import EventsForDay from './HomePageComponents/EventsPage';
import PartnersLink from './HomePageComponents/PartnersLink';
import DashboardPage from './HomePageComponents/DashboardPage';

const HomePageLinksContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: max-content;
  margin-bottom: 10px;
`;
const HelpfulLink = styled.a``;
const HomePage = () => {
  const [agentEvents, setAgentEvents] = useRecoilState(agentEventsAtom);
  const [swapDaysSidebarOpen, setSwapDaysSidebarOpen] = useState(false);
  const [showSwapDaysButton, setShowSwapDaysButton] = useState(false);
  const [swapDaysNotificationOpen, setSwapDaysNotificationOpen] = useState(false);
  const [lastUpdated, setLastUpdated] = useState('');
  const [dateText, setDateText] = useState('');
  const { oid, roles } = JSON.parse(localStorage.getItem('user'));

  useEffect(() => {
    const fetchAgentEvents = async () => {
      const events = await UsersManager.getCurrentUserEventsForToday();
      if (roles?.findIndex((r) => r.department === 'HEA' && r.role === 'Agent') !== -1) {
        const { lastUpdatedSwapMonth } = await UsersManager.getUserLastUpdatedSwapMonth();
        const dateString = lastUpdatedSwapMonth
          ? moment(lastUpdatedSwapMonth)
              .add(2, 'month')
              .format('MMMM YYYY')
          : 'the next month';
        // NOTE: Uncomment line 42-46 to reactivate the
        // const shouldUserUpdateSwapDays = checkLastUpdatedSwapMonth(lastUpdatedSwapMonth);
        // setShowSwapDaysButton(shouldUserUpdateSwapDays);
        // setSwapDaysNotificationOpen(shouldUserUpdateSwapDays);
        // setDateText(dateString);
        // setLastUpdated(lastUpdatedSwapMonth);
      }
      const formatEvents = events.map((event) => {
        const {
          accountId,
          dealId,
          operationsId,
          opportunityId,
          salesVisitId,
          hvacVisitId,
          siteId,
        } = event.sfIds;
        return {
          ...event,
          selectedUnitSfIds: {
            accountId,
            dealId,
            operationsId,
            opportunityId,
            salesVisitId,
            hvacVisitId,
            siteId,
          },
        };
      });
      setAgentEvents(formatEvents);
    };
    fetchAgentEvents();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setAgentEvents]);

  const disableSwapDays = isAuthorized('Manager', 'HEA') || !isAuthorized('Agent', 'HEA');
  const displayDashboard =
    (hasRole('Manager', 'HEA') || hasRole('Agent', 'HEA')) && !hasRole('Agent', 'HEA', 'CT');

  return (
    <Container>
      <Header h1>Welcome to the HomeWorks Energy Scheduler</Header>
      {showSwapDaysButton && (
        <SecondaryButton right onClick={() => setSwapDaysSidebarOpen(!swapDaysSidebarOpen)}>
          Select Swap Days
        </SecondaryButton>
      )}
      <EventsForDay />
      {isAuthorized('Agent', 'Partners', true) && <PartnersLink />}
      {agentEvents?.length === 0 && (
        <HomePageLinksContainer>
          <HelpfulLink
            href="https://homeworksenergy.atlassian.net/wiki/spaces/HOM/pages/402554881/HVAC+Install+Scheduler+FAQ"
            target="_blank"
            rel="noopener noreferrer"
          >
            HVAC Scheduler FAQ
          </HelpfulLink>
          {isAuthorized('Super User', 'All') && (
            <HelpfulLink
              href="https://app.powerbi.com/reportEmbed?reportId=4cb0a3b1-bb49-4997-9bf5-77f04837939d&autoAuth=true&ctid=559755ae-c909-4f42-af1e-a1404fdb0629&config=eyJjbHVzdGVyVXJsIjoiaHR0cHM6Ly93YWJpLXVzLW5vcnRoLWNlbnRyYWwtcmVkaXJlY3QuYW5hbHlzaXMud2luZG93cy5uZXQvIn0%3D"
              target="_blank"
              rel="noopener noreferrer"
            >
              HVAC Power BI report
            </HelpfulLink>
          )}
        </HomePageLinksContainer>
      )}

      {displayDashboard && <DashboardPage />}

      {!disableSwapDays && (
        <>
          <SwapDaysSidebar
            lastUpdated={lastUpdated}
            oid={oid}
            sidebarOpen={swapDaysSidebarOpen}
            closeSidebar={() => setSwapDaysSidebarOpen(false)}
          />
          <SwapDaysNotificationModal
            isOpen={swapDaysNotificationOpen}
            handleCloseModal={() => setSwapDaysNotificationOpen(false)}
            handleOpenSidebar={() => {
              setSwapDaysSidebarOpen(true);
              setSwapDaysNotificationOpen(false);
            }}
            date={dateText}
          />
        </>
      )}
    </Container>
  );
};
export default HomePage;
