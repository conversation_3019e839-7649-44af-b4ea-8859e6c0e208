import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import EventsSearchResult from './EventsSearchResult';

const ResultsListContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const EventsSearchResultsList = ({ searchResults }) => {
  const renderSearchResults = () => {
    let previousDate = null;

    return searchResults.map((event) => {
      const { date } = event;
      const isNewDate = date !== previousDate;
      previousDate = date;

      return <EventsSearchResult displayDateHeader={isNewDate} event={event} key={event.id} />;
    });
  };

  return <ResultsListContainer>{renderSearchResults()}</ResultsListContainer>;
};

EventsSearchResultsList.propTypes = {
  searchResults: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
};

export default EventsSearchResultsList;
