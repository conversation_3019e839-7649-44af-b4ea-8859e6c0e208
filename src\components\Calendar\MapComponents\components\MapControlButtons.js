import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { MapControls, ControlButton } from '../HEAMapViewStyles';

/** Map Buttons */
const MapControlButtons = ({ googleMapRef }) => {
  // Center map on Connecticut
  const centerMapOnCT = useCallback(() => {
    if (googleMapRef.current) {
      googleMapRef.current.setCenter({ lat: 41.6032, lng: -73.0877 });
      googleMapRef.current.setZoom(10);
    }
  }, [googleMapRef]);

  return (
    <MapControls>
      <ControlButton onClick={centerMapOnCT}>Center on CT</ControlButton>
    </MapControls>
  );
};

MapControlButtons.propTypes = {
  googleMapRef: PropTypes.shape({ current: PropTypes.instanceOf(Object) }).isRequired,
};

export default MapControlButtons;
