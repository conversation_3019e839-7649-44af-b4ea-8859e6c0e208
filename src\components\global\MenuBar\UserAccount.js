import React, { useState } from 'react';
import PropTypes from 'prop-types';

import { SessionManager } from '@utils/APIManager';
import DropDownMenu from '@components/global/DropDownMenu/DropDownMenu';
import NameInitialsImage from '@components/global/NameInitialsImage/NameInitialsImage';

const UserAccount = ({ userName }) => {
  // eslint-disable-next-line no-unused-vars
  const [dropDownList, setDropDownList] = useState([{ text: 'Log Out', onClick: () => logOut() }]);
  let nameInitials;
  if (userName.length)
    nameInitials = `${userName
      .split(' ')
      .map((name) => {
        return name?.[0]?.toUpperCase();
      })
      .join('')}`;

  const logOut = () => {
    SessionManager.logOut();
  };

  return (
    <DropDownMenu
      DropDownIcon={<NameInitialsImage text={nameInitials} />}
      listItems={dropDownList}
    />
  );
};

UserAccount.propTypes = {
  userName: PropTypes.string.isRequired,
};

export default UserAccount;
