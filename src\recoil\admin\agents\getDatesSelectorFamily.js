import { selectorFamily } from 'recoil';
import moment from 'moment';

import { EventsManager } from '@utils/APIManager';
import calendarMonthStartDateAtom from './calendarMonthStartDateAtom';
import refreshAgentDatesAtom from './refreshAgentDatesAtom';

const getDatesSelectorFamily = selectorFamily({
  key: 'getDatesSelectorFamily',
  get: (oid) => async ({ get }) => {
    get(refreshAgentDatesAtom);
    if (!oid) return {};
    const newMonthStart = get(calendarMonthStartDateAtom);

    const calendarStart = moment(newMonthStart)
      .startOf('month')
      .startOf('week');
    const calendarEnd = moment(newMonthStart) // might be still the previous month (like the 29th or something)
      .endOf('week') // make sure we're in new month
      .endOf('month') // end of new month
      .endOf('week'); // end of week displayed on the calendar

    const [{ openDates, closeDates, disableDates }] = await EventsManager.loadEvents(
      calendarStart,
      calendarEnd,
      null,
      [oid],
    );
    return {
      openDates: formattedDates(openDates),
      closeDates: formattedDates(closeDates),
      disableDates: formattedDates(disableDates),
    };
  },
});

// the date time value is returned as string in json response from back end
// these values needs to be of type date time for react datepicker
const formattedDates = (dates) => {
  return dates.map((date) => {
    return moment(date, 'YYYY-MM-DD').toDate();
  });
};

export default getDatesSelectorFamily;
