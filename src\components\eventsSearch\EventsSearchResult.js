import React from 'react';
import moment from 'moment';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { useSetRecoilState } from 'recoil';

import { isEventsSearchState } from '@recoil/eventsSearch';
import { DepartmentEvent } from '@components/Calendar/EventComponents';
import { DateHeader } from '@components/global';

const SearchResultContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const DATE_FORMAT = 'MM/DD/YYYY';

const EventsSearchResult = ({ event, displayDateHeader = false }) => {
  const { id, associatedEventIds, date } = event;
  const setIsEventsSearch = useSetRecoilState(isEventsSearchState);

  const isFirstInSeries = id === associatedEventIds[0];

  if (!isFirstInSeries) return null;

  return (
    <SearchResultContainer>
      {displayDateHeader && <DateHeader dateMoment={moment(date, DATE_FORMAT)} />}

      <DepartmentEvent
        event={event}
        ignoreMultiDay
        lockable={false}
        onClick={() => setIsEventsSearch(false)}
      />
    </SearchResultContainer>
  );
};

EventsSearchResult.propTypes = {
  event: PropTypes.shape({
    id: PropTypes.string,
    associatedEventIds: PropTypes.arrayOf(PropTypes.number),
    date: PropTypes.string,
  }).isRequired,
  displayDateHeader: PropTypes.bool,
};

export default EventsSearchResult;
