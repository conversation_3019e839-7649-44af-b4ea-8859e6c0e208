import React, { useState } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { RadioButton } from '@components/global';
import { Row } from '@components/global/Form';
import ToolTip from '@components/global/Tooltip/Tooltip';
import { QuestionCircleFill } from '@styled-icons/bootstrap/QuestionCircleFill';

import FormFieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';
import RecoilFieldOptions from './RecoilFieldOptions';

const FormRadioButtonsContainer = styled.div`
  ${({ border }) => {
    return border
      ? `border: 1px solid ${({ theme }) => theme.secondary[100]};
         box-sizing: border-box;
         border-radius: 8px;`
      : '';
  }}
  padding: ${({ border }) => {
    return border ? '10px' : '';
  }};
  ${({ horizontal }) => {
    return horizontal ? 'width: 100%' : '';
  }};
`;

const FormRadioButtonsContent = styled.form`
  display: flex;
  gap: ${({ maxGap }) => (maxGap ? '12px' : '0px')};
  flex-flow: ${({ column }) => {
    return column ? 'column wrap' : '';
  }};
  ${({ horizontal }) => {
    return !horizontal ? 'width: 100%' : '';
  }};

  flex-direction: ${({ column }) => {
    return column ? 'column' : 'row';
  }};
  padding: 5px 16px 0px 0px;
`;

const RadioButtonWrapperStyled = styled.div`
  display: flex;
  align-items: center;
`;

const QuestionIconStyled = styled(QuestionCircleFill)`
  height: 12px;
`;
const QuestionIconTextStyled = styled.u`
  margin-left: 6px;
  margin-top: -3px;
`;

const RenderButtonContainer = styled(Row)`
  ${({ horizontal }) => {
    return horizontal ? 'width: 50%' : '';
  }};
`;

const FormRadioButtons = (props) => {
  const [hoveringText, setHoveringText] = useState(null);
  const {
    title,
    name,
    options = [{ key: '', value: '' }],
    recoilOptions = null,
    onChange = () => {},
    type = 'string',
    border = false,
    required = false,
    column = true,
    compact = false,
    horizontal = false,
    showDescription = false,
    maxGap = false,
    weight = 500,
    description = '',
  } = props;

  let { value } = props;
  if (value == null) value = '';
  if (type === 'object') value = JSON.stringify(value);

  // TODO: is there any way to extract this "type" functionality to be used across all Form components?
  const handleChange = (e) => {
    const changeEvent =
      // eslint-disable-next-line no-nested-ternary
      type === 'number'
        ? { target: { name: e.target.name, value: parseInt(e.target.value, 10) } }
        : type === 'object'
        ? { target: { name: e.target.name, value: JSON.parse(e.target.value) } }
        : e;
    return onChange(changeEvent);
  };

  const renderRadioButtons = () => {
    return (
      <FormRadioButtonsContent column={column} maxGap={maxGap} horizontal={horizontal}>
        {options.map((option) => {
          const { key, description } = option;
          let { value: radioBtnValue } = option;

          if (type === 'object') radioBtnValue = JSON.stringify(option);

          return (
            <RadioButtonWrapperStyled key={JSON.stringify(option)}>
              <RadioButton
                name={name}
                label={key || option}
                checked={value === (radioBtnValue || option)}
                onChange={handleChange}
                value={radioBtnValue || option}
              />
              {showDescription && (
                <>
                  <QuestionIconStyled />
                  <QuestionIconTextStyled
                    onMouseEnter={() => setHoveringText(radioBtnValue)}
                    onMouseLeave={() => setHoveringText(null)}
                  >
                    What&apos;s this?
                  </QuestionIconTextStyled>
                  {hoveringText === radioBtnValue && <ToolTip text={description} />}
                </>
              )}
            </RadioButtonWrapperStyled>
          );
        })}
      </FormRadioButtonsContent>
    );
  };
  if (recoilOptions) return <RecoilFieldOptions Component={FormRadioButtons} {...props} />;
  return (
    <FormRadioButtonsContainer border={border} horizontal={horizontal}>
      <FormFieldContainer
        required={required}
        compact={compact}
        horizontal={horizontal}
        fieldName={name}
      >
        <FormFieldLabel weight={weight} description={description} horizontal={horizontal}>
          {title}
        </FormFieldLabel>
        <RenderButtonContainer horizontal={horizontal}>
          {renderRadioButtons()}
        </RenderButtonContainer>
      </FormFieldContainer>
    </FormRadioButtonsContainer>
  );
};

FormRadioButtons.propTypes = {
  title: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.shape({ label: PropTypes.string }),
      PropTypes.string,
      PropTypes.number,
    ]),
  ),
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.shape({})]),
  onChange: PropTypes.func,
  type: PropTypes.oneOf(['number', 'string', 'object']),
  border: PropTypes.bool,
  required: PropTypes.bool,
  column: PropTypes.bool,
  recoilOptions: PropTypes.shape({}),
  compact: PropTypes.bool,
  horizontal: PropTypes.bool,
  showDescription: PropTypes.bool,
  maxGap: PropTypes.bool,
  weight: PropTypes.number,
  description: PropTypes.string,
};

export default FormRadioButtons;
