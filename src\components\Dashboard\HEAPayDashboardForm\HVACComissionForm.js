import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HVACComissionForm = ({ record = {} }) => {
  const { finalContractPrice, opportunityId } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput
            readOnly
            name="opportunityId"
            value={opportunityId}
            title="Opportunity Id"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="finalContractPrice"
            value={finalContractPrice}
            title="Final Contract Price"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

HVACComissionForm.propTypes = {
  record: PropTypes.shape({
    finalContractPrice: PropTypes.string,
    opportunityId: PropTypes.string,
  }),
};

export default HVACComissionForm;
