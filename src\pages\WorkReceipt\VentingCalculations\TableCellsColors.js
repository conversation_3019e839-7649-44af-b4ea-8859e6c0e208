import styled from 'styled-components';

const Table = styled.table`
  border-collapse: collapse;
  width: 100%;
  margin-top: 20px;
`;

export const TableCell = styled.td`
  padding: 8px;
  text-align: center;
  white-space: ${(props) => (props.whiteSpace ? props.whiteSpace : 'auto')};
  background-color: ${(props) => (props.backgroundColor ? props.backgroundColor : 'auto')};
  font-weight: ${(props) => (props.fontWeight ? props.fontWeight : 'auto')};
  margin: ${(props) => props.margin ?? 'auto'};
  text-align: ${(props) => props.textAlign ?? 'auto'};
  font-size: ${(props) => props.fontSize ?? 'auto'};
`;

const TableRow = styled.tr`
  &:nth-child(even) {
    background-color: #f2f2f2;
  }
`;

const TableHeader = styled.th`
  background-color: #f2f2f2;
  padding: 8px;
  text-align: center;
`;

const DarkGreenCell = styled(TableCell)`
  background-color: rgba(0, 100, 0, 0.6); /* Dark Green */
  color: #fff;
`;

const LightGreenCell = styled(TableCell)`
  background-color: rgba(50, 205, 50, 0.8); /* Light Green */
`;

const LightYellowCell = styled(TableCell)`
  background-color: rgba(255, 217, 0, 0.8); /* Light Yellow */
`;

const OrangeCell = styled(TableCell)`
  background-color: rgba(255, 140, 0, 0.8); /* Orange */
`;

const RedCell = styled(TableCell)`
  background-color: rgba(255, 0, 0, 0.8); /* Red */
`;

const Label = styled(styled.div)``;

export {
  DarkGreenCell,
  LightGreenCell,
  LightYellowCell,
  OrangeCell,
  RedCell,
  TableHeader,
  TableRow,
  Table,
  Label,
};
