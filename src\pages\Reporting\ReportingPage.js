import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import moment from 'moment';
import { Header } from '@components/global';
import { PrimaryButton } from '@components/global/Buttons';
import { Row, Col, FormDatePicker, FormSelect } from '@components/global/Form';
import { ReportingManager } from '@utils/APIManager';
import Table from '../../components/global/Table/index';

const StyledReportingPage = styled.div``;

const ReportingPage = () => {
  const [startDate, setStartDate] = useState(moment());
  const [endDate, setEndDate] = useState(moment().add(1, 'week'));
  const [department, setDepartment] = useState(1);
  const [data, setData] = useState([]);

  const departmentOptions = [
    { key: 'HEA ', value: 1 },
    { key: 'HVAC-Sales  ', value: 2 },
    { key: 'HVAC-Install ', value: 3 },
    { key: 'Insulation ', value: 6 },
  ];

  const isHvacInstall = department === 3;

  const columnHeader = isHvacInstall
    ? ['Date', 'Category', 'Capacity', 'Utilization']
    : ['Date', 'Region', 'Capacity', 'Utilization'];

  const regionDict = isHvacInstall
    ? {
        installer: 'Install Crew',
        electrician: 'Electrician',
      }
    : {
        NS: 'North Shore',
        CC: 'Cape Cod',
        WEMA: 'Western MA',
        FH: 'Fermingham',
        MW: 'Metro West',
        SS: 'South Shore',
        SSA: 'South Shore A',
        SSB: 'South Shore B',
      };

  useEffect(() => {
    setData([]);
  }, [department, startDate, endDate]);

  const handleStartDateChange = (e) => {
    const { value: newStartDate } = e.target;

    const newEndDate = moment(newStartDate).add(1, 'week');
    setStartDate(moment(newStartDate));
    setEndDate(moment(newEndDate));
  };

  const handleEndDateChange = (e) => {
    const { value: newEndDate } = e.target;
    setEndDate(moment(newEndDate));
  };

  const handleDepartmentChange = (e) => {
    const { value } = e.target;
    setDepartment(value);
  };

  const handleUtilizationReport = async () => {
    const response = await ReportingManager.generateUtilizationReport({
      startDate,
      endDate,
      department,
    });
    formatData(response);
  };

  const formatData = (data) => {
    // Use array reduce to calculate total capacity and utilization by date and region
    const result = Object.entries(data.capacity).reduce((totalData, [, capacityData]) => {
      // Create a new object to avoid modifying the original totalData
      const updatedTotalData = { ...totalData };
      // Iterate through capacity data
      const capacityDataEntries = Object.entries(capacityData);
      for (let i = 0; i < capacityDataEntries.length; i++) {
        const [date, dateRegions] = capacityDataEntries[i];
        // Initialize the total capacity and utilization for the date if not exists
        updatedTotalData[date] = updatedTotalData[date] || {};
        // Iterate through regions for the date
        const regions = Object.keys(dateRegions);
        for (let j = 0; j < regions.length; j++) {
          const region = regions[j];
          // Initialize the total capacity and utilization for the region if not exists
          updatedTotalData[date][region] = updatedTotalData[date][region] || {
            capacity: 0,
            utilization: 0, // Initialize utilization as a number
            utilizationPercentage: 0, // Initialize utilization as a number
          };
          // Add the capacity to the total capacity
          updatedTotalData[date][region].capacity += dateRegions[region];

          // Check if utilization data exists for the region and date
          if (data.utilization[date] && data.utilization[date][region]) {
            // Add the utilization to the total utilization
            const utilizationData = data.utilization[date][region];
            const utilizationByRegionNDate = Object.values(utilizationData).reduce(
              (sum, value) => sum + value,
              0,
            );
            let addCount = 0;
            if (updatedTotalData[date][region].capacity === 0) addCount = 1;
            updatedTotalData[date][region].utilization = utilizationByRegionNDate;
            if (utilizationByRegionNDate === 0 && updatedTotalData[date][region].capacity === 0)
              updatedTotalData[date][region].utilizationPercentage = 0;
            else
              updatedTotalData[date][region].utilizationPercentage = (
                ((utilizationByRegionNDate + addCount) /
                  (updatedTotalData[date][region].capacity + addCount)) *
                100
              ).toFixed(2);
          }
        }
      }

      return updatedTotalData;
    }, {});

    let formatResult = result;

    if (endDate.diff(startDate, 'days') > 7) {
      formatResult = getDataByWeek(Object.keys(result), result);
    }

    setData(formatResult);
  };

  const getDataByWeek = (dates, result) => {
    const datesByWeek = {};
    const regionDetailsByWeek = {};
    // Group dates by week and accumulate region details
    for (let i = 0; i < dates.length; i++) {
      const date = dates[i];
      const weekNumber = moment(date, 'MM-DD-YYYY').week();
      datesByWeek[weekNumber] = datesByWeek[weekNumber] || [];
      datesByWeek[weekNumber].push(date);
      if (!regionDetailsByWeek[weekNumber]) {
        regionDetailsByWeek[weekNumber] = {};
      }
      regionDetailsByWeek[weekNumber][date] = result[date];
    }
    const returnObject = {};
    // Process data by week
    const datesByWeekDetails = Object.keys(datesByWeek);
    for (let i = 0; i < datesByWeekDetails.length; i++) {
      const weekNumber = datesByWeekDetails[i];
      const datesInWeek = datesByWeek[weekNumber];
      const combinedObject = {};
      for (let j = 0; j < datesInWeek.length; j++) {
        const date = datesInWeek[j];
        const regions = regionDetailsByWeek[weekNumber][date];
        // Iterate through the regions for each date
        const regionsDetail = Object.keys(regions);
        for (let k = 0; k < regionsDetail.length; k++) {
          const region = regionsDetail[k];
          const { capacity, utilization } = regions[region];
          // Initialize combinedData if it doesn't exist
          if (!combinedObject[region]) {
            combinedObject[region] = { capacity: 0, utilization: 0 };
          }
          // Add capacity and utilization
          combinedObject[region].capacity += capacity;
          combinedObject[region].utilization += utilization;
          combinedObject[region].utilizationPercentage = (
            (combinedObject[region].utilization / combinedObject[region].capacity) *
            100
          ).toFixed(2);
        }
      }
      returnObject[`${datesInWeek[0]}-${datesInWeek[datesInWeek.length - 1]}`] = combinedObject;
    }
    return returnObject;
  };

  const renderTable = () => {
    const columnData = [];
    // Iterate through the object keys (dates)
    const dates = Object.keys(data);
    for (let i = 0; i < dates.length; i++) {
      const date = dates[i];
      const regionsDetails = data[date];
      const regions = Object.keys(regionsDetails);
      // Iterate through the regions for each date
      for (let j = 0; j < regions.length; j++) {
        const region = regions[j];
        const { capacity, utilizationPercentage } = regionsDetails[region];
        // Push data to the columnData array
        const column = {
          id: `${date}-${region}`,
          tableData: [date, regionDict[region], capacity, `${utilizationPercentage || 0}%`],
        };
        columnData.push(column);
      }
    }
    return (
      columnData.length > 0 && <Table header={columnHeader} rows={columnData} editable={false} />
    );
  };
  return (
    <StyledReportingPage>
      <Header h2>Reports</Header>
      <Row>
        <Col size={1}>
          <FormSelect
            title="Department"
            type="number" // number type since this is returning dept id
            placeholder="Select Department"
            name="department"
            value={department}
            onChange={handleDepartmentChange}
            options={departmentOptions}
          />
        </Col>
        <Col>
          <FormDatePicker
            title="Start Date"
            name="startDate"
            value={startDate}
            onChange={handleStartDateChange}
            required
            useSyntheticEvent
          />
        </Col>
        <Col>
          <FormDatePicker
            title="End Date"
            name="endDate"
            value={endDate}
            minDate={startDate}
            onChange={handleEndDateChange}
            required
            useSyntheticEvent
          />
        </Col>
      </Row>

      <PrimaryButton onClick={handleUtilizationReport}>Generate Utilization Report</PrimaryButton>
      {renderTable()}
    </StyledReportingPage>
  );
};

export default ReportingPage;
