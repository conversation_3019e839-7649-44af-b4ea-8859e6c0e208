import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';

import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  FormMultiselect,
} from '@components/global/Form';
import { LoadingIndicator } from '@components/global';
import { SalesforceManager } from '@utils/APIManager';
import { SecondaryButton } from '@components/global/Buttons/Buttons';
import {
  eventTypeOptionsSelectorFamily,
  jobAttributesSelectorFamily,
  regionsSelector,
} from '@recoil/app';
import { selectedEventState, isSlotsSearchAtom } from '@recoil/eventSidebar';

const FindInsulationSlotsForm = (props) => {
  const { handleFindSlotsClick } = props;

  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);

  // Calling this 'slotInfo' for ease of reading.
  // Using the same 'selectedEvent' atom as elsewhere in the app
  const [slotInfo, setSlotInfo] = useRecoilState(selectedEventState);

  const regionOptions = useRecoilValue(regionsSelector);

  const [loading, setLoading] = useState(false);

  const searchId = useLocation().pathname.split('/find-slots/')[1];

  const eventTypes = useRecoilValue(
    eventTypeOptionsSelectorFamily({
      departmentName: 'Insulation',
      showGroups: false,
      stateCode: slotInfo?.type?.slice(0, 2),
    }),
  );

  const defaultType = eventTypes[0]?.value;

  const { sfIds, attributes, type, jobLength, amount, regions } = slotInfo;

  useEffect(() => {
    // If no type for create event, set defaultType
    if (!type) setSlotInfo({ ...slotInfo, type: defaultType });
  }, [type, defaultType, setSlotInfo, slotInfo]);

  useEffect(() => {
    if (searchId)
      handleOperationsIdChange({ target: { name: 'sfIds.operationsId', value: searchId } });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchId]);

  const handleOperationsIdChange = async (e) => {
    const { value: operationsId } = e.target;
    // If not the correct length, set the id but don't search
    if (![15, 18].includes(operationsId.length)) {
      setSlotInfo({ ...slotInfo, sfIds: { operationsId } });
      return;
    }
    setLoading(true);
    const salesforceInfo = await SalesforceManager.getInsulationEventInfoWithOperationsId(
      operationsId,
    );

    const { amount } = salesforceInfo;

    const jobLength = getJobLengthFromAmount(amount);

    setSlotInfo({ ...slotInfo, ...salesforceInfo, jobLength });
    setLoading(false);
  };

  const getJobLengthFromAmount = (amount) => {
    let jobLength = Math.ceil(amount / 6000);
    if (jobLength > 5) jobLength = 5;
    return jobLength;
  };

  // Added second param 'slotInfo' defaulting to the state value.
  // This is for the race condition of setting (for example) the operationsID form field and
  // the other returned information gathered from the operationsID
  const handleFieldChange = (e, updatedEvent = slotInfo) => {
    handleFormFieldChange(e, updatedEvent, setSlotInfo);
  };

  const handleAmountChange = (e) => {
    const { value: amount } = e.target;

    const jobLength = getJobLengthFromAmount(amount);

    handleFieldChange(e, { ...slotInfo, jobLength });
  };

  // Insulation Sidebar is shown with information for WX before acutally booking the appointment.
  const handleBookSlot = () => {
    setIsSlotsSearch(false);
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col size={2}>
            <HeaderTitle>Book Appointment</HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            <FormInput
              required
              search // TODO: implement this
              title="operations id/wx scheduling id (not site id)"
              placeholder="Enter Operations ID"
              name="sfIds.operationsId"
              value={sfIds.operationsId}
              onChange={handleOperationsIdChange}
              data-testid="find-insulation-slots-operations-id-input"
            />
            <FormSelect
              title="job type"
              name="type"
              value={type}
              options={eventTypes}
              onChange={handleFieldChange}
            />
            <FormMultiselect
              title="requirement(s)"
              name="attributes"
              value={attributes}
              recoilOptions={jobAttributesSelectorFamily({ type })}
              onChange={handleFieldChange}
            />
            <FormMultiselect
              required
              title="Region(s)"
              name="regions"
              options={regionOptions}
              onChange={handleFieldChange}
              value={regions}
            />
            <FormInput
              required
              title="Contract Amount"
              name="amount"
              type="number"
              value={amount}
              onChange={handleAmountChange}
            />
            {amount > 0 && (
              <FormInput readOnly title="Job Length" name="jobLength" value={jobLength} />
            )}
            <SecondaryButton center onClick={() => handleFindSlotsClick()}>
              View Available Slots
            </SecondaryButton>
            <AvailableSlots />
            <BookSlotsButton handleBookSlots={() => handleBookSlot()} />
          </Col>
        </Row>
      </EventSidebarBody>
      {/* Footer necessary even when unused to allow closing sidebar on mobile */}
      <EventSidebarFooter />
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

FindInsulationSlotsForm.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
};

export default FindInsulationSlotsForm;
