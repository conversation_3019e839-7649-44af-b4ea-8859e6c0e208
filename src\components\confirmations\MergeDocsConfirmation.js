import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled, { ThemeProvider } from 'styled-components';

import { selectedEventState } from '@recoil/eventSidebar';

import { handleFormFieldChange, FormInput } from '@components/global/Form';

const StyledConfirmationBody = styled.div``;

const MergeDocsConfirmation = ({ theme }) => {
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);
  const { mergeDocsTitle } = selectedEvent;

  const handleFieldChange = (e, updatedEvent = selectedEvent) => {
    return handleFormFieldChange(e, updatedEvent, setSelectedEvent);
  };

  return (
    <ThemeProvider theme={theme}>
      <StyledConfirmationBody>
        <FormInput
          required
          name="mergeDocsTitle"
          value={mergeDocsTitle}
          title="Title for merged document:"
          placeholder=""
          onChange={handleFieldChange}
        />
      </StyledConfirmationBody>
    </ThemeProvider>
  );
};

const mergeDocsConfirmation = async (RecoilBridge, getMergeDocsName, theme) => {
  const { createSwalWithTheme } = await import('@config/swalConfig');
  const swal = createSwalWithTheme(theme);

  return swal.fire({
    titleText: 'Merge PDFs',
    html: (
      <RecoilBridge>
        <MergeDocsConfirmation theme={theme} />
      </RecoilBridge>
    ),
    icon: 'warning',
    confirmButtonText: 'Yes',
    showCancelButton: true,
    cancelButtonText: 'No',
  });
};

MergeDocsConfirmation.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  theme: PropTypes.object.isRequired,
};

export { mergeDocsConfirmation };

export default MergeDocsConfirmation;
