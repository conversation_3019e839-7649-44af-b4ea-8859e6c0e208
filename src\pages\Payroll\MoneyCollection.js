import React, { useState } from 'react';
import styled from 'styled-components';
import moment from 'moment';

import { PayrollManager } from '@utils/APIManager';
import { Container } from '@components/global/Form';
import { Header } from '@components/global';
import StyledLabel from './StyledLabel';
import StyledButton from './StyledButton';
import StyledCsvLink from './StyledCsvLink';

const StyledContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const StyledButtonContainer = styled(Container)`
  display: flex;
`;

const StyledInput = styled.input`
  display: none;
`;

const BottomPaddingContainer = styled(Container)`
  padding-bottom: 15px;
`;

const StyledI = styled.i``;

const StyledTableRow = styled.tr`
  display: flex;
  justify-content: space-between;
  width: 95%;
  padding-bottom: 15px;
`;

const StyledHeaders = styled.td`
  font-size: 16px;
  font-weight: bold;
  text-decoration: underline;
  width: 11%;
`;

const StyledTdText = styled.td`
  font-size: 16px;
  width: 11%;
`;

const MoneyCollection = () => {
  const { uploadMoneyCollectionDocument, uploadMoneyCollectionErrorFile } = PayrollManager;
  const [recordErrors, setRecordErrors] = useState([]);
  const [errorsForCsvFile, setErrorsForCsvFile] = useState([]);
  const [displayErrors, setDisplayErrors] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState('');
  const headers = Object.keys(recordErrors[0] || {});
  const headersForErrorsCsv = Object.keys(errorsForCsvFile[0] || {});

  const handleMoneyCollectionUpload = async (event) => {
    const { files } = event.target;
    const data = new FormData();
    data.append('file', files[0]);
    const { errors, csvFileErrors, fileName } = await uploadMoneyCollectionDocument(data);
    if (errors) {
      setRecordErrors(errors);
      setErrorsForCsvFile(csvFileErrors);
      setUploadedFileName(fileName);
      setDisplayErrors(true);
    }
  };

  const handleMoneyCollectionErrorFileUpload = async (event) => {
    const { files } = event.target;
    const data = new FormData();
    data.append('file', files[0]);
    const processedErrorFile = await uploadMoneyCollectionErrorFile(data);
    if (processedErrorFile) {
      setErrorsForCsvFile(processedErrorFile);
      setUploadedFileName('Payzer');
      setDisplayErrors(true);
    }
  };

  const handleGoBackClick = () => {
    setRecordErrors([]);
    setErrorsForCsvFile([]);
    setUploadedFileName('');
    setDisplayErrors(false);
  };

  const renderRecordsWithErrors = () => {
    const errorDisplay = [];
    errorDisplay.push(
      <StyledTableRow>
        {headers.map((header) => (
          <StyledHeaders key={header}>{header}</StyledHeaders>
        ))}
      </StyledTableRow>,
    );
    for (let k = 0; k < recordErrors.length; k++) {
      const record = recordErrors[k];
      errorDisplay.push(
        <StyledTableRow>
          {headers.map((header) => (
            <StyledTdText key={record[header]}>{record[header]}</StyledTdText>
          ))}
        </StyledTableRow>,
      );
    }

    return errorDisplay;
  };

  return (
    <StyledContainer>
      <Header h1>Money Collection</Header>
      {!displayErrors && (
        <Container>
          <StyledButtonContainer>
            <StyledLabel htmlFor="money-collection">
              <StyledI /> Upload
              <StyledInput
                id="money-collection"
                type="file"
                name="money-collection"
                onChange={handleMoneyCollectionUpload}
              />
            </StyledLabel>
            <StyledLabel htmlFor="money-collection-error-file">
              <StyledI /> Fix Errors
              <StyledInput
                id="money-collection-error-file"
                type="file"
                name="money-collection-error-file"
                onChange={handleMoneyCollectionErrorFileUpload}
              />
            </StyledLabel>
          </StyledButtonContainer>
        </Container>
      )}
      {displayErrors && (
        <BottomPaddingContainer>
          <StyledButton type="button" onClick={handleGoBackClick}>
            Go Back
          </StyledButton>
          <StyledCsvLink
            data={errorsForCsvFile}
            headers={headersForErrorsCsv}
            filename={`${uploadedFileName}_MoneyCollectionErrors_${moment().format(
              'MM-DD-YYYY HHmmss',
            )}.csv`}
          >
            Download CSV
          </StyledCsvLink>
        </BottomPaddingContainer>
      )}
      {displayErrors && recordErrors?.length > 0 && renderRecordsWithErrors()}
    </StyledContainer>
  );
};

export default MoneyCollection;
