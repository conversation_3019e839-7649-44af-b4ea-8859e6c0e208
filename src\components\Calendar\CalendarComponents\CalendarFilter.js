import React, { useState, useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { calendarTypeAtom } from '@recoil/app';
import { UtilityManager } from '@utils/APIManager';

const FilterContainer = styled.div`
  margin-right: 15px;
`;

const StyledFilter = styled.select`
  background: ${({ theme }) => theme.secondary[200]};
  width: 185px;
  height: 30px;
  border: none;
`;

const agentTypeFilterOptions = [
  { name: 'All Agents', key: '' },
  { name: 'Project Manager', key: 'projectManager' },
  { name: 'Electrician', key: 'electrician' },
  { name: 'Installer', key: 'installer' },
  { name: 'Plumbing Installer', key: 'plumbingInstaller' },
  { name: 'Installer / Electrician', key: 'installerNElectrician' },
];

const CalendarFilter = ({
  setFilter = () => {},
  isRegion = false,
  isEventType = false,
  value = '',
}) => {
  const [selectOptions, setSelectOptions] = useState([]);
  const eventType = useRecoilValue(calendarTypeAtom);
  useEffect(() => {
    const getSelectOptions = async (eventType) => {
      if (eventType === '000400') return setSelectOptions(agentTypeFilterOptions);
      if (isRegion) {
        const regions = await UtilityManager.getRegionsForEventType(eventType);
        if (!regions || !regions.length) {
          return setSelectOptions([{ name: 'Not Available', key: null }]);
        }
        return setSelectOptions([{ name: 'All Regions', key: 0 }, ...regions]);
      }
      if (isEventType) {
        const eventTypes = await UtilityManager.getEventTypesForEventType(eventType);
        if (!eventTypes || !eventTypes.length) {
          return setSelectOptions([{ name: 'Not Available', key: null }]);
        }
        return setSelectOptions([{ name: 'All Event Types', key: '' }, ...eventTypes]);
      }
      return false;
    };
    getSelectOptions(eventType);
  }, [eventType, isRegion, isEventType]);

  return (
    <FilterContainer>
      {selectOptions?.[0]?.name !== 'Not Available' && (
        <StyledFilter value={value} onChange={(e) => setFilter(e.target.value)}>
          {selectOptions.map((option) => {
            const { name, key } = option;
            return (
              <option value={key} key={name}>
                {name}
              </option>
            );
          })}
        </StyledFilter>
      )}
    </FilterContainer>
  );
};

CalendarFilter.propTypes = {
  setFilter: PropTypes.func,
  isRegion: PropTypes.bool,
  isEventType: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

export default CalendarFilter;
