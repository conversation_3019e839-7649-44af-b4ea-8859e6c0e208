import { useRecoilValue } from 'recoil';
import { decodeEventType } from '@homeworksenergy/utility-service';

import { calendarTypeAtom } from '@recoil/app';
import { draggingEventAtom } from '@recoil/event';

const useValidateCell = (cell) => {
  const calendarType = useRecoilValue(calendarTypeAtom);
  const draggingEvent = useRecoilValue(draggingEventAtom);

  // If no event being dragged, nothing changes
  if (!draggingEvent) return false;

  const { rowDisabled, status } = cell;

  // If the row is disabled, the cell is automatically disabled
  if (rowDisabled) return true;

  const { business: department } = decodeEventType(calendarType);

  // Check if date is closed
  const isClosed = department === 'HEA' ? status === 'closed' : false;

  return isClosed;
};

export default useValidateCell;
