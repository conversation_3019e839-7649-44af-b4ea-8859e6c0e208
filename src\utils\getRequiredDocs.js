import { resultingRequiredDocs as capHeaDocRequiredDocs } from '@utils/businessLogic/capHeaDocRepoBusinessLogic';
import { resultingRequiredDocs as hvacSalesDocRequiredDocs } from '@utils/businessLogic/capHvacSalesDocRepoBusinessLogic';

const getCapHeaRequiredDocs = (resultingQues, department) => {
  const resultingRequiredDocs =
    department === 'HEA-CAP' ? capHeaDocRequiredDocs : hvacSalesDocRequiredDocs;
  const requiredDocsByDept = resultingRequiredDocs[department];
  const { required, ...questions } = requiredDocsByDept;

  let requiredDocs = [...required];
  let notRequiredDocs = [];
  Object.keys(resultingQues).forEach((key) => {
    const value = resultingQues[key];
    const valueExists = questions?.[key]?.[value];
    if (valueExists) {
      const { required = [], notRequired = [] } = valueExists;
      requiredDocs = [...requiredDocs, ...required];
      notRequiredDocs = [...notRequiredDocs, ...notRequired];
    }
  });
  const filteredRequiredDocs = requiredDocs.filter((doc) => {
    return !notRequiredDocs.includes(doc);
  });
  return filteredRequiredDocs;
};

export { getCapHeaRequiredDocs };
