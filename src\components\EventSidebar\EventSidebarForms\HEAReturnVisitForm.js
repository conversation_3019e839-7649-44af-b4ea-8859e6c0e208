import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue } from 'recoil';
import moment from 'moment';

import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarHeader, {
  Header<PERSON><PERSON><PERSON>,
  HeaderLabel,
} from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';

import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import {
  Row,
  Col,
  FormSelect,
  FormRadioButtons,
  handleFormFieldChange,
  FormMultiselect,
  FormTextBox,
} from '@components/global/Form';
import { selectedEventState } from '@recoil/eventSidebar';
import { returnVisitReasons } from '@utils/businessLogic/heaBusinessLogic';
import { agentsFormOptionsSelector } from '@recoil/agents';

const HEAReturnVisitForm = ({ handleReturnClick, handleFindSlotsClick }) => {
  const [returnType, setReturnType] = useState(null);
  const [event, setEvent] = useRecoilState(selectedEventState);
  const agents = useRecoilValue(agentsFormOptionsSelector);
  const date = moment().format('MM/DD/YYYY');
  const {
    address: { displayAddress },
    address,
    returnReason,
    notes: { fieldNotes } = {},
    includeAgents = [],
  } = event;

  const typeOptions = [
    { key: '1 Hour', value: '000004' },
    { key: '2 Hour', value: '000005' },
  ];
  const canSchedule = returnType && returnReason && fieldNotes?.length > 0;

  useEffect(() => {
    const updatedEvent = { ...event, includeAgents: event.oids };
    setEvent(updatedEvent);
  }, []);

  useEffect(() => {
    if (returnType) handleFindSlotsClick();
    // HandleFindSlotsClick causes this use effect to fire on every re-render
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [returnType, includeAgents]);

  const handleReturnType = async (e) => {
    const { value } = e.target;
    const updatedEvent = { ...event, date, startEndTimes: [], type: value };
    setEvent(updatedEvent);
    setReturnType(value);
  };

  const handleChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col side={2}>
            <HeaderTitle>Return HEA:</HeaderTitle>
            <HeaderLabel>{address ? `${displayAddress}` : 'No Address Available'}</HeaderLabel>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody noFooter>
        <Row>
          <Col>
            <FormRadioButtons
              name="type"
              title="Duration"
              required
              options={typeOptions}
              value={returnType}
              onChange={handleReturnType}
            />
            {returnType && (
              <FormSelect
                title="Reason For Return"
                name="returnReason"
                required
                value={returnReason}
                options={returnVisitReasons}
                onChange={handleChange}
              />
            )}
            <FormTextBox
              title="Notes"
              placeholder="Enter Notes"
              name="notes.fieldNotes"
              onChange={handleChange}
              value={fieldNotes || ''} // '`value` prop on `textarea` should not be null. Consider using an empty string to clear the component'
              required
            />
            <FormMultiselect
              title="Include HES(s)"
              name="includeAgents"
              value={includeAgents}
              options={agents}
              onChange={handleChange}
            />
            {canSchedule && (
              <>
                <AvailableSlots singleAgent />
                <BookSlotsButton handleBookSlots={() => handleReturnClick()} />
              </>
            )}
          </Col>
        </Row>
      </EventSidebarBody>
    </SidebarForm>
  );
};

HEAReturnVisitForm.propTypes = {
  handleReturnClick: PropTypes.func.isRequired,
  handleFindSlotsClick: PropTypes.func.isRequired,
};

export default HEAReturnVisitForm;
