import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

import { Tachometer as DashboardIcon } from '@styled-icons/boxicons-regular/Tachometer';

import { Header, RefreshButton } from '@components/global';

import { UtilityManager, UsersManager } from '@utils/APIManager';
import { getUserCookie } from '@utils/AuthUtils';

import Dashboard from '@components/Dashboard/Dashboard';

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  // This lets the border radius show over the rest of the cells.overflow
  // Might need to take this out for the tooltip.
  overflow: hidden;
  background-color: ${({ theme }) => theme.secondary[100]};
`;

const DashboardTopContainer = styled.div`
  flex: 0 1 auto;
  background-color: #f3f3f3;
  border-bottom: 2px solid #0070d2;
`;

const CalendarRowsContainer = styled.div`
  display: grid;
  ${({ theme }) => theme.screenSize.down(theme.breakpoints.mobileL)} {
    display: block;
  }
  flex: 1 1 auto;
  overflow: auto;
  padding: 10px;
`;

const HeaderStaticContainer = styled.div`
  display: flex;
  flex-direction: row;
  width: 100%;
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  flex-direction: row-reverse;
  width: 100%;
`;

const HeaderTextContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const DashboardText = styled.div`
  color: ${({ theme }) => theme.primary[400]};
  font-size: 13px;
`;
const DashboardTypeText = styled.div`
  color: ${({ theme }) => theme.primary[400]};
  font-size: 28px;
`;

const DashboardIconStyle = styled(DashboardIcon)`
  height: 52px;
  margin-right: 5px;
  margin-bottom: 2px;
  color: white;
  background-color: #707172;
  padding: 4px;
  border-radius: 2px;
`;

const DashboardHeader = styled.div`
  display: flex;
  flex-direction: row;
  margin: 12px 12px;
  font-size: 24px;
  font-weight: 500;
  padding: 8px;
  color: #032d60;
  @media (max-width: 450px) {
    font-size: 20px;
    padding-bottom: 10px;
    align-self: flex-start;
  }
`;

const TimeoutComponent = styled.div`
  text-align: center;
`;

const DashboardPage = () => {
  const [userDetails, setUserDetails] = useState({});
  const [recordDetails, setRecordDetails] = useState(null);

  useEffect(() => {
    const getDashboardDetails = async () => {
      const { sfId, regionAbbreviation } = await getUserDetails();
      if (sfId && regionAbbreviation) await getHESDashboardDetails(sfId, regionAbbreviation);
    };
    getDashboardDetails();
  }, []);

  const getHESDashboardDetails = async (sfId, regionAbbreviation, refresh = false) => {
    const records = await UtilityManager.getHESDashboardDetails(sfId, regionAbbreviation, refresh);
    setRecordDetails(records);
  };

  const getUserDetails = async () => {
    const { oid } = getUserCookie();
    const { regionAbbreviation, regionName, sfId } = await UsersManager.getUserInfo(oid);
    if (regionAbbreviation !== null && regionName !== null) {
      setUserDetails({ regionAbbreviation, regionName, sfId });
      return { sfId, regionAbbreviation };
    }
    return {};
  };

  return (
    <>
      <DashboardContainer>
        <DashboardTopContainer>
          <DashboardHeader>
            <HeaderStaticContainer>
              <DashboardIconStyle />
              <HeaderTextContainer>
                <DashboardText>Dashboard</DashboardText>
                <DashboardTypeText>
                  HES Leader Board
                  {`${userDetails?.regionName ? `- ${userDetails.regionName}` : ''}`}
                </DashboardTypeText>
              </HeaderTextContainer>
            </HeaderStaticContainer>
            <ActionButtonsContainer>
              {userDetails?.sfId && (
                <RefreshButton
                  onClick={() =>
                    getHESDashboardDetails(userDetails.sfId, userDetails.regionAbbreviation, true)
                  }
                />
              )}
            </ActionButtonsContainer>
          </DashboardHeader>
        </DashboardTopContainer>
        <CalendarRowsContainer>
          {recordDetails ? (
            <Dashboard details={recordDetails} region={userDetails.regionAbbreviation} />
          ) : (
            <TimeoutComponent>
              <Header h2>Running your report can take a while.</Header>
              <Header h4>
                This process can take several minutes. If the report runs for too long, it can
                timeout.
              </Header>
            </TimeoutComponent>
          )}
        </CalendarRowsContainer>
      </DashboardContainer>
    </>
  );
};

export default DashboardPage;
