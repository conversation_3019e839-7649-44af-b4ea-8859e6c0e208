import { formValuesState } from '@recoil/dataIntakeForm';
import { useRecoilState } from 'recoil';
import { capeLightZips, eplusElectricZips, eplusGasZips } from '../consts';

export const useLeadVendor = () => {
  const [formValues, setFormValues] = useRecoilState(formValuesState);

  const setLeadVendorAndApprovalSoftware = () => {
    const {
      electricProvider,
      discountedRateCode,
      heatingFuel,
      gasProvider,
      numUnitsSchedulingToday,
      customerAddress,
    } = formValues;

    let approvalSoftware = 'Hancock';
    let leadVendor = 'Abode';
    const isCap = discountedRateCode === 'Yes';

    for (let iterator = 0; iterator < Number(numUnitsSchedulingToday); iterator++) {
      const isElectricProviderColumbia = electricProvider?.[iterator] === 'Columbia Gas';
      const isElectricProviderEversource = electricProvider?.[iterator] === 'Eversource';
      const isHeatingFuelGas = heatingFuel?.[iterator] === 'Gas';
      const isGasProviderBerkshire = gasProvider?.[iterator] === 'Berkshire';
      const isGasProviderColumbia = gasProvider?.[iterator] === 'Columbia Gas';
      const isGasProviderEverSource = gasProvider?.[iterator] === 'Eversource';

      if (capeLightZips.includes(customerAddress.postalCode)) {
        // All gas customers on the Cape are now Abode customers
        // Only rise on cape if not gas
        if (!isHeatingFuelGas) {
          leadVendor = 'RISE on Cape';
          approvalSoftware = 'Eplus';
        }
      } else if (isHeatingFuelGas && isGasProviderBerkshire) {
        leadVendor = 'Berkshire - IIC';
        approvalSoftware = 'Smartsheet';
      } else if (
        (isHeatingFuelGas && isGasProviderColumbia) ||
        (!isHeatingFuelGas && isElectricProviderColumbia)
      ) {
        approvalSoftware = 'Eplus';
        leadVendor = 'RISE';
      } else if (
        (isHeatingFuelGas && isGasProviderEverSource) ||
        (!isHeatingFuelGas && isElectricProviderEversource)
      ) {
        leadVendor = 'CleaResult';
      }
    }

    if (
      eplusElectricZips.includes(customerAddress.postalCode) &&
      heatingFuel[0] !== 'Gas' &&
      electricProvider.includes('Eversource')
    ) {
      approvalSoftware = 'Eplus';
    }

    if (
      eplusGasZips.includes(customerAddress.postalCode) &&
      heatingFuel[0] === 'Gas' &&
      ['Eversource', 'Columbia Gas'].some((providerOption) => gasProvider.includes(providerOption))
    ) {
      approvalSoftware = 'Eplus';
    }

    if (isCap) {
      leadVendor = 'CAP';
      approvalSoftware = 'Monday.com';
    }

    setFormValues({
      approvalSoftware,
      leadVendor,
    });

    return { ...formValues, approvalSoftware, leadVendor };
  };

  return { setLeadVendorAndApprovalSoftware };
};
