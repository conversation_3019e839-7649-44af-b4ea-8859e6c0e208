import React from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { decodeEventType } from '@homeworksenergy/utility-service';
import { isAuthorized } from '@utils/AuthUtils';

const StyledText = styled.span`
  padding: 0.3em;
  font-weight: ${({ important }) => (important ? 'bold' : '')};
  @media (max-width: 1149px) {
    padding: 3.2px;
  }
`;

const EventListBody = ({ event }) => {
  const {
    type = '',
    startTime = '',
    endTime = '',
    customerName = '',
    notes = {
      fieldNotes: '',
      summaryNotes: '',
    },
    address = {
      street: '',
      city: '',
      state: '',
      postalCode: '',
    },
    eventName = '',
  } = event;
  const { businessEvent, business } = decodeEventType(type);

  const renderNotes = () => {
    let showNotes = false;
    let notesKey = '';
    if (business === 'Insulation') {
      showNotes = true;
      notesKey = 'summaryNotes';
    } else {
      // fieldNotes used for other event types
      const isHeaAgent = isAuthorized('Agent', 'HEA');
      if (notes?.fieldNotes?.length > 0) {
        showNotes = true;
        notesKey = 'fieldNotes';
      }
      // officeNotes used for custom block
      if (isHeaAgent && notes?.officeNotes?.length > 0) {
        showNotes = true;
        notesKey = 'officeNotes';
      }
    }
    return showNotes ? <StyledText>{notes[notesKey]}</StyledText> : null;
  };
  return (
    <>
      <StyledText important>
        {businessEvent === 'Custom Block' ? eventName : businessEvent}
      </StyledText>
      <StyledText>
        {moment(startTime, 'H:mm').format('h:mm A')} - {moment(endTime, 'H:mm').format('h:mm A')}
      </StyledText>
      {address && (
        <StyledText important>
          {address.street}, {address.city}, {address.state} {address.postalCode}
        </StyledText>
      )}
      <StyledText>{customerName}</StyledText>
      {renderNotes()}
    </>
  );
};

EventListBody.propTypes = {
  event: PropTypes.shape({
    type: PropTypes.string,
    startTime: PropTypes.string,
    endTime: PropTypes.string,
    eventName: PropTypes.string,
    customerName: PropTypes.string,
    address: PropTypes.shape({
      street: PropTypes.string,
      city: PropTypes.string,
      state: PropTypes.string,
      postalCode: PropTypes.string,
    }),
    notes: {
      fieldNotes: PropTypes.string,
      summaryNotes: PropTypes.string,
    },
  }),
};

export default EventListBody;
