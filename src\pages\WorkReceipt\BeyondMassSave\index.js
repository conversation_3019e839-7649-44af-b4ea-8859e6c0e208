import React, { memo } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { FormSelect, FormInput } from '@components/global/Form';
import { useRecoilState, useRecoilValue } from 'recoil';
import { formValuesState, activeFormState } from '@recoil/dataIntakeForm';
import { bmsOptions } from '../consts';

const BmsContianer = styled.div`
  width: 900px;
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 20px;
`;
const FormSelectContainer = styled.div`
  width: 300px;
`;
const BmsBox = styled.div`
  width: 300px;
`;

const BMSCustomItems = memo(function BMSCustomItems({
  item,
  formValues,
  handleFieldChange = () => {},
}) {
  return (
    <>
      <FormInput
        name={`bms${item}Item`}
        value={formValues[`bms${item}Item`]}
        onChange={handleFieldChange}
      />
      <BmsBox>/per unit</BmsBox>
      <FormInput
        type="number"
        title="Price"
        name={`bms${item}UnitPrice`}
        value={formValues[`bms${item}UnitPrice`]}
        onChange={handleFieldChange}
      />
      <FormInput
        type="number"
        name={`bms${item}Qty`}
        title="Quantity"
        value={formValues[`bms${item}Qty`]}
        onChange={handleFieldChange}
      />
      <FormInput
        type="number"
        readOnly
        title="Total"
        name={`bms${item}Total`}
        value={(formValues[`bms${item}Qty`] * formValues[`bms${item}UnitPrice`]).toFixed(2)}
      />
    </>
  );
});

export const BeyondMassSave = ({ handleFieldChange }) => {
  const activeForm = useRecoilValue(activeFormState);
  const [formValuesList, setFormValues] = useRecoilState(formValuesState);
  const formValues = formValuesList[activeForm];
  const rangeList = Array(11)
    .fill()
    .map((_, index) => 1 + index);

  const handleBmsItemChange = (e) => {
    const unit = `${e.target.name.slice(0, 4)}UnitPrice`;
    setFormValues({
      [e.target.name]: e.target.value,
      [unit]: bmsOptions[e.target.value],
      index: activeForm,
    });
  };

  const handleQuantityChange = (e) => {
    setFormValues({ [e.target.name]: Number(e.target.value), index: activeForm });
  };
  return (
    <>
      {rangeList?.map((item, iterator) => {
        let shouldRenderNext = true;
        if (iterator > 0) {
          const prevItem = rangeList[iterator - 1];
          if (!formValues[`bms${prevItem}Item`]) {
            shouldRenderNext = false;
          }
        }
        if (shouldRenderNext) {
          const isCutomBMS =
            formValues[`bms${item}Item`] && formValues[`bms${item}Item`].includes('Custom Item');
          return (
            <BmsContianer key={`bmbsForm${item}`}>
              Beyond Item+Quantity {item}
              {isCutomBMS ? (
                <BMSCustomItems
                  item={item}
                  formValues={formValues}
                  handleFieldChange={handleFieldChange}
                />
              ) : (
                <>
                  <FormSelectContainer>
                    <FormSelect
                      name={`bms${item}Item`}
                      value={formValues[`bms${item}Item`]}
                      onChange={handleBmsItemChange}
                      options={Object.keys(bmsOptions)}
                    />
                  </FormSelectContainer>
                  <BmsBox>
                    Price:
                    <br />
                    {bmsOptions[formValues[`bms${item}Item`]]}
                  </BmsBox>
                  <BmsBox>/per unit</BmsBox>
                  <BmsBox>
                    <FormInput
                      type="number"
                      title="Quantity"
                      name={`bms${item}Qty`}
                      value={formValues[`bms${item}Qty`]}
                      onChange={handleQuantityChange}
                    />
                  </BmsBox>
                  <BmsBox>
                    <FormInput
                      title="Total"
                      name={`bms${item}Total`}
                      readOnly
                      value={(
                        formValues[`bms${item}Qty`] * bmsOptions[formValues[`bms${item}Item`]] || 0
                      ).toFixed(2)}
                    />
                  </BmsBox>
                </>
              )}
            </BmsContianer>
          );
        }
        return null;
      })}
    </>
  );
};

BMSCustomItems.propTypes = {
  item: PropTypes.string.isRequired,
  handleFieldChange: PropTypes.func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  formValues: PropTypes.objectOf(PropTypes.any).isRequired,
};

BeyondMassSave.propTypes = {
  handleFieldChange: PropTypes.func,
};
