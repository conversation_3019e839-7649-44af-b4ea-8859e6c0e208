import axios from 'axios';
import { startLoading, stopLoading, throwError, displaySuccessMessage } from '@utils/EventEmitter';

const getPatterns = async (params) => {
  const url = '/api/eventPatterns/getPatterns';
  startLoading('Getting Patterns...');
  const response = await axios.get(url, { params });
  stopLoading();
  if (response.error) return throwError(response.error);
  return response;
};

const createPattern = async (params) => {
  const url = '/api/eventPatterns/createPattern';
  startLoading('Creating Pattern...');
  const response = await axios.post(url, params);
  stopLoading();
  if (response.error) return throwError(response.error);
  return displaySuccessMessage('Successfully created new pattern');
};

const updatePattern = async (params) => {
  const url = '/api/eventPatterns/updatePattern';
  startLoading('Updating Pattern...');
  const response = await axios.post(url, params);
  stopLoading();
  if (response.error) return throwError(response.error);
  return displaySuccessMessage('Successfully updated pattern');
};

const deletePattern = async (params) => {
  const url = '/api/eventPatterns/deletePattern';
  startLoading('Deleting Pattern...');
  const response = await axios.post(url, params);
  stopLoading();
  if (response.error) return throwError(response.error);
  return displaySuccessMessage('Successfully deleted pattern');
};

export { getPatterns, createPattern, updatePattern, deletePattern };
