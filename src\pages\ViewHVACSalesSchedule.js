import React from 'react';
import { InstallationScheduledCalendar } from '@components/global';
import { isAuthorized } from '@utils/AuthUtils';

const ViewHVACSalesSchedulePage = () => {
  const isHVACScheduler = isAuthorized('Scheduler', 'HVAC-Sales');

  return (
    <InstallationScheduledCalendar
      title="HVAC Sales Schedule"
      type="000100"
      includeRegionHeaders
      noButtons={!isHVACScheduler}
      showAllUsersEvents={isHVACScheduler}
    />
  );
};

export default ViewHVACSalesSchedulePage;
