import { handleApiCall } from './utils/AxiosConfig';

const updateMaxMonthlyCAPVisits = async (maxMonthlyCAPVisits) => {
  const url = '/api/settings/updateApplicationVariable';
  const params = {
    variableName: 'max_monthly_cap_visits',
    type: '0000',
    value: maxMonthlyCAPVisits,
  };
  return handleApiCall({
    url,
    method: 'put',
    params,
    loadingMessage: 'Updating max monthly CAP visits...',
    successMessage: 'Successfully updated max monthly CAP visits',
  });
};

const getMaxMonthlyCAPVisits = async () => {
  const url = '/api/settings/getApplicationVariable';
  const response = await handleApiCall({
    url,
    params: { variableName: 'max_monthly_cap_visits' },
    method: 'get',
  });

  return parseInt(response, 10);
};

const getGoogleMapsApiKey = async () => {
  const url = '/api/settings/getGoogleMapsApiKey';
  const response = await handleApiCall({
    url,
    method: 'get',
  });

  return response?.apiKey;
};

export default { updateMaxMonthlyCAPVisits, getMaxMonthlyCAPVisits, getGoogleMapsApiKey };
