import React from 'react';
import PropTypes from 'prop-types';

import { ScrollableList } from '@components/global/ScreenPartitionView';
import UserListCard from './UserListCard';

// TODO: this component shouldn't be necessary. Refactor any logic to scrollable list
const UserList = ({ users }) => {
  return (
    <ScrollableList>
      {users.map((user) => {
        return <UserListCard key={user.oid} user={user} />;
      })}
    </ScrollableList>
  );
};

UserList.propTypes = {
  users: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
};

export default UserList;
