import styled from 'styled-components';

const List = styled.ul`
  padding-inline-start: ${({ paddingInlineStart }) => paddingInlineStart || 'initial'};
`;

const ListItem = styled.li`
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  font-weight: 600;
`;

const ScriptHeader = styled.h4`
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 0;
`;

const ScriptTitle = styled.h2`
  font-size: 22px;
  font-weight: 600;
`;

const ScriptText = styled.span`
  font-size: 16px;
  font-weight: ${({ bold }) => (bold ? 600 : 400)};
  display: ${({ block }) => (block ? 'block' : 'inline')};
`;

export { List, ListItem, ScriptHeader, ScriptTitle, ScriptText };
