import React, { useState, useEffect } from 'react';
import { useRecoilValue } from 'recoil';

import utilityService from '@homeworksenergy/utility-service';
import { selectedPartnerEventState } from '@recoil/eventSidebar';
import { openFile, fetchDocumentsFromS3, downloadFile, handleUpload } from './DocRepoButtons';
import DocRepoBody from './DocRepoBody';

const PartnersDocRepo = () => {
  const [documents, setDocuments] = useState([]);
  const [s3Params, setS3Params] = useState([]);
  const [docsSelected, setDocsSelected] = useState(false);
  const [allDocsSelected, setAllDocsSelected] = useState(false);
  const selectedPartnerEvent = useRecoilValue(selectedPartnerEventState);

  const {
    type,
    sfIds: { dealId, barrierId },
  } = selectedPartnerEvent;

  useEffect(() => {
    if (barrierId) {
      const { business } = utilityService?.decodeEventType(type);
      const s3Params = {
        state: 'MA',
        department: business,
        uniqueId: barrierId,
        dealId,
      };
      setS3Params(s3Params);
      fetchDocumentsFromS3(s3Params, setDocuments);
    }
  }, [barrierId]);

  if (!type) return null;

  const handleUploadAllButton = (event) => {
    handleUpload(event, s3Params, setDocuments);
  };

  const handleDownloadAllButton = () => {
    const selectedDocs = documents.filter(({ checked }) => {
      return checked;
    });
    selectedDocs.forEach(({ fileName }) => {
      downloadFile({ ...s3Params, docName: fileName });
    });
  };

  const handleViewAllButton = () => {
    const selectedDocs = documents.filter(({ checked }) => {
      return checked;
    });
    selectedDocs.forEach((document) => {
      viewDocument(document);
    });
  };

  const viewDocument = ({ fileName, type }) => {
    let obj = { ...s3Params, docName: fileName };
    if (['PWB', 'PICS', 'MOLD'].includes(fileName))
      obj = { ...obj, docName: `${fileName}.${type}`, uniqueId: dealId };
    openFile(obj);
  };

  const handleDocsSelection = (e) => {
    const { name, value } = e.target;
    const formattedDocuments = documents.map((document) => {
      const doc = { ...document };
      if (document.fileName === name) doc.checked = value;
      return doc;
    });
    const isDocSelected = formattedDocuments.filter(({ checked }) => {
      return checked;
    });
    if (isDocSelected.length > 0) setDocsSelected(true);
    else setDocsSelected(false);
    setDocuments(formattedDocuments);
  };

  const handleSelectAll = (e) => {
    const { value } = e.target;
    const formattedDocuments = documents.map((document) => {
      const doc = { ...document };
      if (doc.uploaded) doc.checked = value;
      return doc;
    });
    setDocsSelected(value);
    setAllDocsSelected(value);
    setDocuments(formattedDocuments);
  };

  return (
    <DocRepoBody
      documents={documents}
      s3Params={s3Params}
      handleDocsSelection={handleDocsSelection}
      viewDocument={viewDocument}
      handleUploadAllButton={handleUploadAllButton}
      handleDownloadAllButton={handleDownloadAllButton}
      handleViewAllButton={handleViewAllButton}
      setDocuments={setDocuments}
      docsSelected={docsSelected}
      allDocsSelected={allDocsSelected}
      handleSelectAll={handleSelectAll}
    />
  );
};

export default PartnersDocRepo;
