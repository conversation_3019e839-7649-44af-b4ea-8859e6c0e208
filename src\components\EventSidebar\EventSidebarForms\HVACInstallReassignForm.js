import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useRecoilValue } from 'recoil';
import { SlotsManager } from '@utils/APIManager';

import { PrimaryButton } from '@components/global/Buttons';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarHeader, { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import { Row, Col, FormSelect } from '@components/global/Form';
import { selectedEventState } from '@recoil/eventSidebar';

const HVACInstallReassignForm = ({ handleReassignClick }) => {
  const [availableUsers, setAvailableUsers] = useState([]);
  const [selectedOid, setSelectedOid] = useState('');

  const event = useRecoilValue(selectedEventState);

  useEffect(() => {
    getSlots();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getSlots]);

  const getSlots = useCallback(async () => {
    const { type, date, startTime, endTime } = event;
    const params = { type, date, startTime, endTime };
    const openingsResponse = await SlotsManager.getAvailableUsersForReassign(params);
    if (openingsResponse.error) return;
    const { users } = openingsResponse;

    const userOptions = users.map(({ oid, displayName }) => {
      return { key: displayName, value: oid };
    });

    setAvailableUsers(userOptions);
  }, [event]);

  const handleSubmit = async () => {
    const { id, associatedEventIds, startEndTimes, oldAgentOid } = event;
    const postObject = { id, associatedEventIds, oid: selectedOid, startEndTimes, oldAgentOid };

    return handleReassignClick(postObject);
  };

  const handleUserChange = (event) => {
    const selectedUser = event.target.value;
    setSelectedOid(selectedUser);
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col side={2}>
            <HeaderLabel>Reassign HVAC Install:</HeaderLabel>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            <FormSelect
              title="available users"
              name="availableUsers"
              value={selectedOid}
              options={availableUsers}
              onChange={handleUserChange}
              disablePlaceholder={false}
            />
          </Col>
        </Row>
      </EventSidebarBody>
      <EventSidebarFooter>
        {selectedOid && (
          <PrimaryButton right onClick={() => handleSubmit()}>
            Reassign
          </PrimaryButton>
        )}
      </EventSidebarFooter>
    </SidebarForm>
  );
};

HVACInstallReassignForm.propTypes = {
  handleReassignClick: PropTypes.func.isRequired,
};

export default HVACInstallReassignForm;
