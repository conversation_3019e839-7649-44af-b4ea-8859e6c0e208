import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput, FormTextBox } from '@components/global/Form';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const CAPHVACReconcileReport = ({ record = {} }) => {
  const { dealId, lastActivity, heaHvacNotes, hvacInterest, siteid, isCap } = record;

  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" isCap={isCap} />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput readOnly name="siteid" value={siteid} title="Site ID" placeholder="" />
          <FormInput
            readOnly
            name="lastActivity"
            value={lastActivity}
            title="Last Activity"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="hvacInterest"
            value={hvacInterest}
            title="HVAC Interest"
            placeholder=""
          />
        </Col>
      </Row>
      <FormTextBox
        readOnly
        name="heaHvacNotes"
        value={heaHvacNotes}
        title="HEA HVAC Notes"
        placeholder=""
      />
    </>
  );
};

CAPHVACReconcileReport.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    lastActivity: PropTypes.string,
    hvacInterest: PropTypes.string,
    heaHvacNotes: PropTypes.string,
    siteid: PropTypes.string,
    isCap: PropTypes.string,
  }),
};

export default CAPHVACReconcileReport;
