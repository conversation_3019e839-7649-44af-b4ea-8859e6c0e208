import React from 'react';
import { useRecoilValue, useResetRecoilState } from 'recoil';

import { selectedPatternState } from '@recoil/eventPatterns';
import { authorizedDepartmentsSelector } from '@recoil/app';

import { PageHeader, PageContainer } from '@pages/Components';
import { PrimaryButton } from '@components/global/Buttons';
import ScreenPartitionView from '@components/global/ScreenPartitionView/ScreenPartitionView';
import { Filters } from '@components/global/Filters';

import EventPatternsForm from './EventPatternsForm';
import EventPatternsList from './EventPatternsList';

const EventPatternsPage = () => {
  const resetSelectedPattern = useResetRecoilState(selectedPatternState);
  const departments = useRecoilValue(authorizedDepartmentsSelector);

  const filterOptions = [{ displayName: 'Department', name: 'departmentId', options: departments }];

  return (
    <PageContainer>
      <PageHeader
        buttons={[
          <PrimaryButton key="create-new-pattern" onClick={() => resetSelectedPattern()}>
            Create New Pattern
          </PrimaryButton>,
        ]}
        filters={<Filters filters={filterOptions} />}
      >
        Event Patterns
      </PageHeader>

      <ScreenPartitionView ratio={[3, 7]}>
        <EventPatternsList />
        <EventPatternsForm />
      </ScreenPartitionView>
    </PageContainer>
  );
};

export default EventPatternsPage;
