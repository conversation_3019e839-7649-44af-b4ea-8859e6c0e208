import moment from 'moment';

import { SlotsManager } from '@utils/APIManager';
import { programOptions } from '@utils/constants';

export const useGetNextAvailableSlots = () => {
  const getNextAvailableSlots = async (formValues, state = 'MA') => {
    const {
      type,
      heaOrHvac,
      electricProvider,
      discountedRateCode,
      numUnitsSchedulingToday,
      customerAddress,
      preferredLanguage,
      leadVendor,
      leadSource,
      approvalSoftware,
      includeAgents,
      incomeEligibleOrMarketRate,
      eventDuration,
      jobLength,
    } = formValues;

    const currentDate = moment();
    const formattedDate = currentDate.format('YYYY-MM-DD');

    const program = programOptions.find((option) => {
      return option?.name === electricProvider[0];
    });

    const isCT = state === 'CT';
    const isHEA = heaOrHvac === 'HEA';
    const isHVAC = heaOrHvac === 'HVAC';
    const isCap = discountedRateCode === 'Yes' && isHEA;
    let numDays = null;

    switch (true) {
      case isCT:
        numDays = 260;
        break;
      case isCap:
      case isHVAC:
        numDays = 120;
        break;
      default:
        numDays = 60;
    }

    const getSlotsValues = {
      type,
      startDate: formattedDate,
      numUnit: Number(numUnitsSchedulingToday),
      address: {
        ...customerAddress,
      },
      regions: [],
      languagePref: preferredLanguage,
      findPast: false,
      virtual: false,
      jobLength,
      eventDuration,
      numDays,
      program: program?.value,
      leadVendor,
      leadSource,
      approvalSoftware,
      includeAgents,
      incomeEligibleOrMarketRate,
      // TODO: should this support attributes?
    };
    if (isHVAC) {
      delete getSlotsValues.approvalSoftware;
      delete getSlotsValues.program;
      // Not sure if this is necessary or not, adding just in case. we should really avoid doing this kind of "delete" stuff
      delete getSlotsValues.incomeEligibleOrMarketRate;
    }
    const slots = await SlotsManager.getNextAvailableSlots(getSlotsValues);

    return slots;
  };
  return { getNextAvailableSlots };
};
