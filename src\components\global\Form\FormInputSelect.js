import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { handleFormFieldChange, FormInput, FormSelect } from './index';

const InputContainer = styled.div`
  display: flex;
  align-items: center;
`;

const StyledFormInput = styled(FormInput)`
  border-right: none;
`;

const FormFieldWarning = styled.div`
  color: ${({ theme }) => theme.colors.red};
  font-style: italic;
  font-size: 14px;
`;

const FormInputSelect = ({
  title,
  options,
  selectFieldName,
  inputFieldName,
  selectFieldValue = '',
  inputFieldValue = '',
  state = {},
  setState = () => {},
  readOnly = false,
  errorMessage = null,
  customAmountMax = null,
  showCustomValue = true,
}) => {
  const [showInputField, setShowInputField] = useState(false);
  const [warningMessage, setWarningMessage] = useState(false);

  const customKeyValue = 'customValue';
  const formattedOptions = !readOnly
    ? options
    : [{ key: `$${selectFieldValue}.00`, value: selectFieldValue }];
  const selectOptions = showCustomValue
    ? [...formattedOptions, { key: 'Enter Custom Value', value: customKeyValue }]
    : [...formattedOptions];

  useEffect(() => {
    const selectFieldValueIsBlank = selectFieldValue === '';
    if (selectFieldValueIsBlank || selectFieldValue !== customKeyValue) setShowInputField(false);
  }, [selectFieldValue, inputFieldValue]);

  const handleFieldChange = (e) => {
    setWarningMessage(null);
    if (e.target.name === selectFieldName) {
      setShowInputField(e.target.value === customKeyValue);
    }
    if (customAmountMax && e.target.value > 150) {
      setWarningMessage(errorMessage);
      return;
    }
    handleFormFieldChange(e, state, setState);
  };

  return (
    <>
      <FormSelect
        required
        title={title}
        placeholder={`Select ${title}`}
        name={selectFieldName}
        value={selectFieldValue}
        onChange={handleFieldChange}
        options={selectOptions}
        readOnly={readOnly}
      />
      {!readOnly && showInputField && (
        <InputContainer>
          <StyledFormInput
            title=""
            placeholder={`Enter ${title}`}
            type="number"
            name={inputFieldName}
            value={inputFieldValue}
            onChange={handleFieldChange}
            max={customAmountMax}
          />
        </InputContainer>
      )}
      {warningMessage && <FormFieldWarning>{warningMessage}</FormFieldWarning>}
    </>
  );
};

FormInputSelect.propTypes = {
  title: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({ key: PropTypes.string, value: PropTypes.string }),
    ]),
  ).isRequired,
  selectFieldName: PropTypes.string.isRequired,
  inputFieldName: PropTypes.string.isRequired,
  selectFieldValue: PropTypes.string,
  inputFieldValue: PropTypes.string,
  state: PropTypes.shape({}),
  setState: PropTypes.func,
  readOnly: PropTypes.bool,
  errorMessage: PropTypes.string,
  customAmountMax: PropTypes.number,
  showCustomValue: PropTypes.bool,
};

export default FormInputSelect;
