import { startLoading, stopLoading, throwError } from '@utils/EventEmitter';
import axios from './utils/AxiosConfig';

// Upload required document to S3 but renames the file.
const uploadRequiredFile = async (file, urlParams, newName) => {
  const { state, department, uniqueId } = urlParams;
  const url = `/api/docRepo/uploadRequiredFile/${state}/${department}/${uniqueId}/${newName}`;
  startLoading('Uploading File ...');
  const response = await axios.post(url, file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  stopLoading();
  const { error } = response.data;
  if (error) {
    throwError({
      message: `Failed to upload file. Error: ${error}`,
      params: `Params passed: ${urlParams}`,
    });
    return false;
  }
  return true;
};

// Upload file to S3
const uploadFileToS3 = async (file, urlParams) => {
  const { state, department, uniqueId } = urlParams;
  const url = `/api/docRepo/uploadFile/${state}/${department}/${uniqueId}`;
  startLoading('Uploading File To S3...');
  const response = await axios.post(url, file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  const { error } = response.data;
  if (error) {
    throwError({
      message: `Failed to upload file. Error: ${error}`,
      params: `Params passed: ${urlParams}`,
    });
    return false;
  }
  stopLoading();
  return true;
};

// Get all documents from S3
const getDocsFromS3 = async (s3ParamsObj) => {
  const { bucketKeys, department } = s3ParamsObj;
  startLoading('Getting Documents...');
  const response = await axios.get('/api/docRepo/getDocs', {
    params: {
      bucketKeys,
      department,
    },
  });
  stopLoading();
  const { error } = response.data;
  if (error)
    return throwError({
      message: `Failed to get document. Error: ${error}`,
      params: `Params passed: ${s3ParamsObj}`,
    });
  return response.data;
};

// Downloads file fromS3.
// When first uploading file to S3, I store them as the file's type instead of a Octeat Stream.
// When a file is retrieved from S3, it comes back as an array buffer of bytes, had to convert that buffer into a Uint8Array
// Then we put the whole array of bytes into a blob, attach the blob to an html element, then click it then remove it.
const downloadFileFromS3 = async (params) => {
  startLoading('Downloading File...');
  const response = await axios.get('/api/docRepo/downloadFile', { params });
  const { error } = response.data;
  if (error) return throwError(error);

  const byte = new Uint8Array(response.data.Body.data);
  const file = new Blob([byte], { type: response.data.ContentType });
  const fileUrl = window.URL.createObjectURL(file);
  const link = document.createElement('A');
  link.href = fileUrl;
  link.download = params.doc;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  stopLoading();
  return response;
};

// Delete file from S3
// We don't delete files. We rename deleted_timeStamp_fileName and put the file back into S3
const deleteFileFromS3 = async (params) => {
  startLoading('Deleting File...');
  const response = await axios.get('/api/docRepo/deleteFile', { params });
  const { error } = response.data;
  if (response.error) {
    throwError({
      message: `Failed to delete file. Error: ${error}`,
      params: `Params passed: ${params}`,
    });
    return false;
  }
  stopLoading();
  return true;
};

// Get Doc Pre Signed Url
const getSignedUrl = async (urlParams) => {
  const { state, department, uniqueId, docName } = urlParams;
  const url = `/api/docRepo/getSignedUrl/${state}/${department}/${uniqueId}/${encodeURIComponent(
    docName,
  )}`;
  const response = await axios.get(url);
  const { message, signedUrl } = response.data;
  if (message) {
    throwError({
      message: `Failed to get Url. Error: ${message}`,
      params: `Params passed: ${urlParams}`,
    });
    return false;
  }
  return signedUrl;
};

// print Event docs
const printEventDocs = async (department, id) => {
  const url = `/api/docRepo/printDocs/${department}/${id}`;
  startLoading('Getting Docs');
  const response = await axios.post(url);
  const { error, bufferPdf, isPdfBlank } = response.data;
  if (error) {
    throwError({
      message: `Failed to get pdf Documents. Error: ${error}`,
      params: `Params passed: ${{ department, id }}`,
    });
  }
  if (isPdfBlank) {
    throwError({
      message: 'There are no documents for this customer.',
    });
  }
  stopLoading();
  return isPdfBlank || error ? false : Object.values(bufferPdf);
};

const updateDocRepoResultingQuestions = async (params) => {
  const { state, department, uniqueId, updateResulting } = params;
  const url = `/api/docRepo/updateDocRepoResultingQuestions/${state}/${department}/${uniqueId}`;
  startLoading('Updating Docs');
  const response = await axios.post(url, updateResulting);
  stopLoading();
  const { error } = response.data;
  if (error) {
    throwError({
      message: `Failed to update Resulting Error: ${error.message}`,
    });
    return false;
  }
  return response;
};

const mergeDocuments = async (params) => {
  const { state, department, uniqueId, ...otherParams } = params;
  const url = `/api/docRepo/mergeDocuments/${state}/${department}/${uniqueId}`;
  startLoading('Merging Docs');
  const response = await axios.post(url, otherParams);
  stopLoading();
  const { error } = response.data;
  if (error) {
    throwError({
      message: `Failed to merge Documents.\nError: ${error.message}`,
    });
    return false;
  }
  return response;
};

export default {
  uploadFileToS3,
  uploadRequiredFile,
  getDocsFromS3,
  downloadFileFromS3,
  deleteFileFromS3,
  getSignedUrl,
  printEventDocs,
  updateDocRepoResultingQuestions,
  mergeDocuments,
};
