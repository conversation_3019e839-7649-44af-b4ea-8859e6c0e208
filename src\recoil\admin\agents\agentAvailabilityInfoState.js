import { atom } from 'recoil';
import moment from 'moment';

const agentAvailabilityInfoState = atom({
  key: 'agentAvailabilityInfoState',
  default: {
    days: [1, 2, 3, 4, 5], // Default to monday through friday
    state: 'MA',
    department: 6,
    oids: [],
    overrideHoliday: false,
    region: '',
    startDate: moment(),
    endDate: moment().add(1, 'month'),
    startTime: moment()
      .set('hour', 9)
      .set('minute', 0),
    endTime: moment()
      .set('hour', 17)
      .set('minute', 0),
    action: 'open',
    maxAppt: 3,
  },
});

export default agentAvailabilityInfoState;
