import { selector } from 'recoil';

import { UtilityManager } from '@utils/APIManager';

const departmentsSelector = selector({
  key: 'departments',
  get: async () => {
    const allDepartments = await UtilityManager.getAllDepartments();
    return allDepartments.map(({ department, id, eventType }) => {
      return { key: department, name: department, value: id, eventType };
    });
  },
});

export default departmentsSelector;
