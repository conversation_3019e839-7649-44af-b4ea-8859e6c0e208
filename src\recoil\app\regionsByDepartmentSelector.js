import { selectorFamily } from 'recoil';
import { UtilityManager } from '@utils/APIManager';

const regionsByDepartmentSelector = selectorFamily({
  key: 'regionsByDepartment',
  get: (departmentId) => async () => {
    let regions = await UtilityManager.getAllRegions([departmentId]);
    regions = regions.map(({ name, regionId }) => {
      return { key: name, value: regionId };
    });
    return regions;
  },
});

export default regionsByDepartmentSelector;
