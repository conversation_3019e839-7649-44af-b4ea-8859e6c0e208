import React, { memo } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { calculateVentMathRound } from '@utils/functions';
import { TableHeader, TableCell, TableRow, Table } from './TableCellsColors';
import { ventingOptions } from '../consts';

const InputField = styled.input`
  width: 80px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 5px;
`;

const Label = styled.span`
  font-size: 12px;
`;
const VentilationContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
`;

export const HighVenting = memo(function HighVenting({
  handleVentingChange = () => {},
  formValues,
}) {
  const { highVentingTotal, lowVentingTotal, balanced, hiLo } = formValues;
  const calculateCellValue = ({ fieldName, price }) =>
    calculateVentMathRound(formValues[fieldName] * price);
  return (
    <VentilationContainer>
      <Table>
        <thead>
          <TableRow>
            <TableHeader colSpan="4">High Venting</TableHeader>
          </TableRow>
        </thead>
        <tbody>
          {ventingOptions.highVenting.map((option) => (
            <TableRow key={`highVenting-${option.name}-key`}>
              <TableCell>
                <InputField
                  data-price={option.price}
                  data-parent={option.parent}
                  data-ventingname="highVenting"
                  type="number"
                  name={option.fieldName}
                  value={formValues[option.fieldName]}
                  onChange={handleVentingChange}
                />
              </TableCell>
              <TableCell whiteSpace="nowrap" margin={0}>
                {option.name}
              </TableCell>
              <TableCell>{option.price}</TableCell>
              <TableCell>{calculateCellValue(option)}</TableCell>
            </TableRow>
          ))}
          <TableRow>
            <TableCell />
            <TableCell fontWeight={700} textAlign="right">
              total:
            </TableCell>
            <TableCell />
            <TableCell>{highVentingTotal.toFixed(2)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell />
            <TableCell whiteSpace="nowrap" fontSize="12px">
              % (1:300)
            </TableCell>
            <TableCell />
            <TableCell whiteSpace="nowrap" fontSize="12px">
              {Math.round(
                (Number(highVentingTotal) + Number(lowVentingTotal) / Number(balanced)) * 100,
              ) || 0}
              %
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell />
            <TableCell whiteSpace="nowrap" fontSize="12px">
              % (1:150)
            </TableCell>
            <TableCell />
            <TableCell whiteSpace="nowrap" fontSize="12px">
              {Math.round((Number(highVentingTotal) / Number(hiLo)) * 100) || 0}%
            </TableCell>
          </TableRow>
        </tbody>
      </Table>
      <Label>** You may NEVER have ONLY low venting. MUST add High Venting **</Label>
    </VentilationContainer>
  );
});

HighVenting.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  formValues: PropTypes.objectOf(PropTypes.any).isRequired,
  handleVentingChange: PropTypes.func,
};
