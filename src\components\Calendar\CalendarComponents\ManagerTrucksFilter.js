import React from 'react';
import { useRecoilState } from 'recoil';
import styled from 'styled-components';

import { managerTruckFilterAtom } from '@recoil/event';

const ManagerTrucksButton = styled.button`
  background: ${({ $filter, theme }) => {
    return $filter ? theme.colors.eventGreen : theme.secondary[100];
  }};
  border: 1px solid ${({ theme }) => theme.colors.eventGreen};
  border-radius: 4px;
  color: ${({ $filter, theme }) => {
    return $filter ? theme.secondary[100] : theme.colors.eventGreen;
  }};
  margin-right: 10px;
  padding: 1px 20px;
`;

const ManagerTrucksFilter = () => {
  const [managerTrucksFilter, setManagerTrucksFilter] = useRecoilState(managerTruckFilterAtom);

  return (
    <ManagerTrucksButton
      $filter={managerTrucksFilter}
      onClick={() => setManagerTrucksFilter(!managerTrucksFilter)}
    >
      Manager
    </ManagerTrucksButton>
  );
};

export default ManagerTrucksFilter;
