import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import styled, { ThemeProvider } from 'styled-components';
import { useSetRecoilState, useRecoilState } from 'recoil';
import { formMapState, formFieldsState, formRecoilState } from '@recoil/dataIntakeForm';
import { activeTabIndexAtom } from '@recoil/app';
import useScreenSize from '@hooks/useScreenSize';
import { formTheme } from '../../style/variables';

import DataIntakeFormNavigation from './components/DataIntakeFormNavigation';

const StyledFormLabel = styled.div`
  color: ${({ theme }) => theme.colors.formText};
  text-transform: none;
`;
const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  height: 100%;
  background-color: ${({ theme }) => theme.secondary[100]};
`;
const {
  colors,
  primary,
  secondary,
  fontSizes,
  breakpoints,
  screenSize,
  textAndColor: {
    form: { textTransform, color },
  },
} = formTheme;
const theme = {
  colors,
  primary,
  secondary,
  fontSizes,
  breakpoints,
  screenSize,
  textTransform,
  color,
};

/**
 * The props for the DataIntakeForm are set to state here to keep the form generic.
 * This way, we can use these Recoil objects rather than referencing the leadIntake or workReceipt configuration files directly.
 *
 * map: Overall map of the form. Holds top level form sections and indicates the order of fields within each section
 * fields: Definition of the fields used by the form. These are used in formSettingsState to handle numUnit change. Not sure if there's other times they will be needed
 * valuesState: This is the recoil state object that holds the values for the form. For example the leadIntakeValuesState or the workReceiptValuesState.
 */

const DataIntakeForm = ({
  map,
  fields,
  valuesState,
  tabs,
  actions,
  stepValidation = false,
  navActions = <></>,
}) => {
  const [activeTab] = useRecoilState(activeTabIndexAtom(['tabs']));
  const setFormMap = useSetRecoilState(formMapState);
  const setFormFieldsState = useSetRecoilState(formFieldsState);
  const setFormRecoilState = useSetRecoilState(formRecoilState);
  const isMobile = useScreenSize();
  useEffect(() => {
    setFormMap(map);
    setFormFieldsState(fields);
    setFormRecoilState(valuesState);
  }, [map, setFormMap, valuesState, setFormRecoilState, setFormFieldsState, fields]);
  return (
    <ThemeProvider theme={theme}>
      <StyledFormLabel>
        <ContentWrapper isMobile={isMobile}>
          {navActions}
          <DataIntakeFormNavigation tabs={tabs} stepValidation={stepValidation} />
          {tabs[activeTab]?.component && React.cloneElement(tabs[activeTab].component, { actions })}
        </ContentWrapper>
      </StyledFormLabel>
    </ThemeProvider>
  );
};

DataIntakeForm.propTypes = {
  map: PropTypes.shape({}).isRequired,
  fields: PropTypes.shape({}).isRequired,
  valuesState: PropTypes.shape({}).isRequired,
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      component: PropTypes.node.isRequired,
    }),
  ).isRequired,
  stepValidation: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.bool), PropTypes.bool]),
  actions: PropTypes.node.isRequired,
  navActions: PropTypes.node,
};

export default DataIntakeForm;
