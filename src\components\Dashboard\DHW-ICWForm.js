import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput, FormTextBox } from '@components/global/Form';
import { openPhoneCall, displayPhoneNumber } from '@utils/functions';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const DHWICWForm = ({ record = {} }) => {
  const {
    dealDealId,
    opportunityName,
    hesDhwVisitResult,
    hesDhwQuoteAmount,
    hesDhwProductQuoted,
    hesDhwPaymentMethod,
    hesDhwOfficeInstallNotes,
    hesDhwFollowUpDate,
    phoneNumber,
  } = record;
  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealDealId} title="Doc Repo" />
        <InteractiveButtons dealId={dealDealId} title="Work Receipt" />
      </InteractiveRow>
      <FormInput
        readOnly
        name="opportunityName"
        value={opportunityName}
        title="Opportunity Name"
        placeholder=""
      />
      <Row>
        <Col>
          <FormInput readOnly name="dealDealId" value={dealDealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="hesDhwVisitResult"
            value={hesDhwVisitResult}
            title="HES DHW Visit Result"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hesDhwQuoteAmount"
            value={hesDhwQuoteAmount}
            title="HES DHW Quote Amount"
            placeholder=""
          />
          <FormInput
            readOnly
            name="phoneNumber"
            value={displayPhoneNumber(phoneNumber)}
            title="Phone Number"
            onClick={() => openPhoneCall(phoneNumber)}
            placeholder="Phone Number"
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="hesDhwFollowUpDate"
            value={hesDhwFollowUpDate}
            title="HES DHW Follow Up Date"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hesDhwPaymentMethod"
            value={hesDhwPaymentMethod}
            title="HES DHW Product Quoted"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hesDhwProductQuoted"
            value={hesDhwProductQuoted}
            title="HES DHW Product Quoted"
            placeholder=""
          />
        </Col>
      </Row>
      <FormTextBox
        readOnly
        name="hesDhwOfficeInstallNotes"
        value={hesDhwOfficeInstallNotes}
        title="HES DHW Office/Install Notes"
        placeholder=""
      />
    </>
  );
};

DHWICWForm.propTypes = {
  record: PropTypes.shape({
    dealDealId: PropTypes.string,
    opportunityName: PropTypes.string,
    hesDhwVisitResult: PropTypes.string,
    hesDhwQuoteAmount: PropTypes.string,
    hesDhwProductQuoted: PropTypes.string,
    hesDhwPaymentMethod: PropTypes.string,
    hesDhwOfficeInstallNotes: PropTypes.string,
    hesDhwFollowUpDate: PropTypes.string,
    phoneNumber: PropTypes.string,
  }),
};

export default DHWICWForm;
