import React from 'react';
import { useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';

import { monthStartAtom } from '@recoil/calendar';

const StyledDateNumber = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 2px 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  color: ${({ theme }) => theme.primary[500]};
  ${({ notInMonth, isToday }) => {
    if (notInMonth) return `color: ${({ theme }) => theme.secondary[500]};`;
    if (isToday)
      return `
      background-color: ${({ theme }) => theme.colors.actionButton};
      color: ${({ theme }) => theme.secondary[100]};
    `;
    return '';
  }};
`;

const DateNumber = (props) => {
  const monthStart = useRecoilValue(monthStartAtom);

  const { date } = props;
  const cellDay = date.format('D');

  let isToday = false;
  let notInMonth = false;
  if (date.isSame(moment(), 'day')) isToday = true;
  if (!date.isSame(monthStart, 'month')) notInMonth = true;

  return (
    <StyledDateNumber isToday={isToday} notInMonth={notInMonth}>
      {cellDay}
    </StyledDateNumber>
  );
};

DateNumber.propTypes = {
  date: PropTypes.instanceOf(moment).isRequired,
};

export default DateNumber;
