import React, { Suspense, useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState, useRecoilValue } from 'recoil';
import moment from 'moment';
import { decodeEventType } from '@homeworksenergy/utility-service';

import { selectedEventState } from '@recoil/eventSidebar';
import { allAgentsFormOptionsState } from '@recoil/agents';

import useStartEndTimes from '@hooks/useStartEndTimes';

import { isAuthorized } from '@utils/AuthUtils';

import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  FormTextBox,
  FormInfoField,
  FormInfo,
  FormStartEndDateTimePickers,
} from '@components/global/Form';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import EventSidebarHeader, {
  HeaderLabel,
  HeaderTitle,
} from '@components/EventSidebar/EventSidebarHeader';
import Pin from '@components/Calendar/Pin';
import Lock from '@components/Calendar/Lock';
import TownWarnings from '@components/townWarnings/TownWarnings';
import { DropDownMenu, LoadingIndicator } from '@components/global';
import { UtilityManager } from '@utils/APIManager';

const IconContainer = styled.div`
  margin-right: 10px;
  margin-top: auto;
  height: 24px;
  & :hover {
    cursor: pointer;
  }
`;

const SubFormCT = ({ handleCancelClick, handleSaveClick }) => {
  const [event, setEvent] = useRecoilState(selectedEventState);
  const agents = useRecoilValue(allAgentsFormOptionsState);

  const [loading, setLoading] = useState(false);
  const {
    id,
    sfIds,
    customerName,
    notes,
    phoneNumber,
    email,
    oids,
    date,
    scheduledBy,
    scheduledDate,
    type,
    address,
    lock,
    startEndTimes,
    arrivalWindow,
  } = event;

  const isCreate = !id;

  const createEventTypes = [
    { key: 'Custom Block', value: '999999' },
    { key: 'Return Visit', value: '010004' },
  ];

  useEffect(() => {
    if (!date || !isCreate) return;

    // If no type for create event, push to the custom block form
    if (!type) setEvent({ ...event, type: createEventTypes[0].value });
  }, [date, createEventTypes, type, isCreate, setEvent, event]);

  // For some reason, this needs to be below the above useEffect, or else its value gets overwritten with the previous event value
  const handleTimeChange = useStartEndTimes();

  const displayAddress = address?.displayAddress;

  const isSchedulerLevel = isAuthorized('Scheduler', 'Subcontractor', null, 'CT');
  const canPerformActions = isSchedulerLevel && !lock;

  const agent = agents.find((agent) => agent.value === oids[0]);

  const hesName = agent?.key;

  // TODO: should this display an error message if the event type is not found?
  const eventTypeName = type ? decodeEventType(type)?.businessEvent : '';

  const { accountId } = sfIds;

  const dropDownList = [
    {
      text: 'Go to Salesforce Page',
      onClick: () => onClickAccountId(accountId),
    },
  ];

  const handleFieldChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  const onClickAccountId = (accountId) => {
    if (!isCreate && accountId) UtilityManager.openSf2Page(accountId, 'Account');
  };

  const arriwalWindowWarning = [
    {
      townWarningName: `Arrival Window: ${arrivalWindow}`,
      townWarningDescription:
        'The arrival window shown is for communication with the customer. Any changes to the official start time of the visit need to be communicated to the utilities.',
    },
  ];
  const renderFormFields = () => {
    return (
      <>
        {arrivalWindow && <TownWarnings townWarnings={arriwalWindowWarning} />}
        <EventSidebarBody>
          <Row>
            <Col size={1}>
              <SfIdInputs sfObjectType="account" title="salesforce" setLoading={setLoading} />
            </Col>
            <Col size={1}>
              <SfIdInputs sfObjectType="workOrder" readOnly setLoading={setLoading} />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput readOnly name="date" value={date} title="date" placeholder="" />
            </Col>
            <Col size={1}>
              {isCreate ? (
                <FormSelect
                  required
                  title="Visit Type"
                  placeholder="Select Visit Type"
                  name="type"
                  value={type}
                  onChange={handleFieldChange}
                  options={createEventTypes}
                />
              ) : (
                <FormInput
                  readOnly
                  name="eventTypeName"
                  value={eventTypeName}
                  title="Visit Type"
                  placeholder=""
                />
              )}
            </Col>
          </Row>
          <Row>
            <Col size={2}>
              <FormStartEndDateTimePickers
                key="startEndTime"
                name="startEndTime"
                displayDay={false}
                startEndTimes={startEndTimes}
                onChange={handleTimeChange}
                dateFormat="h:mm aa"
                allowDateSelect={false}
                direction="row"
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormInput
                readOnly
                name="address"
                value={displayAddress || ''}
                title="location"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput
                readOnly
                name="phoneNumber"
                value={phoneNumber}
                title="Customer Phone Number"
                placeholder=""
              />
            </Col>
            <Col size={1}>
              <FormInput
                readOnly
                name="email"
                value={email}
                title="Customer Email"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormTextBox
                name="notes.officeNotes"
                value={notes.officeNotes}
                title="Lead Scheduling Notes"
                placeholder=""
                onChange={handleFieldChange}
              />
            </Col>
          </Row>
          {isSchedulerLevel && !isCreate && (
            <Row>
              <Col>
                <FormInfo>
                  <FormInfoField title="Scheduled By :" body={scheduledBy} />
                  <FormInfoField
                    title="Scheduled On :"
                    body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
                  />
                </FormInfo>
              </Col>
            </Row>
          )}
        </EventSidebarBody>
      </>
    );
  };

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col size={2}>
              <HeaderLabel>Customer Name:</HeaderLabel>
              <HeaderTitle>{customerName}</HeaderTitle>
            </Col>
            <Col size={1} right>
              <HeaderLabel>HES:</HeaderLabel>
              <HeaderTitle>{hesName}</HeaderTitle>
            </Col>
            <Col size={0} left>
              <Row>
                <IconContainer>
                  {canPerformActions && <Pin event={event} onSidebar />}
                </IconContainer>
                <IconContainer>{isSchedulerLevel && <Lock event={event} />}</IconContainer>
                <DropDownMenu listItems={dropDownList} />
              </Row>
            </Col>
          </Row>
        </EventSidebarHeader>
        {renderFormFields()}
        <EventSidebarFooter
          leftButtons={
            <>
              <PrimaryButton onClick={() => handleSaveClick()}>Save</PrimaryButton>
              {canPerformActions && !isCreate && (
                <CancelButton onClick={() => handleCancelClick()}>Cancel Event</CancelButton>
              )}
            </>
          }
        />
      </Suspense>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

SubFormCT.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
  history: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
};

export default withRouter(SubFormCT);
