import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { Checkbox } from '@components/global';
import { Row } from '@components/global/Form';
import FormFieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';
import RecoilFieldOptions from './RecoilFieldOptions';

const FormCheckboxesContainer = styled.div`
  ${({ border, theme }) => {
    return border
      ? `border: 1px solid ${theme.secondary[300]};
         box-sizing: border-box;
         border-radius: 8px;
         padding: 5px;`
      : '';
  }}
`;

const FormCheckboxesContent = styled.div`
  display: flex;
  flex-flow: ${({ column }) => {
    return column ? 'column wrap' : '';
  }};
  width: 100%;
  flex-direction: ${({ column }) => {
    return column ? 'column' : '';
  }};
  padding: 18px 16px 16px 0px;
`;

const FormCheckboxes = (props) => {
  const {
    title,
    name,
    options = [{ key: '', value: '' }],
    recoilOptions = null,
    value: selectedCheckboxValues = '',
    onChange = () => {},
    border = false,
    required = false,
    column = true,
    compact = false,
  } = props;
  const handleCheckboxChange = (option) => {
    const index = selectedCheckboxValues.indexOf(option.value);

    const isChecked = index > -1;
    // If it's already selected, filter it out (without mutating the original from props)
    // else, add it in (without mutating original)
    const newValues = isChecked
      ? selectedCheckboxValues.filter((value) => value !== option.value)
      : [...selectedCheckboxValues, option.value];

    onChange({
      target: {
        name,
        value: newValues,
      },
    });
  };

  const renderCheckboxes = () => {
    return (
      <FormCheckboxesContent column={column}>
        {options.map((option) => {
          const { key, value: valueId } = option;
          return (
            <Checkbox
              name={key}
              key={key}
              label={key}
              value={selectedCheckboxValues.includes(valueId)}
              onChange={() => handleCheckboxChange(option)}
            />
          );
        })}
      </FormCheckboxesContent>
    );
  };
  if (recoilOptions) return <RecoilFieldOptions Component={FormCheckboxes} {...props} />;
  return (
    <FormCheckboxesContainer border={border}>
      <FormFieldContainer required={required} border={border} compact={compact}>
        <FormFieldLabel>{title}</FormFieldLabel>
        <Row>{renderCheckboxes()}</Row>
      </FormFieldContainer>
    </FormCheckboxesContainer>
  );
};

FormCheckboxes.propTypes = {
  title: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(PropTypes.shape({})),
  value: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.shape({})]),
  ),
  onChange: PropTypes.func,
  border: PropTypes.bool,
  required: PropTypes.bool,
  column: PropTypes.bool,
  recoilOptions: PropTypes.shape({}),
  compact: PropTypes.bool,
};

export default FormCheckboxes;
