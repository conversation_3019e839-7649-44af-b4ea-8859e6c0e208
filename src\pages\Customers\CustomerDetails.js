import React, { useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import { decodeEventType } from '@homeworksenergy/utility-service';

import { HorizontalLine, Skeleton, Header as Title } from '@components/global';
import { PageHeader } from '@pages/Components';
import { useParams } from 'react-router-dom/cjs/react-router-dom.min';
import { User } from '@styled-icons/boxicons-solid/User';
import { ArrowBack } from '@styled-icons/boxicons-regular/ArrowBack';
import { Salesforce } from '@styled-icons/fa-brands/Salesforce';
import { CustomerManager } from '@utils/APIManager';
import { formatPhoneNumber } from '@utils/functions';
import Swal from 'sweetalert2/dist/sweetalert2';
import useScreenSize from '@hooks/useScreenSize';
import { useHistory } from 'react-router-dom';
import { salesforceUrls } from '@utils/constants';
import { Timeline } from './CustomerTimeline';
import {
  FlexColumn,
  FlexRow,
  InformationIcon,
  CardContainer,
  HomeIcon,
  PhoneIcon,
  MailIcon,
} from './styles';

const Avatar = styled(User)`
  color: ${({ theme }) => theme.colors.nameIniBg};
  height: 44px;
  margin: 0;
  @media (max-width: 375px) {
    display: none;
  }
`;
export const ArrowIconBack = styled(ArrowBack)`
  color: ${({ color, theme }) => color || theme.primary[400]};
  height: 30px;
  margin-left: 0;
  margin-right: 4px;
`;
const SalesforceIcon = styled(Salesforce)`
  color: ${({ theme }) => theme.primary[400]};
  height: 20px;
  margin-right: 4px;
`;
const Text = styled.span``;

const CustomerDetails = () => {
  const history = useHistory();
  const { customerId } = useParams();
  const isMobile = useScreenSize(575);
  const [customerDetails, setCustomerDetails] = useState([]);
  const [dealIds, setDealIds] = useState([]);
  const [customerDealId, setCustomerDealId] = useState('');
  const { events = [], customer = '' } = customerDetails;

  const { state } = events[0] ? decodeEventType(events[0]?.type) : '';

  const devSalesforceUrl = state === 'CT' ? salesforceUrls.dev2 : salesforceUrls.dev;
  const prodSalesforceUrl = state === 'CT' ? salesforceUrls.prod2 : salesforceUrls.prod;

  const host = window.location.origin;

  const salesforceUrl =
    host === 'https://sch.homeworksenergy.com' ? prodSalesforceUrl : devSalesforceUrl;

  const setDealIdsFromDetails = useCallback((sfIds) => {
    if (!sfIds) return;
    const dealList = [];
    const order = ['dealId', 'dealId2', 'dealId3', 'dealId4'];
    order.forEach((key) => {
      if (key in sfIds) {
        dealList.push(sfIds[key]);
      }
    });
    if (dealList.length) {
      setDealIds(dealList);
    }
  }, []);

  const {
    customerName = '',
    accountId = '',
    address = '',
    email = '',
    phoneNumber = '',
  } = customer;

  const handleIdClick = (sfId) => {
    if (sfId) {
      window.open(`${salesforceUrl}${sfId}`, '_blank');
    } else {
      return Swal.fire({ icon: 'error', title: 'Invalid SfID' });
    }
    return false;
  };

  useEffect(() => {
    if (customerId) {
      const getCustomerDetails = async () => {
        try {
          const response = await CustomerManager.getCustomerTimeline({
            customerId,
          });
          setCustomerDetails(response);
          setDealIdsFromDetails(response?.events?.[0]?.sfIds);
          setCustomerDealId(response?.dealId);
          return false;
        } catch (err) {
          console.error(err);
          return Swal.fire({
            icon: 'error',
            title: 'Error Occured While Fetching Customer',
            text: err,
          });
        }
      };
      getCustomerDetails(customerId);
    }
  }, [customerId]);

  if (!customerDetails.customer)
    return (
      <>
        <PageHeader>
          <ArrowIconBack onClick={() => history.goBack()} /> Customer Details
        </PageHeader>
        <CardContainer>
          <Skeleton />
        </CardContainer>
      </>
    );
  return (
    <>
      <PageHeader>
        <ArrowIconBack onClick={() => history.goBack()} /> Customer Details
      </PageHeader>
      <CardContainer>
        <FlexRow>
          <Avatar />
          <FlexColumn gap="0px">
            <Title h2 weight="bold">
              {customerName}
            </Title>
          </FlexColumn>
        </FlexRow>
        <Title h3 weight="bold">
          Details
        </Title>
        <FlexRow gap="4px">
          <InformationIcon />
          <Title h5>
            Clicking on &quot;Account ID or Deal ID&quot; redirects to the Salesforce page.
          </Title>
        </FlexRow>
        <HorizontalLine />
        <FlexColumn>
          <FlexRow>
            <HomeIcon />
            <Text>{address || 'No Address Available'}</Text>
          </FlexRow>
          <FlexRow>
            <PhoneIcon />
            <Text>{formatPhoneNumber(phoneNumber) || 'No Phone Number Available'}</Text>
          </FlexRow>
          <FlexRow>
            <MailIcon />
            <Text>{email || 'No Email Available'}</Text>
          </FlexRow>
          <FlexRow cursor="pointer" onClick={() => handleIdClick(accountId)}>
            <SalesforceIcon />
            <Text>Account ID: {customerId || 'No Account ID Available'}</Text>
          </FlexRow>
          {!dealIds.length && (
            <FlexRow cursor="pointer">
              <SalesforceIcon />
              <Text>Deal Id: {customerDealId || 'No Deal ID Available'}</Text>
            </FlexRow>
          )}
          <FlexColumn>
            {dealIds?.map((deal, index) => (
              <FlexRow key={deal} cursor="pointer" onClick={() => handleIdClick(deal, 'Deal__c')}>
                <SalesforceIcon />
                <Text>
                  Deal {index + 1}: {deal || 'No Deal ID Available'}
                </Text>
              </FlexRow>
            ))}
          </FlexColumn>
        </FlexColumn>
        <Timeline
          events={events || []}
          isMobile={isMobile}
          handleIdClick={(dealId, path) => handleIdClick(dealId, path)}
        />
      </CardContainer>
    </>
  );
};

export default CustomerDetails;
