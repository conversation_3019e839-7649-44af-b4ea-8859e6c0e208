import React, { useState } from 'react';
import moment from 'moment';
import { useRecoilValue, useRecoilState } from 'recoil';
import Swal from 'sweetalert2/dist/sweetalert2';
import { isHWEUser } from '@homeworksenergy/utility-service';

import { eventTypeOptionsSelectorFamily, departmentsSelector } from '@recoil/app';
import {
  selectedAgentAtom,
  agentAvailabilityInfoState,
  allAuthorizedAgentsForUserSelector,
  refreshAgentDatesAtom,
} from '@recoil/admin/agents';

import {
  refreshAdminListState,
  adminInfoChangesState,
  addressesAreValidState,
} from '@recoil/admin';

import { useInvalidateSelector } from '@recoil/hooks';

import eventValidation from '@utils/eventValidation';
import { AgentsManager, SlotsManager } from '@utils/APIManager';

import { throwError } from '@utils/EventEmitter';
import ListItemDetail from '@components/global/ScreenPartitionView/ListItemDetail';

import EditAgentInfoForm from './EditAgentInfoForm';
import EditAgentEventTypesForm from './EditAgentEventTypesForm';
import EditAgentAvailabilityForm from './EditAgentAvailabilityForm';

const AgentListItemDetail = () => {
  const agents = useRecoilValue(allAuthorizedAgentsForUserSelector);
  const refreshAgents = useInvalidateSelector(refreshAdminListState);
  const [selectedAgent, setSelectedAgent] = useRecoilState(selectedAgentAtom);
  const refreshDates = useInvalidateSelector(refreshAgentDatesAtom);
  const agentAvailabilityInfo = useRecoilValue(agentAvailabilityInfoState);
  const agentInfoChanges = useRecoilValue(adminInfoChangesState);
  const addressesAreValid = useRecoilValue(addressesAreValidState);
  const departments = useRecoilValue(departmentsSelector);
  const agentCapability = useRecoilValue(
    eventTypeOptionsSelectorFamily({
      departmentId: selectedAgent.department,
      state: selectedAgent.state,
    }),
  );

  const [isAvailabilityTab, setIsAvailabilityTab] = useState(false);

  const { regionAbbreviation, companyName, departmentName, displayName } = selectedAgent;

  // Only show ' | ' for values that exist
  const detailText =
    [regionAbbreviation, companyName, departmentName].filter((detail) => detail).join(' | ') ||
    'More details will show here';

  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    startTime: selectedStartTime,
    endTime: selectedEndTime,
    maxAppt,
    action,
    days,
    overrideHoliday,
  } = agentAvailabilityInfo;

  const tabs = [
    {
      name: 'info',
      title: 'Agent Info',
      component: <EditAgentInfoForm />,
    },
    {
      name: 'capabilities',
      title: 'Agent Capability',
      component: <EditAgentEventTypesForm />,
    },
    {
      name: 'availability',
      title: 'Agent Availability',
      component: <EditAgentAvailabilityForm />,
      onTabEnter: () => setIsAvailabilityTab(true),
      onTabExit: () => setIsAvailabilityTab(false),
    },
  ];

  const isCreateAgent = !selectedAgent?.oid;

  const createAgent = async () => {
    // TODO: this will currently work for hvac and insulation but it should be hooked into the specific department
    const params = eventValidation.MA.Insulation.createAgent(agentInfoChanges);
    if (!params) return false;

    const createdAgent = await AgentsManager.createAgent(params);
    if (!createdAgent) return false;

    setSelectedAgent(createdAgent);
    return true;
  };

  const saveAgentInfo = async () => {
    const { homeAddress, dayStartAddress, region = null } = agentInfoChanges;
    const hweUser = isHWEUser({ company: selectedAgent.company });
    if ((homeAddress || dayStartAddress) && !addressesAreValid)
      return Swal.fire({ title: 'Please Select Addresses From The Address Drop-Down' });
    if (isCreateAgent) return createAgent();

    const agentDepartment = agentInfoChanges?.department || selectedAgent?.department;
    const selectedDepartment = departments.find(
      (department) => department.value === agentDepartment,
    ).name;
    const parsedDepartment = selectedDepartment.replace('-', ' ');

    // ON UPDATE CREW ALSO CHECK IF USER SELECTED HWE THEN REGION SHOULD BE SELECTED
    if (hweUser && selectedAgent.region === null && region === null) {
      return throwError('Please Select Region');
    }

    if (eventValidation.MA?.[parsedDepartment]?.updateAgent) {
      const { isValid, agentUpdatedChanges } = eventValidation.MA[parsedDepartment].updateAgent({
        agentInfoChanges,
        selectedAgent,
        agentCapability,
      });
      if (!isValid) return false;
      await AgentsManager.updateAgent(selectedAgent.oid, agentUpdatedChanges);
    } else {
      await AgentsManager.updateAgent(selectedAgent.oid, agentInfoChanges);
    }

    if (isAvailabilityTab) await saveAvailability();

    return true;
  };

  const openDays = async () => {
    const params = {
      days,
      startDate: moment(selectedStartDate),
      endDate: moment(selectedEndDate),
      oids: [selectedAgent.oid],
      maxAppt,
      startTime: selectedStartTime.format('hh:mm'),
      endTime: selectedEndTime.format('HH:mm'),
      overrideHoliday,
    };

    await SlotsManager.openAgents(params);
    refreshDates();
  };

  const closeDays = async () => {
    const params = {
      days,
      startDate: moment(selectedStartDate),
      endDate: moment(selectedEndDate),
      oids: [selectedAgent.oid],
    };

    await SlotsManager.closeAgents(params);
    refreshDates();
  };

  const deleteAgent = async () => {
    const alert = await Swal.fire({
      title: 'Do you want to deactivate the agent?',
      showCloseButton: true,
      showCancelButton: true,
      showConfirmButton: true,
      confirmButtonText: 'Deactivate',
      cancelButtonText: 'Cancel',
    });
    if (!alert?.value) return;

    await AgentsManager.updateAgent(selectedAgent.oid, { active: false });
    refreshAgents();
  };

  const saveAvailability = action === 'open' ? openDays : closeDays;

  return (
    <ListItemDetail
      tabs={tabs}
      listItems={agents}
      selectedItem={selectedAgent}
      setSelectedItem={setSelectedAgent}
      onSaveButtonClick={saveAgentInfo}
      onDeleteButtonClick={deleteAgent}
      headerText={displayName}
      detailText={detailText}
    />
  );
};

export default AgentListItemDetail;
