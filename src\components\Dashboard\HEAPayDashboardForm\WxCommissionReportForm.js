import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const WxCommissionReportForm = ({ record = {} }) => {
  const {
    finalContractAmount,
    baseWxCommission,
    finalBmsPrice,
    heaVisitResultDetail,
    hesBmsCommissionTotal,
    milestoneWxCommission,
    wxCommission,
    wxCommissionTotal,
  } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput
            readOnly
            name="finalContractAmount"
            value={`$${finalContractAmount}`}
            title="Final Contract Amount"
            placeholder=""
          />
          <FormInput
            readOnly
            name="baseWxCommission"
            value={`${baseWxCommission}`}
            title="Base WX Commission"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hesBmsCommissionTotal"
            value={`$${hesBmsCommissionTotal}`}
            title="HES BMS Commission Total"
            placeholder=""
          />
          <FormInput
            readOnly
            name="wxCommission"
            value={wxCommission}
            title="WX Commission %"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="finalBmsPrice"
            value={`$${finalBmsPrice}`}
            title="Final BMS Price"
            placeholder=""
          />
          <FormInput
            readOnly
            name="heaVisitResultDetail"
            value={heaVisitResultDetail}
            title="HEA Visit Result Detail"
            placeholder=""
          />
          <FormInput
            readOnly
            name="milestoneWxCommission"
            value={`$${milestoneWxCommission}`}
            title="Milestone WX Commission"
            placeholder=""
          />
          <FormInput
            readOnly
            name="wxCommissionTotal"
            value={`$${wxCommissionTotal}`}
            title="WX Commission Total"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

WxCommissionReportForm.propTypes = {
  record: PropTypes.shape({
    finalContractAmount: PropTypes.string,
    baseWxCommission: PropTypes.string,
    finalBmsPrice: PropTypes.string,
    heaVisitResultDetail: PropTypes.string,
    hesBmsCommissionTotal: PropTypes.string,
    milestoneWxCommission: PropTypes.string,
    wxCommission: PropTypes.string,
    wxCommissionTotal: PropTypes.string,
  }),
};

export default WxCommissionReportForm;
