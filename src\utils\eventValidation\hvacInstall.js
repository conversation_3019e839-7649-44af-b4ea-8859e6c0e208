import validateRequiredParams from '@utils/validateRequiredParams';
import { verifySingleWeek } from '@utils/functions';
import { throwError } from '@utils/EventEmitter';

const create = (params) => {
  const {
    sfIds,
    oids,
    phoneNumber,
    type,
    date,
    jobLength,
    notes,
    startEndTimes,
    address,
    equipmentOrderStatus,
    numUnit,
    oid,
    sfIds: { accountId },
    projectManager,
    concierge,
  } = params;

  const requiredFields = {
    'Crew(s)': oids,
    'Job Type': type,
    Date: date,
    'Account Id': accountId,
    Address: address,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const createData = {
    date,
    oids,
    type,
    jobLength,
    sfIds,
    notes,
    phoneNumber,
    startEndTimes,
    address,
    shadow: false,
    equipmentOrderStatus,
    numUnit,
    oid,
    accountId,
    projectManager,
    concierge,
  };

  // Create slots using the start and end times
  return createData;
};

const update = (params) => {
  const {
    address,
    date,
    associatedEventIds,
    associatedEventsId,
    oids,
    sfIds,
    type,
    jobLength,
    notes,
    startEndTimes,
    equipmentOrderStatus,
    eventName,
  } = params;

  const requiredFields = {
    'Truck(s)': oids,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const updateData = {
    date,
    oids,
    type,
    sfIds,
    notes,
    jobLength,
    associatedEventIds,
    associatedEventsId,
    startEndTimes,
    equipmentOrderStatus,
    eventName,
    address,
  };

  return updateData;
};

const getSlots = (params) => {
  const { type, jobLength, address } = params;

  const requiredFields = {
    'Job Type': type,
    'Job Length': jobLength,
    Address: address,
  };
  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  return [params];
};

const cancel = () => true;

const reassign = () => true;

const reschedule = (params) => {
  const { date, jobLength } = params;
  if (!verifySingleWeek(date, jobLength))
    return throwError('Events must start and end on the same week');

  return params;
};

const isGroupHavingJobTypes = ({ agentCapability, agentInfoChanges, selectedAgent }) => {
  const selectedGroups = agentInfoChanges?.group;
  const eventTypes = agentInfoChanges?.eventTypes || selectedAgent?.eventTypes;

  let hasMatchingEventTypes = true;
  if (selectedGroups) {
    const groupsWithMissingValues = selectedGroups.filter((group) => {
      if (group === 'ungrouped') {
        return false;
      }

      const matchingCapability = agentCapability?.find((capability) => {
        return capability.value === group;
      });
      if (!matchingCapability) {
        return true;
      }

      const matchingEventTypes = matchingCapability?.eventTypes?.filter((eventType) =>
        eventTypes.includes(eventType.value),
      );
      if (matchingEventTypes.length === 0) {
        return true;
      }

      return false;
    });

    if (groupsWithMissingValues.length > 0) {
      hasMatchingEventTypes = false;
      throwError(
        `Missing required fields. Please select job types for ${groupsWithMissingValues.join(
          ', ',
        )}`,
      );
      return hasMatchingEventTypes;
    }
  }
  return true;
};

// updateAgent is responsible for showing Pop up if following validation error occured
// If group is selected then one of its job type should be selected else throw error
// If group is unselected their jobtypes should be auto unchecked when saving hvac crew
const updateAgent = ({ agentCapability = [], agentInfoChanges, selectedAgent }) => {
  const updatedAgentInfoChanges = { ...agentInfoChanges };
  let isValid = true;
  const hasMatchingEventTypes = isGroupHavingJobTypes({
    agentCapability,
    agentInfoChanges,
    selectedAgent,
  });

  if (hasMatchingEventTypes && agentInfoChanges.group) {
    const updatedEventTypes = selectedAgent?.eventTypes?.filter((eventType) =>
      agentCapability?.some(
        (grp) =>
          grp.eventTypes.some((groupEventType) => groupEventType.value === eventType) &&
          agentInfoChanges?.group.includes(grp.value),
      ),
    );
    updatedAgentInfoChanges.eventTypes = updatedEventTypes;
  }
  if (agentInfoChanges?.sendNotification && agentInfoChanges?.notificationChannel?.length === 0)
    isValid = false;
  if (agentInfoChanges?.phoneNumber) {
    isValid = agentInfoChanges?.phoneNumber?.replace(/\D/g, '')?.length === 10;
  }
  return {
    isValid: hasMatchingEventTypes && isValid,
    agentUpdatedChanges: updatedAgentInfoChanges,
  };
};

const hvacInstall = { create, update, getSlots, cancel, reassign, reschedule, updateAgent };
export default hvacInstall;
