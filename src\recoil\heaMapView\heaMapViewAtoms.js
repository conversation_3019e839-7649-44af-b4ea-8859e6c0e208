import { atom } from 'recoil';
import moment from 'moment';

// Selected date for the map view
export const heaSelectedDateAtom = atom({
  key: 'heaSelectedDate',
  default: moment().startOf('day'),
});

// All appointments for the selected date
export const heaAppointmentsAtom = atom({
  key: 'heaAppointments',
  default: [],
});

// HES agents with appointment counts
export const heaAgentsAtom = atom({
  key: 'heaAgents',
  default: [],
});

// Active agent filters (Set of agent OIDs)
export const heaActiveFiltersAtom = atom({
  key: 'heaActiveFilters',
  default: new Set(),
});

// Currently selected HES agent OID
export const heaSelectedHesOidAtom = atom({
  key: 'heaSelectedHesOid',
  default: null,
});

// Loading state
export const heaLoadingAtom = atom({
  key: 'heaLoading',
  default: false,
});
