import React from 'react';
import PropTypes from 'prop-types';
import { decodeEventType } from '@homeworksenergy/utility-service';
// import { throwError } from '@utils/EventEmitter';

import {
  HEAEvent,
  HVACInstallEvent,
  InsulationInstallEvent,
  CustomBlockEvent,
  HVACSalesEvent,
} from '.';
import HEAEventCT from './CT/HEAEventCT';
import SubEventCT from './CT/SubEventCT';

const DepartmentEvent = (props) => {
  const { event, ...otherProps } = props;
  const { type } = event;
  const { state, business: department } = decodeEventType(type);

  const eventsMap = {
    MA: {
      HEA: HEAEvent,
      'HVAC Sales': HVACSalesEvent,
      'HVAC Install': HVACInstallEvent,
      Insulation: InsulationInstallEvent,
    },
    CT: {
      HEA: HEAEventCT,
      Subcontractor: SubEventCT,
    },
  };

  const EventComponent = department === 'N/A' ? CustomBlockEvent : eventsMap[state][department];

  // if (!EventComponent)
  //   return throwError(
  //     `Error occurred on event: ${id} date: ${date} agentId: ${oid} startTime: ${startTime} department: ${department} in state: ${state}`,
  //   );

  return <EventComponent event={event} {...otherProps} />;
};

DepartmentEvent.propTypes = {
  event: PropTypes.shape({ type: PropTypes.string }).isRequired,
};

export default DepartmentEvent;
