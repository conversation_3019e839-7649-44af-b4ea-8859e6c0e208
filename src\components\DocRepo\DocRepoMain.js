import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { SalesforceManager } from '@utils/APIManager/index';
import DocumentTable from './DocumentTable';
import { sfObjectName, resultingQuestions } from './DocRepoFuncs';
import DocRepoQuestionPage from './ResultingQuestions';

const StyledContainer = styled.div``;

const DocRepoMain = (props) => {
  const {
    match: {
      params: { state, department, uniqueId, company = 'HWE' },
    },
  } = props;

  // Imports
  const sfObjectForResulting = sfObjectName(state, department);
  const { getResultStatus } = SalesforceManager;

  // State
  // This render status has to do with the renderComponent function below
  const [renderStatus, setRenderStatus] = useState('');
  const [valuesOnSf, setValuesOnSF] = useState([]);
  const [showBackButton, setShowBackButton] = useState(false);
  const [resultingQuestionObject, setResultingQuestionObject] = useState(
    resultingQuestions(state, department),
  );
  const { questions } = resultingQuestionObject;

  // Use Effect
  useEffect(() => {
    if (resultingQuestionObject?.questions?.length > 0) getResultingStatusFromSf();
    else setRenderStatus('beenHereBefore');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /*  
    This returns an obj with each SF field name and the default current value from DocRepoFuncs.
    This is used to check against the values with the SF field names.
    If any of them have a value this visit has been resulted before.
  */
  const returnAnObjOfSfTargetNames = (questionsArr) => {
    const sfTargetNameArr = [];
    questionsArr.forEach((questionObj) => {
      const questionCopy = { ...questionObj };
      const { sfTargetName, currentValue, notes } = questionCopy;
      const questionAndValueObj = {
        sfTargetName,
        currentValue,
      };
      if (notes) questionAndValueObj.notes = notes;
      sfTargetNameArr.push(questionAndValueObj);
    });
    return sfTargetNameArr;
  };

  // Updates the question objects notes fields.
  const updateResultingQuestionsObject = (sfValues) => {
    const questionsObjCopy = { ...resultingQuestionObject };
    for (let k = 0; k < sfValues.length; k++) {
      const { notes } = sfValues[k];
      if (notes) {
        questionsObjCopy.questions[k].notes = notes;
      }
    }
    setResultingQuestionObject(questionsObjCopy);
  };

  /* 
  Uses the sf field name and default current value from the function above.
  If any of the fields has a value it will return 1 which means it has been resulted before. This will show the documents page
  If all the field has a value of null it will return a 0 which mean it has not been resulted. This will show the questions page.
  */
  const getResultingStatusFromSf = async () => {
    const sfFieldsToCheck = returnAnObjOfSfTargetNames(questions);
    const response = await getResultStatus(sfObjectForResulting, sfFieldsToCheck, uniqueId);
    const { updatedQuestions, status, locked } = response;
    setShowBackButton(locked);
    updateResultingQuestionsObject(updatedQuestions);
    setValuesOnSF(updatedQuestions);
    setRenderStatus(status);
  };

  // Depending on the resultStatus it will render a different component.
  const renderComponent = (renderStatus) => {
    const components = {
      firstTimeHere: (
        <DocRepoQuestionPage
          state={state}
          department={department}
          uniqueId={uniqueId}
          resultingQuestionObject={resultingQuestionObject}
          sfObject={sfObjectForResulting}
          showBackButton={showBackButton}
          setRenderStatus={setRenderStatus}
        />
      ),
      beenHereBefore: (
        <DocumentTable
          state={state}
          department={department}
          uniqueId={uniqueId}
          company={company}
          valuesOnSf={valuesOnSf}
          reqDocsAndQuestionsObj={resultingQuestionObject}
          setRenderStatus={setRenderStatus}
          sfObjectForResulting={sfObjectForResulting}
        />
      ),
    };
    return components[renderStatus];
  };

  return <StyledContainer>{renderStatus ? renderComponent(renderStatus) : null}</StyledContainer>;
};

DocRepoMain.propTypes = {
  match: PropTypes.shape({
    params: PropTypes.shape({
      state: PropTypes.string,
      department: PropTypes.string,
      uniqueId: PropTypes.string,
      company: PropTypes.string,
    }),
  }).isRequired,
};

export default DocRepoMain;
