import React, { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import FormFieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';

const StyledInput = styled.input`
  width: 100%;
  min-height: 30px;
  background: ${({ theme }) => theme.secondary[100]};
  border: 1px solid ${({ isInvalid, theme }) => (isInvalid ? 'red' : `${theme.secondary[300]}`)};
  box-sizing: border-box;
  border-radius: 4px;
  ${({ readOnly, theme }) =>
    readOnly &&
    `
    border: 0px;
    background: ${theme.secondary[200]};
    border-color: ${theme.secondary[400]};
  `}
`;

const StyledText = styled.div`
  padding-top: 5px;
  color: ${({ isInvalid }) => (isInvalid ? 'red' : 'black')};
`;

const GoogleAutocompleteInput = ({
  title = 'Address',
  uniqueId = 'Google-Address-Input',
  // onPlaceChange is fired when the user selects an address from the dropdown
  onPlaceChange,
  isInvalid,
  // onChang<PERSON> is fired every time a key is pressed in the input
  onChange,
  required = false,
  name,
  compact = false,
  value = {},
  readOnly = false,
  types = ['geocode'],
}) => {
  const [isDropdownSelected, setIsDropdownSelected] = useState(null);
  const isAddressTouched = isInvalid || isDropdownSelected === false;

  // Handle selecting an address from the dropdown
  const handlePlaceChange = useCallback(
    (autocomplete) => {
      onPlaceChange(autocomplete);
      setIsDropdownSelected(true);
    },
    [onPlaceChange],
  );

  // Handle typing into the address input
  const handleChange = useCallback(
    (e) => {
      if (isDropdownSelected) {
        setIsDropdownSelected(false);
      }
      onChange(e);
    },
    [onChange, isDropdownSelected],
  );

  // if they did not select an address from the dropdown, reset the value and show an error message
  const resetValueOnInvalidAddress = useCallback(() => {
    if (typeof isDropdownSelected === 'boolean' && !isDropdownSelected) {
      onChange({ target: { name, value: '' } });
      setIsDropdownSelected(false);
    }
  }, [onChange, isDropdownSelected]);

  // When the input loses focus, fire function to check if they selected something from the dropdown
  useEffect(() => {
    const element = document.getElementById(uniqueId);

    const handleInputBlur = () => {
      resetValueOnInvalidAddress();
    };

    element.addEventListener('blur', handleInputBlur);

    return () => {
      element.removeEventListener('blur', handleInputBlur);
    };
  }, [resetValueOnInvalidAddress]);

  // Initialize the Google Maps Autocomplete
  useEffect(() => {
    if (!readOnly && window.google) {
      const element = document.getElementById(uniqueId);
      const autocomplete = new window.google.maps.places.Autocomplete(element, {
        types,
      });
      autocomplete.addListener('place_changed', () => handlePlaceChange(autocomplete));
    }
  }, [uniqueId, readOnly]);

  // If the displayAddress is invalid, reset the value and show an error message
  useEffect(() => {
    const isDisplayAddressInvalid =
      value?.displayAddress && // don't show red warning if they haven't typed anything yet
      // Handle situation where there are bad values in the selected address
      // TODO: have we ever run into issues with this? If the town name or street name contains the string 'null' then this would throw an error even if it is valid
      (value?.displayAddress.includes('undefined') || value?.displayAddress.includes('null'));

    if (isDisplayAddressInvalid) {
      onChange({ target: { name, value: '' } });
      setIsDropdownSelected(false);
    }
  }, [value]);

  const isString = typeof value === 'string';
  const isNull = value == null;
  let inputValue;

  if (isString) {
    inputValue = value;
  } else if (isNull) {
    inputValue = '';
  } else {
    inputValue = value ? value.displayAddress : '';
  }

  return (
    <FormFieldContainer required={required} compact={compact}>
      <FormFieldLabel htmlFor={uniqueId}>{title || 'Address'}</FormFieldLabel>
      <StyledInput
        id={uniqueId}
        type="search"
        onChange={handleChange}
        isInvalid={isAddressTouched}
        name={name}
        value={inputValue}
        readOnly={readOnly}
      />
      {isAddressTouched && (
        <StyledText isInvalid={isAddressTouched}>House number and Street not found!</StyledText>
      )}
    </FormFieldContainer>
  );
};

GoogleAutocompleteInput.propTypes = {
  onPlaceChange: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
  title: PropTypes.string,
  uniqueId: PropTypes.string.isRequired,
  isInvalid: PropTypes.bool.isRequired,
  required: PropTypes.bool,
  name: PropTypes.string.isRequired,
  compact: PropTypes.bool,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.shape({ displayAddress: PropTypes.string }),
  ]),
  readOnly: PropTypes.bool,
  types: PropTypes.string,
};

export default GoogleAutocompleteInput;
