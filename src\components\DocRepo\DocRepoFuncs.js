// What Salesforce object to look at depending on state and department
const sfObjectName = (state, department) => {
  const sfObjNames = {
    MA: {
      HEA: 'Deal__c',
      HVAC_Sales: 'Opportunity',
    },
    NY: {
      HEA: 'Accounts',
    },
    CT: {
      HEA: 'Project__c',
    },
  };
  if (!sfObjNames[state][department]) {
    return { error: 'Something is wrong with the url' };
  }
  return sfObjNames[state][department];
};

// Return an object of resulting questions and logic depending on state and department.
const resultingQuestions = (state, department) => {
  const questionsObj = {
    MA: {
      HVAC_Install: {
        requiredDocuments: ['PV.pdf', 'WO.pdf', 'COC.pdf', 'Invoice.pdf'],
        partnerDocuments: ['Pictures.zip'],
        questions: [],
      },
      HVAC_Sales: {
        requiredDocuments: [],
        partnerDocuments: [],
        questions: [
          {
            question: 'Resulting?',
            type: 'PickList',
            values: ['Sales Visit', 'Qualified Out', 'Closed Lost', 'Closed Won', 'Follow-Up'],
            passThroughValue: [
              'Sales Visit',
              'Qualified Out',
              'Closed Lost',
              'Closed Won',
              'Follow-Up',
            ],
            blockerValues: [],
            currentValue: '',
            sfTargetName: 'StageName',
            'Sales Visit': { requiredDoc: [] },
            'Qualified Out': {
              requiredDoc: [],
            },
            'Closed Lost': { requiredDoc: [] },
            'Closed Won': {
              requiredDoc: [],
            },
            'Follow-Up': {
              requiredDoc: [],
            },

            key: 0,
          },
        ],
      },
      Partners: {
        requiredDocuments: [],
        partnerDocuments: [],
        questions: [],
      },
    },
    CT: {
      HEA: {
        requiredDocuments: [],
        partnerDocuments: [],
        questions: [],
      },
    },
  };
  return { ...questionsObj[state][department] };
};

// Required documents checkbox on salesforce. Used to see if visit is ready to be scheduled.
const sfMissingDocField = (state, department) => {
  const sfFieldObj = {
    MA: {
      HVAC_Install: 'WT_Docs_Complete__c',
    },
  };
  return sfFieldObj[state][department];
};

export { sfObjectName, resultingQuestions, sfMissingDocField };
