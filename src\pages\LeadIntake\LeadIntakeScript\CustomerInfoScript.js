import React, { memo } from 'react';
import { useRecoilValue } from 'recoil';
import { formValuesState } from '@recoil/dataIntakeForm';
import { ScriptText, List, ListItem } from './styles';

export const CustomerInfoScript = memo(function CustomerInfoScript() {
  const { heaOrHvac } = useRecoilValue(formValuesState);
  const isHEA = heaOrHvac === 'HEA';
  return (
    <>
      <ScriptText>
        Great, based on what you told me, you should be eligible for an assessment. To get the final
        approval from MassSave, I need to get some more information.
      </ScriptText>
      <List>
        <ListItem>What is your first and last name? (Check spelling)</ListItem>
        <ListItem>
          What is the best phone number to reach you at? (Check number, check type of phone.)
        </ListItem>
        <ListItem>Do you have a second phone number you would like to add?</ListItem>
        <ListItem>
          What is your email address? (If the customer is hesitant, try saying “We will not sell
          your personal information to anyone else. Your email is just for us to contact you with
          anything regarding your project.”)
        </ListItem>
        <ListItem>What is your address?</ListItem>
      </List>
      {isHEA && (
        <ScriptText>
          Okay, now I have everything I need to get you approved for the Home Energy Assessment. Are
          you okay with me putting you on a quick hold to get you approved through Mass Save?
        </ScriptText>
      )}
    </>
  );
});
