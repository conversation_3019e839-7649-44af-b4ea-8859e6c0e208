import React from 'react';
import { Table } from '@components/global';
import PropTypes from 'prop-types';
import { PrimaryButton } from '@components/global/Buttons';
import styled from 'styled-components';
import { generateRowAndColumn } from './generateColumnsForLeads';
import DuplicateLeadsHeader from './DuplicateLeadsHeader';
import DuplicateCustomerHeader, { TableActionsContainer } from './DuplicateCustomerHeader';
import { duplicateTableSteps } from '../utils/consts';

const DuplicateRecordsTableContainer = styled.div`
  height: 100%;
  overflow: scroll;
`;

const DuplicateRecordsTable = React.memo(function DuplicateRecordsTable({
  tableData,
  handleSelectedRow = () => {},
  handleRowClick = () => {},
  next = () => {},
  back = () => {},
  columnFor = duplicateTableSteps.leads,
  sfType = '1.0',
}) {
  if (!tableData.length) return <></>;
  const HeaderComponent =
    columnFor === duplicateTableSteps.leads ? (
      <DuplicateLeadsHeader />
    ) : (
      <DuplicateCustomerHeader />
    );
  const { rows, columns } = generateRowAndColumn(tableData, columnFor, sfType);

  return (
    <>
      {HeaderComponent}
      <DuplicateRecordsTableContainer>
        <Table
          header={columns}
          data={rows}
          sortColumnFunc={() => {}}
          showEditButton={false}
          showOnRowSelection={columnFor === duplicateTableSteps.leads}
          handleSelectedRow={handleSelectedRow}
          handleRowClick={handleRowClick}
        />
      </DuplicateRecordsTableContainer>
      <TableActionsContainer>
        <PrimaryButton width="75px" onClick={() => back()}>
          Back
        </PrimaryButton>

        <PrimaryButton width="75px" onClick={() => next()}>
          Next
        </PrimaryButton>
      </TableActionsContainer>
    </>
  );
});

DuplicateRecordsTable.propTypes = {
  next: PropTypes.func,
  /* eslint-disable react/forbid-prop-types */
  tableData: PropTypes.arrayOf(PropTypes.any).isRequired,
  back: PropTypes.func,
  columnFor: PropTypes.string,
  handleSelectedRow: PropTypes.func,
  handleRowClick: PropTypes.func,
  sfType: PropTypes.string,
};

export default DuplicateRecordsTable;
