import React from 'react';
import Swal from 'sweetalert2/dist/sweetalert2';
import { parseJSXContentForSwalPopup } from '@utils/functions';
import { ScriptText } from '../LeadIntakeScript/styles';

const fillListWithSameValues = (numUnitsSchedulingToday, fieldValue) =>
  new Array(Number(numUnitsSchedulingToday)).fill(fieldValue);

const resetValuesBasedOnSameUnitInfo = (values) => {
  if (values.numUnitsSchedulingToday === '5+') {
    return Swal.fire({
      title: 'Customer Not Eligible',
      html: parseJSXContentForSwalPopup(
        <ScriptText>
          Unfortunately, the MassSave program does not allow HomeWorks to perform assessments at
          homes that are part of a 5 or more unit multi-family. The good news is that you should
          still be able to get an assessment through MassSave directly. Would you like the number
          for that? (You can reach them directly at ************).
        </ScriptText>,
      ),
      confirmButtonText: 'OK',
      icon: 'info',
    });
  }

  return {
    heatingFuel: fillListWithSameValues(values.numUnitsSchedulingToday, values.heatingFuel[0]),
    electricProvider: fillListWithSameValues(
      values.numUnitsSchedulingToday,
      values.electricProvider[0],
    ),
    electricAccountNumber: fillListWithSameValues(
      values.numUnitsSchedulingToday,
      values.electricAccountNumber[0],
    ),
    hasAlternateElectricBillName: fillListWithSameValues(
      values.numUnitsSchedulingToday,
      values.hasAlternateElectricBillName[0],
    ),
    gasProvider: fillListWithSameValues(values.numUnitsSchedulingToday, values.gasProvider[0]),
    gasAccountNumber: fillListWithSameValues(
      values.numUnitsSchedulingToday,
      values.gasAccountNumber[0],
    ),
    electricBillName: fillListWithSameValues(
      values.numUnitsSchedulingToday,
      values.electricBillName[0],
    ),
    gasBillName: fillListWithSameValues(values.numUnitsSchedulingToday, values.gasBillName[0]),
  };
};

export { resetValuesBasedOnSameUnitInfo, fillListWithSameValues };
