import React, { useMemo } from 'react';
import DataIntakeFormPage from '@components/DataIntakeForm/components/DataIntakeFormPage';
import { cloneDeep } from 'lodash';

import { workReceiptFields, workReceiptMap } from '../FormSchema/fieldsMap';
import { workReceiptTabs as wrIntakeTabs } from '../consts';
import { VentingCalculations } from '../VentingCalculations/VentingCalculations';
import { BeyondMassSave } from '../BeyondMassSave/index';

const areasToCheck = ['Attic Floor', 'Attic Slope', 'Attic Wall', 'Kneewall'];

// Tabs that should be removed when the form is reset or during initial mount.
// These tabs are only relevant after deals are fully loaded with some diff conditions and should not be visible by default.
const tabsToRemoveOnReset = [
  'goodSpecBadSpec',
  'dhwQuoting',
  'returnVisitResulting',
  'postHea',
  'additionalProjectInformation',
  'flooringRemovalIncentive',
  'icRatedLightLocation',
  'ventingCalculation',
];

const containsAnyArea = (areas, areasToCheck) => {
  return areasToCheck.some((area) => areas?.includes(area));
};

export const useTabsAndFieldsForWR = (
  handleFieldChange,
  {
    resultingSingleOrMulti = 'Single Family',
    isDealLoaded,
    hasReturnVisit,
    wasDhwQuoted,
    isBadSpec,
    activeTab,
    areasOnWorkscope,
    docRepoStatus,
  },
) => {
  // TODO Add comments to improve readability of this Custom Hook, After release

  const tabs = useMemo(() => {
    let currentTabs = wrIntakeTabs;
    if (isDealLoaded) {
      // Add tab based on conditions
      if (docRepoStatus > 0 && !currentTabs.some((tab) => tab.name === 'postHea')) {
        currentTabs.push({ name: 'postHea', title: 'Post HEA' });
      }

      if (
        docRepoStatus > 1 &&
        !currentTabs.some((tab) => tab.name === 'additionalProjectInformation')
      ) {
        currentTabs.push({
          name: 'additionalProjectInformation',
          title: 'Additional Project Information',
        });
      }

      if (containsAnyArea(areasOnWorkscope, areasToCheck)) {
        if (!currentTabs.some((tab) => tab.name === 'flooringRemovalIncentive')) {
          currentTabs.push(
            { name: 'flooringRemovalIncentive', title: 'Flooring Removal Incentive' },
            { name: 'icRatedLightLocation', title: 'IC Rated Light Location' },
            { name: 'ventingCalculation', title: 'Venting Calculation' },
          );
        }
      } else {
        currentTabs = currentTabs.filter(
          (tab) =>
            !['flooringRemovalIncentive', 'icRatedLightLocation', 'ventingCalculation'].includes(
              tab.name,
            ),
        );
      }

      if (hasReturnVisit && !currentTabs.some((tab) => tab.name === 'returnVisitResulting')) {
        currentTabs.push({ name: 'returnVisitResulting', title: 'Return Visit Resulting' });
      }

      if (wasDhwQuoted === 'Yes' && !currentTabs.some((tab) => tab.name === 'dhwQuoting')) {
        currentTabs.push({ name: 'dhwQuoting', title: 'DHW Quoting' });
      } else {
        // Ensure 'dhwQuoting' is always the second last item
        const dhwQuotingIndex = currentTabs.findIndex((tab) => tab.name === 'dhwQuoting');
        if (dhwQuotingIndex !== -1 && dhwQuotingIndex !== currentTabs.length - 2) {
          const [dhwQuotingTab] = currentTabs.splice(dhwQuotingIndex, 1);
          currentTabs.splice(currentTabs.length - 1, 0, dhwQuotingTab);
        }
      }

      if (isBadSpec && !currentTabs.some((tab) => tab.name === 'goodSpecBadSpec')) {
        currentTabs.push({ name: 'goodSpecBadSpec', title: 'Specs' });
      }
    }
    // On initial mount or when the form is reset, we need to remove certain tabs.
    // If deals are not yet loaded and the form is in an online work receipt state (isOnlineWR),
    // we check for any tabs that should be hidden by default.
    // The currentTabs array is filtered to remove any tabs listed in tabsToRemoveOnReset,
    // ensuring these tabs only appear when relevant conditions are met later in the process.
    if (!isDealLoaded && currentTabs.some((tab) => tabsToRemoveOnReset.includes(tab.name))) {
      currentTabs = currentTabs.filter((tab) => !tabsToRemoveOnReset.includes(tab.name));
    }

    return currentTabs;
  }, [
    resultingSingleOrMulti,
    isDealLoaded,
    docRepoStatus,
    areasOnWorkscope,
    areasToCheck,
    hasReturnVisit,
    wasDhwQuoted,
    isBadSpec,
  ]);

  // Memoize the mapping of workReceiptTabs
  const workReceiptTabs = useMemo(
    () =>
      tabs.map(({ name, title }) => {
        const map = cloneDeep(workReceiptMap[name]);

        if (name === 'beyondMassSave') {
          return {
            name,
            title,
            component: <BeyondMassSave handleFieldChange={handleFieldChange} />,
          };
        }

        if (name === 'ventingCalculation') {
          return {
            name,
            title,
            component: <VentingCalculations handleFieldChange={handleFieldChange} />,
          };
        }
        return {
          name,
          title,
          component: (
            <DataIntakeFormPage
              map={map}
              readOnlyForReview={activeTab === 'additionalProjectInformation'}
            />
          ),
        };
      }),
    [tabs, workReceiptMap, handleFieldChange, activeTab],
  );
  return {
    workReceiptTabs,
    workReceiptMap,
    workReceiptFields,
  };
};
