import React, { useState, useCallback, memo } from 'react';
import PropTypes from 'prop-types';
import { useRecoilValue } from 'recoil';
import styled from 'styled-components';
import { activeTabState } from '@recoil/dataIntakeForm';
import { ArrowBarLeft } from '@styled-icons/bootstrap/ArrowBarLeft';
import { ArrowBarRight } from '@styled-icons/bootstrap/ArrowBarRight';
import { PrimaryButton } from '@components/global/Buttons';
import { CopyCustomerInformation } from './CopyCustomerInformation';
import { PreliminaryScript } from './PreliminaryScript';
import { CustomerInfoScript } from './CustomerInfoScript';
import { ReviewSectionScript } from './ReviewSectionScript';
import { SourceInfoScript } from './SourceInfoScript';
import { HomeHeatingSystemScript } from './HomeHeatingSystemScript';
import PreliminaryScriptCT from './CT/PreliminaryScriptCT';
import CustomerInfoScriptCT from './CT/CustomerInfoScriptCT';
import SourceInfoScriptCT from './CT/SourceInfoScriptCT';
import ReviewSectionScriptCT from './CT/ReviewSectionScriptCT';
import { ScriptTitle, ScriptText } from './styles';

const ScriptContainer = styled.div`
  position: absolute;
  width: ${({ openSidebar }) => (openSidebar ? '600px' : '40px')};
  height: 600px;
  right: 40px;
  bottom: calc(100% - 650px);
  border-radius: 10px;
  transition: width 0.5s;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  visibility: ${({ openSidebar }) => (openSidebar ? 'visible' : 'hidden')};
  background-color: ${({ theme }) => theme.secondary[100]};
  overflow-y: auto;
`;
const ScriptInnerContainer = styled.div`
  height: inherit;
  display: flex;
  position: relative;
  z-index: 23;
`;
const ArrowLeftIcon = styled(ArrowBarLeft)`
  visibility: visible;
  color: ${({ theme }) => theme.colors.eventGreen};
  width: 40px;
  height: 40px;
  transition: none;
`;
const ArrowRightIcon = styled(ArrowBarRight)`
  visibility: visible;
  color: ${({ theme }) => theme.colors.eventGreen};
  width: 40px;
  height: 40px;
  transition: none;
`;
const Script = styled.div`
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  gap: 8px;
  padding: 24px 24px 24px 22px;
  position: relative;
`;
const ActionsContainer = styled.span`
  display: flex;
  height: inherit;
  align-items: center;
`;
const ScriptAndCustomerInfoContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 8px;
`;
const ScriptAndCustomerInfoAction = styled.div`
  position: absolute;
  top: 24px;
  right: 24px;
`;

const Actions = memo(function Actions({ openSideScript = false, handleIconClick = () => {} }) {
  return (
    <ActionsContainer>
      {openSideScript ? (
        <ArrowRightIcon onClick={handleIconClick} />
      ) : (
        <ArrowLeftIcon onClick={handleIconClick} />
      )}
    </ActionsContainer>
  );
});

const scriptComponentMap = {
  MA: {
    preliminaryQuestions: <PreliminaryScript />,
    customerInfo: <CustomerInfoScript />,
    sourceInfo: <SourceInfoScript />,
    homeHeatingSystem: <HomeHeatingSystemScript />,
    review: <ReviewSectionScript />,
  },
  CT: {
    preliminaryQuestions: <PreliminaryScriptCT />,
    customerInfo: <CustomerInfoScriptCT />,
    sourceInfo: <SourceInfoScriptCT />,
    review: <ReviewSectionScriptCT />,
  },
};

export const LeadIntakeScript = ({ formValues = {}, state = 'MA' }) => {
  const [openSideScript, setOpenSideScript] = useState(false);
  const [showCustomerInfo, setShowCustomerInfo] = useState(false);
  const activeTab = useRecoilValue(activeTabState);
  const { electricProvider, gasProvider } = formValues;
  const handleIconClick = useCallback(() => setOpenSideScript(!openSideScript), [openSideScript]);

  const RenderScript = useCallback(() => {
    let component = scriptComponentMap?.[state]?.[activeTab];
    if (!component)
      component = (
        <ScriptText>
          Error occured trying to render script with name: {activeTab} and state: {state}
        </ScriptText>
      );
    return component;
  }, [activeTab]);

  const showCopyScriptButton = electricProvider?.[0] || gasProvider?.[0];
  const showScript = !showCustomerInfo;

  return (
    <ScriptContainer openSidebar={openSideScript}>
      <ScriptInnerContainer>
        <Actions openSideScript={openSideScript} handleIconClick={handleIconClick} />
        <Script>
          <ScriptAndCustomerInfoContainer>
            {showScript && <ScriptTitle>Script</ScriptTitle>}

            {openSideScript && showScript && <RenderScript />}
            {openSideScript && showCustomerInfo && <CopyCustomerInformation />}

            {showCopyScriptButton && (
              <ScriptAndCustomerInfoAction>
                <PrimaryButton onClick={() => setShowCustomerInfo(!showCustomerInfo)}>
                  {showCustomerInfo ? 'View Script Info' : 'View Customer Info'}
                </PrimaryButton>
              </ScriptAndCustomerInfoAction>
            )}
          </ScriptAndCustomerInfoContainer>
        </Script>
      </ScriptInnerContainer>
    </ScriptContainer>
  );
};

Actions.propTypes = {
  openSideScript: PropTypes.bool,
  handleIconClick: PropTypes.func,
};

LeadIntakeScript.propTypes = {
  formValues: PropTypes.shape({
    electricProvider: PropTypes.arrayOf(PropTypes.string),
    gasProvider: PropTypes.arrayOf(PropTypes.string),
  }),
  state: PropTypes.string,
};
