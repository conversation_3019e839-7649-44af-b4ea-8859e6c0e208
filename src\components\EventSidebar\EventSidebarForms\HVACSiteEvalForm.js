import React, { Suspense, useEffect } from 'react';
import PropTypes from 'prop-types';

import moment from 'moment';
import { useRecoilState, useRecoilValue } from 'recoil';

import { selectedEventState } from '@recoil/eventSidebar';
import { allAgentsFormOptionsState } from '@recoil/agents';

import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import {
  Row,
  Col,
  FormInput,
  handleFormFieldChange,
  FormTextBox,
  FormInfoField,
  FormInfo,
  FormStartEndDateTimePickers,
  FormSelect,
} from '@components/global/Form';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarHeader, {
  HeaderLabel,
  HeaderTitle,
} from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';

import { isAuthorized } from '@utils/AuthUtils';
import {
  openPhoneCall,
  openEmailClient,
  openNewTabWithUrl,
  getSalesforceUrl,
  displayPhoneNumber,
  getGoogleMapsDirectionsUrl,
  getEventDuration,
} from '@utils/functions';

import { SalesforceManager } from '@utils/APIManager';

const HVACSiteEvalForm = ({ handleCancelClick, handleSaveClick }) => {
  const [event, setEvent] = useRecoilState(selectedEventState);
  const agents = useRecoilValue(allAgentsFormOptionsState);

  const {
    id,
    sfIds,
    sfIds: { opportunityId },
    customerName,
    startTime,
    endTime,
    numUnit,
    type,
    eventTypeName,
    address,
    phoneNumber,
    email,
    notes,
    oid,
    oids,
    date,
    scheduledBy,
    scheduledDate,
    startEndTimes = [], // [{start: '', end: ''}, {start: '', end: ''}]
  } = event;

  const eventTypes = [
    { key: 'Site Eval', value: '000433' },
    { key: 'Final Site Eval', value: '000434' },
    { key: 'Custom Block', value: '999999' },
  ];

  const isCreate = !id;
  const createEventTypes =
    type?.slice(0, 4) === '0004'
      ? [{ key: '', value: type }]
      : [{ key: 'Custom Block', value: '999999' }];

  useEffect(() => {
    if (startEndTimes.length > 0 && isCreateEvent) {
      const updatedStartEndTime = [
        {
          start: startEndTimes[0].start,
          end: moment(startEndTimes[0].start).add(moment.duration('01:00:00')),
        },
      ];
      handleFormFieldChange(
        { target: { name: 'startEndTimes', value: updatedStartEndTime } },
        event,
        setEvent,
      );
    }
  }, []);

  useEffect(() => {
    if (!date || !isCreate || type) return; // If no type for create event, push to the returnVisit form

    if (isCreate) setEvent({ ...event, type: createEventTypes[0].value });
  }, [date, createEventTypes, type, isCreate, setEvent, event]);

  if (!type) return null;
  const eventDuration = getEventDuration(startTime, endTime);

  const canEdit = isAuthorized('Scheduler', 'All');

  const isCreateEvent = !id;

  const salesForceUrl = getSalesforceUrl();

  const agent = agents.find((agent) => [oid, oids[0]].includes(agent?.value));

  const agentName = agent?.key;

  const handleOpportunityIdChange = async (e) => {
    handleFieldChange(e);
    const { value: opportunityId } = e.target;
    if (![15, 18].includes(opportunityId.length)) {
      setEvent({ ...event, sfIds: { opportunityId } });
      return false;
    }
    return syncFromSalesforce(opportunityId);
  };

  const syncFromSalesforce = async (opportunityId) => {
    const salesforceInfo = await SalesforceManager.getSiteEvalDetails(opportunityId);
    const { sfIds } = salesforceInfo;
    sfIds.opportunityId = opportunityId;
    setEvent({ ...event, ...salesforceInfo, sfIds, jobLength: 1 });
  };

  const handleFieldChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col size={2}>
              <HeaderLabel>Customer Name:</HeaderLabel>
              <HeaderTitle>{customerName}</HeaderTitle>
              <HeaderLabel>{`Date: ${moment(date).format('M/D/YYYY')}`}</HeaderLabel>
              <HeaderLabel>Duration: {eventDuration} hours</HeaderLabel>
            </Col>
            <Col size={1} right>
              <HeaderLabel>Number of Units: {numUnit}</HeaderLabel>
              <HeaderLabel>
                {`Time: ${moment(startTime, 'HH:mm:ss').format('h:mm')} - ${moment(
                  endTime,
                  'HH:mm:ss',
                ).format('h:mm')}`}
              </HeaderLabel>
            </Col>
          </Row>
        </EventSidebarHeader>
        <EventSidebarBody>
          <Row>
            <Col>
              <FormInput
                required={isCreateEvent}
                readOnly={!isCreateEvent}
                title="opportunity id"
                placeholder="Enter Opportunity ID"
                name="sfIds.opportunityId"
                value={opportunityId}
                onChange={handleOpportunityIdChange}
                onClick={
                  !isCreateEvent
                    ? () => openNewTabWithUrl(`${salesForceUrl}${opportunityId}`)
                    : () => {}
                }
              />
              {isCreateEvent ? (
                <>
                  <FormSelect
                    title="job type"
                    name="type"
                    value={type}
                    options={eventTypes}
                    onChange={handleFieldChange}
                  />
                </>
              ) : (
                <FormInput
                  readOnly
                  name="eventTypeName"
                  value={eventTypeName}
                  title="Event Type"
                  placeholder="Event Type"
                />
              )}

              <FormInput
                readOnly
                name="address"
                value={address?.displayAddress}
                title="Address"
                onClick={() =>
                  openNewTabWithUrl(getGoogleMapsDirectionsUrl(address?.displayAddress))
                }
                placeholder="Address"
              />
              <FormInput
                readOnly
                name="agentName"
                value={agentName}
                title="Crew Name"
                placeholder="Crew Name"
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormInput
                readOnly
                name="phoneNumber"
                value={displayPhoneNumber(phoneNumber)}
                title="Phone Number"
                onClick={() => openPhoneCall(phoneNumber)}
                placeholder="Phone Number"
              />
              <FormInput
                readOnly
                name="sfIds.accountId"
                value={`${sfIds.accountId}`}
                title="SF Account Id"
                onClick={() => openNewTabWithUrl(`${salesForceUrl}${sfIds.accountId}`)}
                placeholder="SF Account Id"
              />
              <FormTextBox
                name="notes.officeNotes"
                value={notes.officeNotes}
                title="Office Notes"
                onChange={handleFieldChange}
                disabled={canEdit}
                placeholder="Office Notes"
              />

              {!isCreateEvent && (
                <FormInfo>
                  <FormInfoField title="Scheduled By :" body={scheduledBy} />
                  <FormInfoField
                    title="Scheduled On :"
                    body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
                  />
                </FormInfo>
              )}
            </Col>
            <Col>
              <FormInput
                readOnly
                name="email"
                value={`${email}`}
                title="Email"
                onClick={() => openEmailClient(email)}
                placeholder="Email"
              />
              {!isCreateEvent && (
                <FormInput
                  readOnly
                  name="sfIds.workVisitId"
                  value={`${sfIds.workVisitId}`}
                  title="SF Work Visit Id"
                  onClick={() => openNewTabWithUrl(`${salesForceUrl}${sfIds.workVisitId}`)}
                  placeholder="SF Work Visit Id"
                />
              )}
              <FormStartEndDateTimePickers allowDateSelect={false} startEndTimes={startEndTimes} />
            </Col>
          </Row>
        </EventSidebarBody>
        {isAuthorized('Scheduler', 'HVAC-Install') && canEdit && (
          <EventSidebarFooter>
            <PrimaryButton right onClick={() => handleSaveClick()}>
              Save
            </PrimaryButton>
            {!isCreateEvent && (
              <>
                <CancelButton left onClick={() => handleCancelClick()}>
                  Cancel Event
                </CancelButton>
              </>
            )}
          </EventSidebarFooter>
        )}
      </Suspense>
    </SidebarForm>
  );
};

HVACSiteEvalForm.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default HVACSiteEvalForm;
