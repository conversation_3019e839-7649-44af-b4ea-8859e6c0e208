import styled from 'styled-components';
import React from 'react';
import PropTypes from 'prop-types';

const FormInfoContainer = styled.div`
  height: 96px;
  width: 100%;
  background: ${({ theme }) => theme.colors.lightYellow};
  box-sizing: border-box;
  border-radius: 4px;
  padding: 1%;
`;

const FormInfo = ({ children }) => {
  return <FormInfoContainer>{children}</FormInfoContainer>;
};

FormInfo.propTypes = {
  children: PropTypes.node.isRequired,
};

export default FormInfo;
