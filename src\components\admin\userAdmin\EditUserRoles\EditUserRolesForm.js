import React, { useState, useEffect, useCallback } from 'react';
import { useRecoilValue, useSetRecoilState } from 'recoil';

import { UtilityManager, UsersManager } from '@utils/APIManager';

import { selectedUserRoleState, selectedUserState } from '@recoil/admin/users';
import { refreshAdminListState } from '@recoil/admin';

import ScreenPartitionView from '@components/global/ScreenPartitionView/ScreenPartitionView';
import RolesList from './RolesList';
import RolesListItemDetail from './RolesListItemDetail';

const EditUserRolesForm = () => {
  const refreshAdminList = useRecoilValue(refreshAdminListState);
  const selectedUser = useRecoilValue(selectedUserState);
  const setSelectedRole = useSetRecoilState(selectedUserRoleState);

  const [allRoles, setAllRoles] = useState([]);
  const [userRoles, setUserRoles] = useState([]);

  const { oid } = selectedUser;

  const getRoles = useCallback(async () => {
    const allRoles = await UtilityManager.getAllRoles();
    setAllRoles(allRoles);
    const userRoles = await UsersManager.getUserRoles(oid);
    userRoles.forEach((role, index) => {
      userRoles[index].roleAssigned = true;
      return role;
    });

    setUserRoles(userRoles);
    setSelectedRole(userRoles[0] || {});

    // added refreshAdminList dependency to cause roles to rerender when added or removed
    // eslint-disable-next-line
  }, [oid, setSelectedRole, refreshAdminList]);

  useEffect(() => {
    getRoles();
  }, [getRoles]);

  return (
    <ScreenPartitionView ratio={[3, 7]}>
      <RolesList userRoles={userRoles} />
      <RolesListItemDetail userRoles={userRoles} allRoles={allRoles} />
    </ScreenPartitionView>
  );
};

export default EditUserRolesForm;
