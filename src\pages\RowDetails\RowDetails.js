import React, { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import { Link, withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import utilityService from '@homeworksenergy/utility-service';

import { getUserCookie, hasRole, getAuthorizedDepartments } from '@utils/AuthUtils';
import { calendarTypeAtom } from '@recoil/app';
import { UsersManager } from '@utils/APIManager';
import { Clickable } from '@components/global';
import MonthlyCalendar from '@components/Calendar/MonthlyCalendar/MonthlyCalendar';
import { SearchVisitsButton } from '@components/global/InstallationScheduledCalendar/components/';
import BackButton from './BackButton';

const StyledRowDetails = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const RowDetailsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  margin-bottom: 18px;
`;

// TODO: We'll need to have this handle trucks and users eventually
const EditTruckButton = styled(Clickable)`
  padding: 6px 12px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.actionButton};
  color: ${({ theme }) => theme.secondary[100]};
`;

const EditButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 20%;
  padding: 10px 0;
`;

const RowDetails = (props) => {
  const {
    match: {
      params: { oid },
    },
    history,
  } = props;

  const [userInfo, setUserInfo] = useState({ displayName: '' });
  const [calendarType, setCalendarType] = useRecoilState(calendarTypeAtom);

  const { displayName } = userInfo;
  const canEditTruck = getAuthorizedDepartments().length > 0;

  useEffect(() => {
    const { oid: userOid, isManager } = getUserCookie();
    const { business: department } = utilityService.decodeEventType(calendarType);
    const isAgent = hasRole('Agent', department);
    if (!isManager && isAgent && oid !== userOid) {
      history.push('/');
      return;
    }
    const getUserInfo = async () => {
      const userInfo = await UsersManager.getUserInfo(oid);
      setUserInfo(userInfo);
    };
    getUserInfo();
  }, [oid, history, calendarType]);

  useEffect(() => {
    const getRoles = async (agentOid) => {
      let calendarType = '000500';
      const roles = await UsersManager.getUserRoles(agentOid);
      if (roles?.length) {
        // Using For Loop here to break once found
        for (let k = 0; k < roles.length; k++) {
          const { role, department, state } = roles[k];
          if (role === 'Agent' && department === 'HEA') {
            // state "01" = Connecticut, state "00" = Massachusetts
            if (state === '01') {
              calendarType = '010000';
            } else {
              calendarType = '000000';
            }
            break;
          }
          if (role === 'Agent' && department === 'HVAC-Sales') {
            calendarType = '000100';
            break;
          }
        }
      }
      setCalendarType(calendarType);
    };
    getRoles(oid);
  }, [oid, setCalendarType]);

  return (
    <StyledRowDetails>
      <RowDetailsHeader>
        <BackButton>{displayName}</BackButton>
        <EditButtonContainer>
          <SearchVisitsButton key="SearchVisitsButton" />
          <Link to={{ pathname: '/edit-agent-info', search: `?oid=${oid}` }}>
            {canEditTruck && <EditTruckButton>Edit Truck</EditTruckButton>}
          </Link>
        </EditButtonContainer>
      </RowDetailsHeader>
      <MonthlyCalendar userInfo={userInfo} />
    </StyledRowDetails>
  );
};

RowDetails.propTypes = {
  match: PropTypes.shape({
    params: PropTypes.shape({
      oid: PropTypes.string,
    }),
  }).isRequired,
  history: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
};

export default withRouter(RowDetails);
