import { useRef, useCallback, useEffect } from 'react';
import moment from 'moment';
import {
  createFontAwesomeMarker,
  createNumberedMarker,
  createAppointmentInfoContent,
  createHomeInfoContent,
  HOME_ICON_PATH,
} from '@components/Calendar/MapComponents/HEAMapViewUtils';

const useMapMarkers = ({
  googleMapRef,
  mapInitialized,
  appointments,
  hesAgents,
  hesAgentsWithHomes,
  activeFilters,
}) => {
  const markersRef = useRef([]);
  const routesRef = useRef([]);
  const geocodedCoordsRef = useRef(new Map());

  const clearMapElements = useCallback(() => {
    markersRef.current.forEach((marker) => marker.setMap(null));
    routesRef.current.forEach((route) => route.setMap(null));
    markersRef.current = [];
    routesRef.current = [];
    geocodedCoordsRef.current.clear();
  }, []);

  const geocodeAddress = (displayAddress) => {
    return new Promise((resolve) => {
      if (!displayAddress || !window.google) {
        resolve(null);
        return;
      }

      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ address: displayAddress }, (results, status) => {
        if (status === 'OK' && results[0]) {
          const {
            geometry: { location },
          } = results[0];
          resolve({
            lat: location.lat(),
            lng: location.lng(),
          });
        } else {
          resolve(null);
        }
      });
    });
  };

  const getTimeSlotNumber = (startTime) => {
    if (!startTime) return 1;

    const time = moment(startTime, ['HH:mm:ss', 'HH:mm', 'h:mm A', 'h:mm:ss A'], true);

    if (!time.isValid()) {
      return 1;
    }

    const hour = time.hour();

    if (hour >= 8 && hour < 10) return 1;
    if (hour >= 10 && hour < 12) return 2;
    if (hour >= 12 && hour < 14) return 3;
    if (hour >= 14 && hour < 16) return 4;
    if (hour >= 16 && hour < 18) return 5;
    if (hour >= 18 && hour < 20) return 6;

    return 1;
  };

  const createMarkers = useCallback(async () => {
    if (!googleMapRef.current || !appointments.length) return;

    clearMapElements();

    // Geocode all addresses first
    const appointmentCoordsMap = new Map();
    const appointmentsToGeocode = [];

    appointments.forEach((appointment) => {
      // Create unique key using hesOid, date, customerName, and startTime to ensure uniqueness
      const appointmentKey = `${appointment.hesOid}-${appointment.date}-${appointment.customerName}-${appointment.startTime}`;

      if (appointment.address?.latitude && appointment.address?.longitude) {
        appointmentCoordsMap.set(appointmentKey, {
          lat: parseFloat(appointment.address.latitude),
          lng: parseFloat(appointment.address.longitude),
        });
      } else if (appointment.address?.displayAddress || appointment.address?.address) {
        appointmentsToGeocode.push({ appointment, appointmentKey });
      }
    });

    // Geocode all in parallel
    if (appointmentsToGeocode.length > 0) {
      const geocodePromises = appointmentsToGeocode.map(({ appointment, appointmentKey }) => {
        const addressToGeocode =
          appointment.address?.displayAddress || appointment.address?.address;
        return geocodeAddress(addressToGeocode).then((coords) => {
          if (coords) {
            appointmentCoordsMap.set(appointmentKey, coords);
            geocodedCoordsRef.current.set(appointmentKey, coords);
          }
        });
      });
      await Promise.all(geocodePromises);
    }

    // Create routes for each agent
    const hesAgentMap = new Map();
    hesAgents.forEach((agent) => hesAgentMap.set(agent.oid, agent));

    const hesAgentHomeMap = new Map();
    hesAgentsWithHomes.forEach((agent) => hesAgentHomeMap.set(agent.oid, agent));

    // Group appointments by agent and date
    const hesRoutes = new Map();
    appointments.forEach((appointment) => {
      const { hesOid, date } = appointment;

      if (!hesOid || hesOid === 'undefined') return;
      if (activeFilters.size === 0 || !activeFilters.has(hesOid)) return;

      const normalizedDate = moment(
        date,
        ['YYYY-MM-DD', 'MM/DD/YYYY', 'YYYY-MM-DDTHH:mm:ss', 'YYYY-MM-DDTHH:mm:ss.SSSZ'],
        true,
      ).format('YYYY-MM-DD');
      const routeKey = `${hesOid}|${normalizedDate}`;
      if (!hesRoutes.has(routeKey)) {
        hesRoutes.set(routeKey, []);
      }
      hesRoutes.get(routeKey).push(appointment);
    });

    // Create polylines connecting appointments
    // eslint-disable-next-line no-restricted-syntax
    for (const [routeKey, dayAppointments] of hesRoutes.entries()) {
      const [hesOid] = routeKey.split('|');
      const agent = hesAgentMap.get(hesOid);
      const agentWithHome = hesAgentHomeMap.get(hesOid);

      if (!agent || dayAppointments.length === 0) {
        // eslint-disable-next-line no-continue
        continue;
      }

      // Sort by start time
      dayAppointments.sort((a, b) =>
        moment(a.startTime, 'HH:mm:ss').diff(moment(b.startTime, 'HH:mm:ss')),
      );

      const routeCoordinates = [];

      // Start from home
      if (agentWithHome?.homeCoordinates) {
        routeCoordinates.push(agentWithHome.homeCoordinates);
      }

      // Add all appointment locations
      // eslint-disable-next-line no-restricted-syntax
      for (const appointment of dayAppointments) {
        const appointmentKey = `${appointment.hesOid}-${appointment.date}-${appointment.customerName}-${appointment.startTime}`;
        const coords = appointmentCoordsMap.get(appointmentKey);

        if (coords) {
          routeCoordinates.push(coords);
        }
      }

      // Create polyline if we have at least 2 points
      if (routeCoordinates.length >= 2) {
        const polyline = new window.google.maps.Polyline({
          path: routeCoordinates,
          geodesic: true,
          strokeColor: agent.color,
          strokeOpacity: 1,
          strokeWeight: 4,
          map: googleMapRef.current,
        });

        routesRef.current.push(polyline);
      }
    }

    // Create markers
    const bounds = new window.google.maps.LatLngBounds();

    // Home markers
    // eslint-disable-next-line no-restricted-syntax
    for (const agent of hesAgents) {
      if (activeFilters.size === 0 || !activeFilters.has(agent.oid)) {
        // eslint-disable-next-line no-continue
        continue;
      }

      const agentWithHome = hesAgentHomeMap.get(agent.oid);
      if (agentWithHome?.homeCoordinates) {
        const homeIcon = createFontAwesomeMarker(HOME_ICON_PATH, agent.color);

        const homeMarker = new window.google.maps.Marker({
          position: agentWithHome.homeCoordinates,
          map: googleMapRef.current,
          title: `${agent.name} - Day Start`,
          icon: homeIcon,
        });

        const homeInfoWindow = new window.google.maps.InfoWindow({
          content: createHomeInfoContent(
            agent.name,
            agentWithHome.dayStartAddress?.displayAddress ||
              agentWithHome.dayStartAddress?.address ||
              agentWithHome.homeAddress,
            agent.color,
          ),
        });

        homeMarker.addListener('click', () => {
          homeInfoWindow.open(googleMapRef.current, homeMarker);
        });

        markersRef.current.push(homeMarker);
        bounds.extend(agentWithHome.homeCoordinates);
      }
    }

    // Appointment markers
    appointments.forEach((appointment) => {
      const { address, hesOid, hesName, startTime, endTime, customerName, date } = appointment;
      const appointmentKey = `${hesOid}-${date}-${customerName}-${startTime}`;

      if (activeFilters.size === 0 || !activeFilters.has(hesOid)) {
        return;
      }

      const coords = appointmentCoordsMap.get(appointmentKey);
      if (!coords) return;

      const agent = hesAgentMap.get(hesOid);
      if (!agent) return;

      const stopNumber = getTimeSlotNumber(startTime);
      const markerIcon = createNumberedMarker(stopNumber, agent.color || '#FF6B6B');

      const marker = new window.google.maps.Marker({
        position: coords,
        map: googleMapRef.current,
        title: `${hesName} - ${customerName}`,
        icon: markerIcon,
      });

      const infoWindow = new window.google.maps.InfoWindow({
        content: createAppointmentInfoContent(
          hesName,
          customerName,
          date,
          startTime,
          endTime,
          address,
          agent.color || '#FF6B6B',
        ),
      });

      marker.addListener('click', () => {
        infoWindow.open(googleMapRef.current, marker);
      });

      markersRef.current.push(marker);
      bounds.extend(coords);
    });

    // Fit map
    if (markersRef.current.length > 0) {
      googleMapRef.current.fitBounds(bounds);

      const listener = window.google.maps.event.addListener(googleMapRef.current, 'idle', () => {
        if (googleMapRef.current.getZoom() > 15) {
          googleMapRef.current.setZoom(15);
        }
        window.google.maps.event.removeListener(listener);
      });
    }
  }, [appointments, hesAgents, hesAgentsWithHomes, activeFilters, googleMapRef, clearMapElements]);

  useEffect(() => {
    if (mapInitialized) {
      if (appointments.length > 0 && activeFilters.size > 0) {
        createMarkers();
      } else {
        // Clear markers and routes when no appointments OR no active filters
        clearMapElements();
      }
    }
  }, [mapInitialized, appointments, activeFilters, createMarkers, clearMapElements]);

  return { createMarkers, clearMapElements };
};

export default useMapMarkers;
