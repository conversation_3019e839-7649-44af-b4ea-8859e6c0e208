import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput } from '@components/global/Form';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const IETeamUnresultedVisitsForm = ({ record = {} }) => {
  const {
    dealId,
    endDate,
    heaVisitResultDetail,
    subject,
    hesOriginalContractAmount,
    applianceVisitPerformed,
    siteid,
  } = record;

  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput readOnly name="siteid" value={siteid} title="Site ID" placeholder="" />
          <FormInput readOnly name="endDate" value={endDate} title="End Date" placeholder="" />
          <FormInput readOnly name="subject" value={subject} title="Subject" placeholder="" />
          <FormInput
            readOnly
            name="applianceVisitPerformed"
            value={applianceVisitPerformed}
            title="Appliance Visit Performed"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="heaVisitResultDetail"
            value={heaVisitResultDetail}
            title="Lead Vendor"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hesOriginalContractAmount"
            value={`${hesOriginalContractAmount}`}
            title="HES Original Contract Amount"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

IETeamUnresultedVisitsForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    endDate: PropTypes.string,
    heaVisitResultDetail: PropTypes.string,
    subject: PropTypes.string,
    hesOriginalContractAmount: PropTypes.string,
    applianceVisitPerformed: PropTypes.string,
    siteid: PropTypes.string,
  }),
};

export default IETeamUnresultedVisitsForm;
