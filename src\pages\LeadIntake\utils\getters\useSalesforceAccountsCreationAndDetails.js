import { SalesforceManager } from '@utils/APIManager/index';

export const useSalesforceAccountsCreationAndDetails = () => {
  const getDealsDetails = async ({ accountIdList = [], setFormValues }) => {
    const promises = accountIdList.map(async (accountId) => {
      const dealId = await SalesforceManager.getDealIdFromAccount(accountId);
      const dealInfo = await SalesforceManager.getObject('Deal__c', dealId);
      return dealInfo;
    });

    const dealInfos = await Promise.all(promises);
    const idsForMultiUnit = {};

    dealInfos.forEach((dealInfo, i) => {
      /* eslint-disable camelcase */
      const { Deal_ID__c, Account__c, Operations__c, CAP_Approval_Lead_Vendor__c } = dealInfo;
      let obj = {};
      if (i > 0) {
        const dealIdKey = `dealId${i + 1}`;
        const accountIdKey = `accountId${i + 1}`;
        const operationIdKey = `operationsId${i + 1}`;

        idsForMultiUnit[dealIdKey] = Deal_ID__c;
        idsForMultiUnit[accountIdKey] = Account__c;
        idsForMultiUnit[operationIdKey] = Operations__c;
        obj = { ...obj, ...idsForMultiUnit };
      } else {
        obj = { ...obj, dealId: Deal_ID__c, accountId: Account__c, operationsId: Operations__c };
      }
      setFormValues({ ...obj, capApprovalLeadVendor: CAP_Approval_Lead_Vendor__c });
      /* eslint-enable camelcase */
    });
  };
  return { getDealsDetails };
};
