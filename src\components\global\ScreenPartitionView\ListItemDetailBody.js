import styled from 'styled-components';
import React from 'react';
import PropTypes from 'prop-types';

const BodyContainer = styled.div`
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: 100%;
  width: 100%;
  background: ${({ theme }) => theme.secondary[100]};
`;

const Body = ({ children }) => {
  return <BodyContainer>{children}</BodyContainer>;
};

Body.propTypes = {
  children: PropTypes.node.isRequired,
};

export default Body;
