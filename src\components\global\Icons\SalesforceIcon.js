import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { Salesforce } from '@styled-icons/fa-brands/Salesforce';
import { UtilityManager } from '@utils/APIManager';

const SalesforceIconContainer = styled.div`
  position: relative;
  height: 100%;
  cursor: pointer;
`;

const IconStyled = styled(Salesforce)`
  height: 40px;
  color: ${({ theme }) => theme.colors.eventA};
  & :hover {
    color: ${({ theme }) => theme.secondary[700]};
  }
`;

const SalesforceIcon = ({ id, sfObject }) => {
  return (
    <SalesforceIconContainer onClick={() => UtilityManager.openSfPage(id, sfObject)}>
      <IconStyled />
    </SalesforceIconContainer>
  );
};

SalesforceIcon.propTypes = {
  id: PropTypes.string.isRequired,
  sfObject: PropTypes.string.isRequired,
};

export default SalesforceIcon;
