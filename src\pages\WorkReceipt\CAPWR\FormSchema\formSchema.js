import { formSchema } from '@pages/WorkReceipt/FormSchema/formSchema';
import {
  hesAgentFormOptionsSelector,
  hesHcsAgentFormAutoCompleteOptionsSelector,
} from '@recoil/agents';

import {
  yesNoOptions,
  accuracyIssuesOptions,
  electricityProviderOptions,
  workScopeOptions,
  heatingSystemTypeOptions,
  hesVisitResultDetailsOptions,
  paperworkSentEmailOptions,
  contractorChoiceOptions,
  ktRemediationContractorChoiceOptions,
  ktDetailOptions,
} from '../../consts';

const {
  asbestosInfo,
  cstFailure,
  electrical,
  moisture,
  crawlSpace,
  mold,
  other,
  structural,
  wallsWorkDisclosures,
  ventingCalculationFields,
  extraFields,
} = formSchema;

const customerAuditorInfoForCapWr = {
  customerName: {
    text: 'Customer Name',
    type: 'input',
    default: '',
    required: true,
  },
  customerAddress: {
    text: 'Address',
    type: 'address',
    default: '',
    required: true,
  },
  cityStateZip: {
    text: 'City, State, Zip',
    type: 'input',
    default: '',
    required: true,
  },
  customerPhone: {
    text: 'Phone',
    type: 'phone',
    default: '',
    required: true,
  },
  customerEmail: {
    text: 'Email',
    type: 'input',
    default: '',
    required: true,
  },
  siteId: {
    text: 'Site ID',
    type: 'input',
    default: '',
  },
  whatRegionIsThis: {
    text: 'Region',
    type: 'select',
    default: '',
    options: ['North Shore', 'South Shore', 'Metro West', 'Western MA', 'Cape Cod'],
  },
  auditDate: {
    text: 'Audit Date',
    type: 'date',
    default: undefined,
    useSyntheticEvent: true,
  },
  auditorName: {
    text: 'Auditor Name',
    type: 'input',
    default: '',
  },
  auditorEmail: {
    text: 'Auditor Email',
    type: 'input',
    default: '',
  },
  intakeIssue: {
    text: 'Intake Accuracy Issue',
    type: 'select',
    default: '',
    options: accuracyIssuesOptions,
  },
  isCondo: {
    text: 'Condo ?',
    type: 'select',
    default: '',
    options: yesNoOptions,
  },
  areasOnWorkscope: {
    default: [],
    text: 'Areas on Workscope (whole building)',
    type: 'multiselect',
    options: workScopeOptions,
  },
};

const propertyOwner = {
  ownerOccupancyType: {
    text: 'Owner Occupancy Type',
    type: 'select',
    default: '',
    options: [
      'Absentee Landlord',
      'Single Family',
      'Single Family RENTER',
      'Unit 1',
      'Unit 2',
      'Unit 3',
      'Unit 4',
    ],
  },
  propertyOwner: {
    text: 'Property Owner Full Name',
    type: 'input',
    default: '',
  },
  propertyOwnerAddress: {
    text: 'Property Owner Address',
    type: 'input',
    default: '',
  },
  propertyOwnerCityStateZip: {
    text: 'Property Owner City State Zip',
    type: 'input',
    default: '',
  },
  propertyOwnerTelephone: {
    text: 'Property Owner Telephone',
    type: 'phone',
    default: '',
  },
  propertyOwnerEmail: {
    text: 'Property Owner Email',
    type: 'input',
    default: '',
  },
  propertyOwnerSiteId: {
    text: 'Property Owner Site ID',
    type: 'input',
    default: '',
  },
  propertyOwnerElectricNumber: {
    text: 'Property Owner Electric Number',
    type: 'input',
    default: '',
  },
  propertyOwnerSingleFamilyRenter: {
    text: 'Single Family Renter',
    type: 'select',
    default: '',
    options: yesNoOptions,
  },
};

const hvacInfoCapWr = {
  ...formSchema.hvacInfo,
  areYouRecommendingGasSpaceHeaterReplacement: {
    text: 'Recommending a Whole Home ASHP w/ System Removal?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  areYouRecommendingForOilPropane: {
    text: 'Recommending ASHP for Oil/Propane? (Partial or Whole)"',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  areYouRecommendingGasFurnaceThatHasAc: {
    text: 'Are you recommending a gas furnace that has existing AC?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  gasHeatPumpRecs: {
    text: 'Gas heat pump recs?',
    type: 'select',
    options: ['Yes', 'No'],
    default: 'No',
  },
};

const capRoadBlocksInfo = {
  disclosureWallsTempAccess: {
    text: 'Temp Access',
    type: 'select',
    default: '',
    options: yesNoOptions,
    required: true,
  },
};

export const incomeEligibleProjectInfo = {
  yearHouseBuilt: {
    text: 'Year House Built',
    type: 'input',
    default: '',
  },
  buildingType: {
    text: 'Building Type',
    type: 'select',
    options: [
      'Single family detached',
      'Five +',
      'Two-family',
      'Three-family',
      'Four-family',
      'Condo',
      'Mobile Home',
    ],
    default: '',
  },
  finishedSqFt: {
    text: 'Finished SqFt',
    type: 'input',
    default: '',
  },
  numberOfUnitsPerformedToday: {
    text: '# Of Units Performed Today',
    type: 'input',
    default: '',
  },
  conditionedStories: {
    text: 'Conditioned Stories',
    type: 'select',
    options: ['1', '1.5', '2', '3', '4'],
    default: '',
  },
  sidingMaterial: {
    text: 'Siding Material',
    type: 'select',
    options: [
      'Aluminium',
      'Board & Batten',
      'Brick',
      'Cement Board',
      'Concrete',
      'Double-nailed Asbestos',
      'Single-nailed Asbestos',
      'Stone',
      'Stucco',
      'Vinyl',
      'Wood Clapboards',
      'Wood Shingles',
    ],
    default: '',
  },
  basementType: {
    text: 'Basement Type',
    type: 'select',
    options: [
      'Conditioned/Unfinished',
      'Conditioned/Finished',
      'Unconditioned',
      'Unintentionall conditioned/Unfinished',
      'Unintentionall conditioned/Finished',
      'Semi conditioned//Unfinished',
      'Semi conditioned/Finished',
      'Slab',
    ],
    default: '',
  },
};

const bathFanInstallReplace = {
  numberOfBathroomsWindowsUnder30cfm: {
    text: '# of Bathrooms w/o Windows under 30cfm',
    type: 'input',
    default: '--None--',
  },
  numberOfBathroomsWindowsUnder50cfm: {
    text: '# of Bathrooms w/o Windows under 50cfm',
    type: 'input',
    default: '--None--',
  },
  numberOfBathroomsFans: {
    text: '# of Bathrooms w/o Fan',
    type: 'input',
    default: '--None--',
  },
};

const pestInfestationRoofLeak = {
  pestInfestation: {
    text: 'Pest Infestation',
    type: 'checkbox',
    default: false,
  },
  roofLeak: {
    text: 'Roof Leak',
    type: 'checkbox',
    default: false,
  },
};

const aif = {
  recessedLights: {
    text: 'Recessed Lights?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  recessedLightsLocation: {
    text: 'Recessed Lights Location',
    type: 'input',
    default: '',
    conditional: (value) => value?.recessedLights === 'Yes',
  },
  bathFans: {
    text: 'Bath Fan?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  kitchenFans: {
    text: 'Kitchen Fan?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  chimneys: {
    text: 'Chimneys?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  otherHeatProducersInAttic: {
    text: 'Other Heat Producers in Attic',
    type: 'input',
    default: '',
  },
  atticOnUnit: {
    text: 'Attic on Unit',
    type: 'select',
    default: '',
    options: ['Single Family', 'Unit 1', 'Unit 2', 'Unit 3', 'Unit 4', 'No Attic Spec'],
  },
  activeSoffitVents: {
    text: 'Active Soffit Vents?',
    type: 'select',
    default: '',
    options: yesNoOptions,
  },
  propsExisting: {
    text: 'Props Existing?',
    type: 'select',
    default: '',
    options: yesNoOptions,
  },
  lengthOfAttic: {
    text: 'Length of Attic(ft)',
    type: 'number',
    default: '',
  },
};

const ampIsm = {
  ampEligible: {
    text: 'AMP Eligible',
    type: 'select',
    options: ['Yes', 'No'],
    default: 'No',
  },
  ampPerformed: {
    text: 'AMP Performed?',
    type: 'select',
    options: ['Yes', 'No'],
    default: 'No',
  },
  capApprovalLeadVendor: {
    text: 'CAP Approval Lead Vendor?',
    type: 'select',
    options: [
      '',
      'ABCD',
      'Action',
      'BCAC',
      'CAPIC',
      'QCAP',
      'NSCAP',
      'SHI',
      'CFC',
      'CAPV',
      'CTI',
      'GLCAC',
      'HAC',
      'LEO',
      'Menotomy Weatherization',
      'MOC',
      'SMOC',
      'SSCAC',
      'SPCA',
      'WCAC',
      'ABCD-LVI',
      'Action-LVI',
    ],
    default: '',
    readOnly: true,
  },
  rentAmount: {
    text: 'Rent Amount',
    type: 'number',
    default: '0',
  },
  electricProviderPostAmp: {
    text: 'Electricity Provider',
    type: 'select',
    options: electricityProviderOptions,
    default: '',
  },
  gasProvider: {
    text: 'Gas Provider',
    type: 'select',
    options: ['Eversource', 'National Grid', 'EGMA', 'Municipal'],
    default: '',
  },
  electricAccountNumber: {
    text: 'Electric Account Number',
    type: 'input',
    default: '',
  },
  electricApplicationNumber: {
    text: 'Electric Application Number',
    type: 'input',
    default: '',
  },
};

const basInformationForCapWr = {
  steamSystem: {
    text: 'Steam System?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  heatingSystemType: {
    text: 'Heating System Type',
    type: 'select',
    default: '',
    options: heatingSystemTypeOptions,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
  houseAirConditioned: {
    text: 'Is House Air Conditioned?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
  howManyStoriesIsHouse: {
    text: 'How Many Stories is the House?',
    type: 'number',
    default: 1,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
  squareFootage: {
    text: 'Square Footage',
    type: 'number',
    default: 0,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
  heightPerStory: {
    text: 'Average Height per Story',
    type: 'number',
    default: 0,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
  estimatedHouseVolume: {
    text: 'Estimated Volume',
    type: 'number',
    default: 0,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
  correctedHouseVolume: {
    text: 'Calculate the total correct volume',
    type: 'number',
    default: 0,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
  numberOfOccupants: {
    text: 'Number of Occupants',
    type: 'number',
    default: 0,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
  numberOfBedrooms: {
    text: 'Number of Bedrooms',
    type: 'number',
    default: 0,
    conditional: (values) => values.steamSystem !== 'Yes',
  },
};

const resultingAmp = {
  applianceVisitPerformed: {
    text: 'Appliance Visit Performed',
    type: 'select',
    default: '',
    options: [
      '',
      'Performed',
      'Not Performed',
      'ISM Install Performed',
      'Not Eligible',
      'NGrid AMP Pause',
    ],
  },
  applianceVisitType: {
    text: 'Appliance Visit Type',
    type: 'select',
    default: '',
    options: [
      '',
      'InDemand Electric',
      'InDemand Electric & InDemand Gas',
      'InDemand Electric & Therm Only Install',
      'EAssist Entry',
      'EAssist Entry (w/o AMP Fee)',
      'Therm Only Install',
    ],
  },
};

const ampFail = {
  heaInvoicingProcess: {
    text: 'HEA Invoicing Process',
    type: 'select',
    default: '',
    options: [
      '',
      'Invoice Submitted',
      'Incorrect Payment Received',
      'HEA Payment Received in Full',
      'Invoice Rejected',
      'Unable to Invoice',
      'Need to Confirm Payment',
      'HEA Fail',
      'HEA QC Failed Fixed Pending Review',
      'Appliance Visit Locked',
      'AMP Locked 1st Attempt',
      'AMP Locked 2nd Attempt',
      'AMP Locked 3rd Attempt',
      'Unable to Invoice - Hancock Issue (Temp)',
    ],
  },
  heaQcFailNotes: {
    text: 'HEA QC Fail Notes',
    type: 'textarea',
    default: '',
  },
};

const icw = {
  icwBy: {
    text: 'ICW By',
    type: 'select',
    default: '',
    recoilOptions: hesAgentFormOptionsSelector,
  },
  icwFixedBy: {
    text: 'ICW Fixed By',
    type: 'select',
    default: '',
    recoilOptions: hesAgentFormOptionsSelector,
  },
  reasonForIncorrectlyCwByIam: {
    text: ' Reason for Incorrectly CW:',
    type: 'multiselect',
    options: [
      'LTA/Wavier Status Incorrect',
      'ARI Incomplete',
      'ARI Missing',
      'CIF Incomplete',
      'CIF Missing',
      'Contract Denied by LV',
      'Contract Incomplete',
      'Contract Missing',
      'CST Incomplete',
      'CST Missing',
      'Deposit Missing',
      'Disclosure Issue',
      'Disclosure Missing',
      'DSAV Incomplete',
      'DSAV Missing',
      'EM/E+/Energy Savvy Missing Auditor Inputs',
      'HL Incomplete',
      'HL Missing',
      'Incomplete -> not signed',
      'ISM Issue',
      'ISM Missing',
      'LPS Missing',
      'Missing All Starting Documents',
      'Miss Spec',
      'PAF Incomplete',
      'PAF Missing',
      'Proposal Term Incomplete',
      'Proposal Terms Missing',
      'PV Incomplete',
      'PV Missing',
      'PWBI Incomplete',
      'PWBI Missing',
      'Roadblock Issue',
      'Signed Permit App Missing',
      'WBI Incomplete',
      'WBI Missing',
      'Work Receipt Issue',
      'WR Missing',
    ],
    default: '',
  },
  incorrectlyCwNotes: {
    text: 'Incorrectly CW Notes',
    type: 'textarea',
    default: '',
  },
};

const resultingHvac = {
  visitResult: {
    text: 'Visit Result',
    type: 'select',
    options: [
      '1st Attempt',
      '2nd Attempt',
      '3rd Attempt',
      '4th Attempt',
      '5th Attempt',
      '6th Attempt',
      ' Dead',
      'Deferred',
      'Visit Scheduled',
      'Reschedule Visit',
      'Visit Performed',
      'Visit Canceled',
      'Open',
    ],
    default: '',
  },
  notesForOffice: {
    text: 'Notes for Office',
    type: 'textarea',
    default: '',
  },
  hcs: {
    text: 'HCS',
    type: 'autocomplete',
    recoilOptions: hesHcsAgentFormAutoCompleteOptionsSelector,
    default: '',
  },
  capQuoteStatus: {
    text: 'CAP Quote Status',
    type: 'select',
    default: '',
    options: ['Open', 'ICW', 'Submitted to HomeWorks', 'Partner Quote Needed'],
    conditionalOptions: (values) => {
      const allowedOptions = ['Open', 'ICW', 'Submitted to HomeWorks', 'Partner Quote Needed'];
      let currentValue;
      if (Array.isArray(values)) {
        currentValue = values[0]?.capQuoteStatus;
      } else {
        currentValue = values?.capQuoteStatus;
      }
      // If current value exists and is not in allowed list, add it to the options so it can be displayed
      if (currentValue && !allowedOptions.includes(currentValue)) {
        return [...allowedOptions, currentValue];
      }
      return allowedOptions;
    },
    conditionalDisable: (values) => {
      const allowedOptions = ['Open', 'ICW', 'Submitted to HomeWorks', 'Partner Quote Needed'];
      let currentValue;
      if (Array.isArray(values)) {
        currentValue = values[0]?.capQuoteStatus;
      } else {
        currentValue = values?.capQuoteStatus;
      }
      // Disable the picklist if the current value exists and is not in the allowed options
      return currentValue && !allowedOptions.includes(currentValue);
    },
  },
  capIcwReason: {
    type: 'select',
    text: 'CAP ICW Reason',
    default: '',
    options: [
      'Manual J Issue',
      'PV Issue',
      'PICS File Issue',
      'Design Issue',
      'Price Issue',
      'Incorrect Equip. Rec.',
      'WS Issue',
      'Disclosure Missing',
      'Wrong Documents Uploaded',
      'Other',
      'CAP – Price',
      'CAP – Design',
    ],
  },
  existingHvac: {
    text: 'Existing HVAC',
    type: 'select',
    default: '',
    options: [
      '',
      'Gas Boiler',
      'Gas Furnace',
      'Deliverable Fuel Boiler',
      'Deliverable Fuel Furnace',
      'Electric Strips',
      'DMS/CHP',
      'Gas Space Heater',
      'Other',
    ],
  },
  recommendedHvac: {
    text: 'Recommended HVAC',
    type: 'select',
    default: '',
    options: [
      '',
      'Whole Home: DMS',
      'Whole Home: CHP',
      'Whole Home: CHP & DMS',
      'Partial: DMS',
      'Partial HP due to Design',
      'Partial HP due to Elec. Barrier',
      'Partial HP due to Elec. Disp.',
      'Boiler',
      'Furnace',
      'Gas Space Heater',
      'DHW Only',
    ],
  },
  existingDhwForPostHvac: {
    text: 'Existing DHW',
    type: 'select',
    default: '',
    options: [
      '',
      'Gas Atmospheric',
      'Gas Indirect',
      'Gas Power Vent',
      'Gas Tankless',
      'Oil Atmospheric',
      'Oil Indirect',
      'Oil Tankless',
      'Propane Atmospheric',
      'Electric Resistance',
      'HPHWH',
    ],
  },
  recommendedDhw: {
    text: 'Recommended DHW',
    type: 'select',
    default: '',
    options: [
      '',
      'No Change',
      'HPHWH',
      'Electric Resistance',
      'Gas Power Vented Tank',
      'Gas Indirect',
      'Gas Combi',
      'Chimney Liner',
    ],
  },
  cstFailInfo: {
    text: 'CST Fail Info',
    type: 'select',
    default: '',
    options: [
      '',
      'Heating System Fail',
      'DHW Fail',
      'Heating System & DHW Fail',
      'No CST Fail',
      'Other',
    ],
  },
  electricalRecommended: {
    text: 'Electrical Recommended',
    type: 'select',
    default: '',
    options: [
      '',
      'No Change',
      'Sub Panel',
      'Service Upgrade',
      'Service Upgrade - Needs Review',
      'Panel Upgrade',
      'Multi Service Upgrade (total building)',
      'Meter Wire & Panel Upgrade',
    ],
  },
  hotLeadNoHeat: {
    text: 'Hot Lead (No Heat)?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  pdsNeeded: {
    text: 'PDS Needed?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  trenchingNeeded: {
    text: 'Trenching Needed?',
    type: 'select',
    default: 'No',
    options: [...yesNoOptions, 'Bypass'],
  },
  asbestosRemediationNeeded: {
    text: 'Asbestos Remediation Needed?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  asbestosLocation: {
    text: 'Asbestos Location',
    type: 'select',
    default: '',
    options: ['', 'Ductwork', 'Pipes Only', 'Pipes and Boiler'],
  },
  chimneyLinerNeeded: {
    text: 'Chimney Liner Needed?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  installContractPrice: {
    text: 'Install Contract Price',
    type: 'input',
    default: '',
  },
  financingMethod: {
    text: 'Financing Method',
    type: 'select',
    default: '',
    options: [
      '',
      'Credit/Debit/Personal Check',
      'HEAT Loan',
      'Income-Eligible',
      'Turnkey',
      'GoodLeap',
      'HEAT Loan + GoodLeap',
      'Tetra',
      'GoodLeap + Self-financing',
    ],
  },
  status: {
    text: 'Status',
    type: 'select',
    default: '',
    options: [
      'Contract Review',
      'Failed Sales QC - HCS',
      'Failed Sales QC - Other',
      'Site Evaluation',
      'Financing',
      'Conversion',
      'Installation',
      'Partial Complete',
      'Walk/In Recovery',
      'Invoicing',
      'CAP Invoicing Fail',
      'Collections',
      'Waiting for Rebate',
      'Complete',
      'Deferred',
      'Unresponsive',
      'Cancel/Save',
      'Canceled',
      'Backordered',
    ],
  },
  failedSalesQcNotes: {
    text: 'Failed Sales QC Notes',
    type: 'textarea',
    default: '',
  },
  seNeededNotes: {
    text: 'SE Needed Notes',
    type: 'textarea',
    default: '',
  },
  roadblockNotes: {
    text: 'RoadBlock Notes',
    type: 'textarea',
    default: '',
  },
};

const capPostHea = {
  preferredLanguage: {
    text: 'Preferred Language',
    type: 'select',
    options: ['English', 'Spanish', 'Portuguese', 'Chinese', 'Haitian Creole', 'Japanese'],
    default: '',
  },
  heaVisitResult: {
    text: 'HEA Visit Result',
    type: 'select',
    default: '',
    options: [
      '',
      'HEA Performed',
      'SHV Performed',
      'Audit Scheduled',
      'Reschedule Audit',
      'Audit Canceled',
    ],
  },
  heaVisitResultDetail: {
    text: 'HEA Visit Result Detail',
    type: 'select',
    default: '',
    options: hesVisitResultDetailsOptions,
  },
  closedWonHesEmp: {
    text: 'Closed Won HES',
    type: 'select',
    default: '',
    recoilOptions: hesAgentFormOptionsSelector,
  },
  ltaWavierStatus: {
    text: 'LTA/Wavier Status',
    type: 'select',
    default: '',
    options: ['', 'Received', 'Missing', 'Sent for Signature', 'Denied/Inactive'],
  },
  heaNotes: {
    text: 'HEA Notes',
    type: 'textarea',
    default: '',
  },
  notesForCrew: {
    text: 'Notes For Crew',
    type: 'textarea',
    default: '',
  },
  notesForIsr: {
    text: 'Notes for ISR',
    type: 'textarea',
    default: '',
  },
  customerObjections: {
    text: 'Customer Objections',
    type: 'multiselect',
    default: '',
    options: [
      'Talk to spouse',
      'Need time to think',
      'Cannot afford',
      'Selling home',
      'Customer says they are too old',
      'Bad experience with previous company/contractor',
      'Says can do the work themselves',
      'Concerned with disturbing roof/attic/walls',
      'No objections identified',
      'Other (Please explain in notes)',
    ],
  },
  customerConcernsPainPoints: {
    text: 'Customer Concerns Pain Points',
    type: 'multiselect',
    default: '',
    options: [
      'Comfort',
      'Savings/ROI',
      'Health and Safety',
      'Green Friendly',
      'Structural Concerns (ice dams)',
      'No Concerns Identified',
      'Other (Please explain in notes)',
    ],
  },
  occupantType: {
    text: 'Occupant Type?',
    type: 'select',
    default: '',
    options: ['Owner', 'Renter'],
  },
  singleOrMultiFamily: {
    text: 'Single Family or Multi Family',
    type: 'select',
    default: '',
    options: ['Single-Family', 'Multi-Family'],
  },
  unitsInBuilding: {
    text: 'Units In Building?',
    type: 'select',
    default: '',
    options: ['', '1', '2', '3', '4', '5+'],
  },
  allQualifiedUnitsPerformed: {
    text: 'All Qualified Units Performed?',
    type: 'select',
    default: '',
    options: [
      '',
      'No (Other Units Need Scheduling)',
      'Yes (All Units Performed)',
      'Yes (Other Unit(s) Not Qualified)',
    ],
  },
  landlordInvolved: {
    text: 'Landlord Involved?',
    type: 'select',
    default: '',
    options: ['', 'Yes', 'No', 'Undetermined'],
  },
  massSaveWorkAmtWeatherization: {
    text: 'Weatherization ($)',
    type: 'number',
    default: 0,
  },
  heatingFuelUtility: {
    text: 'Heating Fuel Utility',
    type: 'select',
    default: '',
    options: ['EVR gas', 'NGrid gas', 'Oil/propane/electric'],
  },
  nestInstalled: {
    text: 'Nest Installed?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
  },
  paperworkEmailSent: {
    text: 'Paperwork Email Sent?',
    type: 'select',
    default: '',
    options: paperworkSentEmailOptions,
  },

  interestedInWindows: {
    text: 'Interested in Windows',
    type: 'select',
    default: '',
    options: [
      '',
      'Very Interesed',
      'Interested',
      'Undecided',
      'Not Interested',
      'CAP Replacement Request',
      'CAP Sent to Partner',
      'CAP Quote Received/Submitted',
      'CAP Replacement Approved',
      'CAP Install Complete Invoice Submitted',
      'CAP Interested - Not Qualified',
      'CAP Install Complete Payment Received',
      'CAP Qualified - Inactive/Declined',
    ],
  },
  numberOfSinglePaneWindow: {
    text: '# Single Pane Windows',
    type: 'number',
    default: 0,
  },
  heaWindowsNotes: {
    text: 'HEA Windows Notes',
    type: 'textarea',
    default: '',
  },
  facilitatorFullName: {
    text: 'Facilitator Full Name',
    type: 'input',
    default: '',
  },
  facilitatorPhoneNumber: {
    text: 'Facilitator Phone Number',
    type: 'phone',
    default: '',
  },
  facilitatorEmail: {
    text: 'Facilitator Email',
    type: 'input',
    default: '',
  },
  collectedMoneyForInsulation: {
    text: 'Collected Money For Insulation',
    type: 'select',
    default: 'No',
    options: ['Yes', 'No- No Copay'],
    conditional: (value) => value.heaVisitResultDetail === 'Closed Won',
  },
  depositAmount: {
    text: 'Deposit Amount ($)',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
    conditional: (value) =>
      value.heaVisitResultDetail === 'Closed Won' &&
      ['Check collected for less than $150', 'Credit Card collected for less than $150'].includes(
        value.collectedMoneyForInsulationDetails,
      ),
  },
  preferredDowForInstall: {
    text: 'Preferred Dow For Install',
    type: 'multiselect',
    default: '',
    options: [
      'No Preference',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Last Minute Scheduling',
    ],
    conditional: (value) => value.heaVisitResultDetail === 'Closed Won',
  },
  qualifiedOutReason: {
    text: 'Qualified Out Reason',
    type: 'select',
    default: '',
    options: [
      '',
      '2012 or Newer',
      'Under Job Minimum',
      'All Areas Treated by Mass Save',
      'All Areas Treated with Spray Foam',
      'Low Headroom/Inaccessible',
      'House Style (Mobile Home)',
    ],
    conditional: (value) => value.heaVisitResultDetail === 'Qualified Out',
  },
  qualifiedOutDetail: {
    text: 'Qualified Out Details',
    type: 'textarea',
    default: '',
    conditional: (value) => value.heaVisitResultDetail === 'Qualified Out',
  },
  VermiculiteRemContractorChoiceC: {
    text: 'If any vermiculite, must specify contractor choice',
    type: 'select',
    default: '',
    options: contractorChoiceOptions,
    conditional: (value) => value.disclosureAsbestosVermiculite === 'Yes',
  },
  MoldRemediationContractorChoiceC: {
    text: 'If any mold, must specify contractor choice',
    type: 'select',
    default: 'No',
    options: contractorChoiceOptions,
    conditional: (value) => value.isThereMold === 'Yes',
  },
  KTDetail: {
    text: 'HES K&T Detail',
    type: 'select',
    default: '',
    options: ktDetailOptions,
    conditional: (value) => value.disclosureElecKnobAntTubeActive === 'Yes',
  },
  KTRemediationContractorChoiceC: {
    text: 'If any K&T, must specify contractor choice',
    type: 'select',
    default: '',
    options: ktRemediationContractorChoiceOptions,
    conditional: (value) => value.disclosureElecKnobAntTubeActive === 'Yes',
  },
};

const readOnlyFields = {
  installStartupDate: {
    text: 'Install Startup Date',
    type: 'input',
    default: undefined,
    useSyntheticEvent: true,
  },
  concierge: {
    text: 'Concierge',
    type: 'input',
    default: '',
  },
  projectManager: {
    text: 'Project Manager',
    type: 'input',
    default: '',
  },
  insideSalesAm: {
    text: 'Inside Sales AM',
    type: 'input',
    default: '',
  },
  jobStatus: {
    text: 'Job Status',
    type: 'input',
    default: '',
  },
  scheduledInsulationStartDate: {
    text: 'Scheduled Insulation Start Date',
    type: 'input',
    default: '',
  },
  insideSalesStage: {
    text: 'Inside Sales Stage',
    type: 'input',
    default: '',
  },
  ltaWavierStatusReadOnly: {
    text: 'LTA/Wavier',
    type: 'input',
    default: '',
  },
  dayPhone: {
    text: 'Day Phone',
    type: 'input',
    default: '',
  },
  email: {
    text: 'Day Phone',
    type: 'input',
    default: '',
  },
  barrierType: {
    text: 'Barrier Type',
    type: 'input',
    default: '',
  },
  remediationStage: {
    text: 'Remediation Stage',
    type: 'input',
    default: '',
  },
  insulationApprovalProcess: {
    text: 'Insulation Approval Process',
    type: 'input',
    default: '',
  },
  insulationInvoicingNotes: {
    text: 'Insulation Invoicing Notes',
    type: 'textarea',
    default: '',
  },
  insulationCallBackNotes: {
    text: 'Insulation Callback Notes',
    type: 'textarea',
    default: '',
  },
  issNotes: {
    text: 'ISS Notes',
    type: 'textarea',
    default: '',
  },
};

const heaSpecReadOnly = {
  atticSpec: {
    text: 'Attic Spec',
    type: 'input',
    default: '',
  },
  crawlSpaceSpec: {
    text: 'Crawlspace Spec',
    type: 'input',
    default: '',
  },
  kwSpec: {
    text: 'kwSpec',
    type: 'input',
    default: '',
  },
  miscVentSpec: {
    text: 'Miscellaneous/Ventilation Spec',
    type: 'input',
    default: '',
  },
  wallSpec: {
    text: 'Wall Spec',
    type: 'input',
    default: '',
  },
  anyPrewxSpec: {
    text: 'Any Pre-Wxs?',
    type: 'input',
    default: '',
  },
  garageSpec: {
    text: 'Garage Spec',
    type: 'input',
    default: '',
  },
  walkReasonForHes: {
    text: 'Walk Reason For HES',
    type: 'input',
    default: '',
  },
  walkApproved: {
    text: 'Walk Approved',
    type: 'input',
    default: '',
  },
  generalNotes: {
    text: 'General Notes',
    type: 'textarea',
    default: '',
  },
};

export const extraFieldsForCapWr = {
  hvacInstallContractId: {
    text: '',
    default: '',
  },
};

export const flatCapWRFormSchema = {
  ...customerAuditorInfoForCapWr,
  ...incomeEligibleProjectInfo,
  ...hvacInfoCapWr,
  ...capRoadBlocksInfo,
  ...asbestosInfo,
  ...cstFailure,
  ...electrical,
  ...moisture,
  ...crawlSpace,
  ...mold,
  ...other,
  ...structural,
  ...aif,
  ...ampIsm,
  ...pestInfestationRoofLeak,
  ...bathFanInstallReplace,
  ...wallsWorkDisclosures,
  ...ventingCalculationFields,
  ...basInformationForCapWr,
  ...capPostHea,
  ...resultingAmp,
  ...resultingHvac,
  ...propertyOwner,
  ...extraFields,
  ...extraFieldsForCapWr,
  ...ampFail,
  ...icw,
  ...readOnlyFields,
  ...heaSpecReadOnly,
};

export const capWRFormSchema = {
  customerAuditorInfoForCapWr,
  incomeEligibleProjectInfo,
  hvacInfoCapWr,
  capRoadBlocksInfo,
  asbestosInfo,
  cstFailure,
  electrical,
  moisture,
  crawlSpace,
  mold,
  other,
  bathFanInstallReplace,
  structural,
  pestInfestationRoofLeak,
  wallsWorkDisclosures,
  ventingCalculationFields,
  basInformationForCapWr,
  aif,
  ampIsm,
  ampFail,
  icw,
  capPostHea,
  resultingAmp,
  resultingHvac,
  propertyOwner,
  extraFields,
  extraFieldsForCapWr,
  readOnlyFields,
  heaSpecReadOnly,
};

export const capRoadBlocksMap = {
  ...capRoadBlocksInfo,
  ...asbestosInfo,
  ...cstFailure,
  ...electrical,
  ...moisture,
  ...crawlSpace,
  ...mold,
  ...other,
  ...bathFanInstallReplace,
  ...structural,
  ...pestInfestationRoofLeak,
  ...wallsWorkDisclosures,
};
