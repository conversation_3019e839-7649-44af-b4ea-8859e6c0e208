import { atom, selector, DefaultValue } from 'recoil';

const DocumentsAtom = atom({
  key: 'DocumentsAtom',
  default: {},
});

const documentsSelector = selector({
  key: 'documentsSelector',
  get: ({ get }) => {
    return get(DocumentsAtom);
  },
  set: ({ set, reset }, newField) => {
    // reset the atoms to its default value
    if (newField instanceof DefaultValue) {
      reset(DocumentsAtom);
      return;
    }
    set(DocumentsAtom, newField);
  },
});

export default documentsSelector;
