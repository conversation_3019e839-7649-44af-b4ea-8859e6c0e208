import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import moment from 'moment';

import useStartEndTimes from '@hooks/useStartEndTimes';

import {
  Row,
  Col,
  AddRemoveButtonContainer,
  FormTextBox,
  FormSelect,
  FormMultiselect,
  FormInfoField,
  FormInfo,
  FormAddRemoveDayButtons,
  FormStartEndDateTimePickers,
  handleFormFieldChange,
} from '@components/global/Form';

import { repairShopOptionsAtom } from '@recoil/app';
import { agentsFormOptionsSelector } from '@recoil/agents';
import { selectedEventState } from '@recoil/eventSidebar';

import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';

const TruckServiceForm = ({ eventTypesForUser }) => {
  const handleTimeChange = useStartEndTimes();
  const [event, setEvent] = useRecoilState(selectedEventState);

  const {
    id,
    oids,
    address,
    notes: { summaryNotes },
    jobLength,
    type,
    scheduledBy,
    scheduledDate,
    eventName = '',
    startEndTimes,
  } = event;

  // The event won't have an ID yet if we're just creating it.
  const isCreateEvent = !id;

  const handleFieldChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  const serviceSummaryOptions = [
    { key: 'Regular Maintenance', value: 'Regular Maintenance' },
    { key: 'Windshield Repair', value: 'Windshield Repair' },
    { key: 'Collision Repair', value: 'Collision Repair' },
    { key: 'Requires An Inspection', value: 'Requires An Inspection' },
  ];

  return (
    <EventSidebarBody>
      <Row>
        <Col>
          <FormSelect
            required
            title="Job Type"
            placeholder="Select Job Type"
            name="type"
            value={type}
            onChange={handleFieldChange}
            options={eventTypesForUser}
            testid="insulation-job-type-input"
          />
          <FormMultiselect
            required
            title="Truck(s)"
            name="oids"
            recoilOptions={agentsFormOptionsSelector}
            onChange={handleFieldChange}
            value={oids}
          />
          <FormSelect
            required
            title="Service Summary"
            placeholder="Enter Service Summary"
            name="eventName"
            value={eventName}
            onChange={handleFieldChange}
            options={serviceSummaryOptions}
          />
          <FormTextBox
            title="Summary Notes"
            name="notes.summaryNotes"
            value={summaryNotes}
            onChange={handleFieldChange}
          />
        </Col>
        <Col>
          <FormSelect
            required
            title="Location"
            placeholder="Enter Location"
            name="address"
            recoilOptions={repairShopOptionsAtom}
            type="object"
            value={address}
            onChange={handleFieldChange}
          />
          <FormStartEndDateTimePickers
            startEndTimes={startEndTimes}
            onChange={handleTimeChange}
            allowDateSelect={false}
          />
          <AddRemoveButtonContainer>
            <FormAddRemoveDayButtons
              name="jobLength"
              value={jobLength}
              onChange={handleFieldChange}
              amount={1}
              testid="1"
            >
              1 Day
            </FormAddRemoveDayButtons>
            <FormAddRemoveDayButtons
              name="jobLength"
              value={jobLength}
              onChange={handleFieldChange}
              amount={0.5}
              testid="0-5"
            >
              1/2 Day
            </FormAddRemoveDayButtons>
            <FormAddRemoveDayButtons
              amount={0.25}
              value={jobLength}
              name="jobLength"
              onChange={handleFieldChange}
              testid="0-25"
            >
              1/4 Day
            </FormAddRemoveDayButtons>
          </AddRemoveButtonContainer>
          {!isCreateEvent && (
            <FormInfo>
              <FormInfoField title="Scheduled By :" body={scheduledBy} />
              <FormInfoField
                title="Scheduled On :"
                body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
              />
            </FormInfo>
          )}
        </Col>
      </Row>
    </EventSidebarBody>
  );
};

TruckServiceForm.propTypes = {
  eventTypesForUser: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string,
      value: PropTypes.string,
    }),
  ).isRequired,
};

export default TruckServiceForm;
