import React from 'react';
import { TableDescription, TableTitle } from './DuplicateCustomerHeader';

const DuplicateLeadsHeader = () => {
  return (
    <>
      <TableTitle>Duplicate Lead(s) Found</TableTitle>
      <TableDescription>
        These existing leads have similar contact information to what was entered. If a lead already
        exists for this customer, select the unit that the lead matches from the dropdown menu in
        the corresponding row in the table below. When you are ready, click Forward.
      </TableDescription>
      <TableTitle>
        IMPORTANT: If leads are identical except for the source, and the source of one of the leads
        is Auditor Referral, be sure to select that lead.
      </TableTitle>
    </>
  );
};

export default DuplicateLeadsHeader;
