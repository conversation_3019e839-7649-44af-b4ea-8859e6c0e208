import { selector } from 'recoil';

import { getAuthorizedDepartments } from '@utils/AuthUtils';
import { agentsSelectorFamily } from '@recoil/agents';
import { searchAdminState } from '@recoil/admin';

// get agents by departments
const allAuthorizedAgentsForUserSelector = selector({
  key: 'allAuthorizedAgentsForUserSelector',
  get: async ({ get }) => {
    const departmentsByState = getAuthorizedDepartments();
    // Keep track of which departments the current user is authorized for in each state
    // example: { 00: ['HVAC-Install', 'Software', 'CIA', 'HVAC-Sales', 'Insulation', 'Partners'], 01: ['HEA'] }
    const authorizedDepartmentsForStatesMap = departmentsByState.reduce(
      (acc, { department, stateAbbr }) => {
        if (!acc[stateAbbr]) acc[stateAbbr] = [];
        acc[stateAbbr].push(department);
        return acc;
      },
      {},
    );
    const agents = get(agentsSelectorFamily(authorizedDepartmentsForStatesMap));
    // Search by a specific search term
    const searchTerm = get(searchAdminState);

    const filteredAgents = agents.filter((agent) => {
      const { displayName, departmentName: agentDepartment, state: agentState } = agent;

      const isAuthorizedForUser = authorizedDepartmentsForStatesMap[agentState]?.includes(
        agentDepartment,
      );

      const matchesSearchTerm = displayName?.toLowerCase().includes(searchTerm?.toLowerCase());

      return isAuthorizedForUser && matchesSearchTerm;
    });

    return filteredAgents;
  },
});

export default allAuthorizedAgentsForUserSelector;
