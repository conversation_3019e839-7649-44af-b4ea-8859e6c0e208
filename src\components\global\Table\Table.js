/* eslint-disable react/no-array-index-key */
import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';
import uniqueId from 'lodash/uniqueId';

const TableContainer = styled.table`
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  background-color: whitesmoke;
  tbody tr:hover td {
    background-color: #e0e0e0;
  }
`;
const EditTextButton = styled.button`
  border: none;
  background-color: transparent;
  text-decoration-line: underline;
  padding-left: initial;
`;
const EditButton = styled.button`
  padding: 5px 10px;
  text-align: center;
  user-select: none;
  background: none;
  color: inherit;
  border: none;
  &:hover {
    text-decoration: underline;
  }
  svg {
    margin-right: 5px;
  }
`;
const SortButton = styled.button`
  background-color: transparent;
  border: none;
  margin-left: 10px;
  color: ${({ theme }) => theme.colors.darkGray};
`;
const TableData = styled.td`
  font-size: 15px;
  font-weight: 400;
`;
const ColumnName = styled.th`
  font-weight: 400;
  color: ${({ theme }) => theme.colors.darkGray};
  &.column-sort {
    font-weight: 450;
  }
`;

const Table = ({
  header = [],
  data,
  editIcon = false,
  sortColumnFunc = () => {},
  showEditButton = true,
  handleSelectedRow = () => {},
  showOnRowSelection = false,
  handleRowClick = () => {},
}) => {
  const [selectedRow, setSelectedRow] = React.useState('');

  const toggleRowSelection = (id) => {
    if (selectedRow === id) {
      setSelectedRow('');
    } else {
      setSelectedRow(id);
      handleSelectedRow(id);
    }
  };

  const renderRow = (tableRow, title) => {
    // '' columnName refers to having an action on each row for first td, but using td will create inconsistency between columns
    // so we are returning empty fragement here, because we don't want our action to have td
    // Assume on each row in the beginning we want to have a checkbox, To acheive that we pass columnName = '' and add checkbox on header loop
    // Please check below iteration of Headers for this table.
    const isActionProperty = title.columnName === '';
    if (isActionProperty) {
      return <></>;
    }
    if (tableRow[title.columnName]) {
      return (
        <td className="table-data">
          {title.showEditModal ? (
            <EditTextButton onClick={() => tableRow.showModal(tableRow.id)} type="button">
              {tableRow[title.columnName]}
            </EditTextButton>
          ) : (
            <>{tableRow[title.columnName]}</>
          )}
        </td>
      );
    }
    return <td className="table-data">-</td>;
  };

  const renderData = () => {
    return data?.map((tableRow, index) => {
      const rowData = header?.map((title) => {
        return (
          <React.Fragment key={uniqueId()}>{renderRow(tableRow, title, index)}</React.Fragment>
        );
      });
      const isSelected = selectedRow?.includes(tableRow.id);
      return (
        <tr key={`table-row-${index}`} onClick={(e) => handleRowClick(e, tableRow)}>
          {showOnRowSelection && (
            <TableData>
              <input
                type="checkbox"
                checked={isSelected}
                onChange={() => toggleRowSelection(tableRow.id)}
              />
            </TableData>
          )}
          {rowData}
          {showEditButton && editIcon && (
            <TableData>
              <EditButton
                type="button"
                key={`edit-${index}`}
                id="user"
                onClick={() => tableRow.showModal(tableRow.id)}
              >
                <FontAwesomeIcon icon={editIcon} />
                Edit
              </EditButton>
            </TableData>
          )}
        </tr>
      );
    });
  };
  return (
    <TableContainer className="table mt-5">
      <thead>
        <tr>
          {header.map(({ columnName, sortColumn }, index) => {
            // if we have action inside return empty fragement so our action can align with td instead of creating new header of action
            const isActionOnEachRow = showOnRowSelection && index === 0;
            return (
              <ColumnName key={uniqueId()} className={sortColumn ? 'column-sort' : ''}>
                {isActionOnEachRow ? (
                  <></>
                ) : (
                  <>
                    {columnName.toUpperCase()}
                    <SortButton
                      onClick={() => (sortColumn ? sortColumnFunc(-1) : sortColumnFunc(index))}
                      type="button"
                    >
                      {sortColumn ? (
                        <FontAwesomeIcon icon={faChevronUp} />
                      ) : (
                        <FontAwesomeIcon icon={faChevronDown} />
                      )}
                    </SortButton>
                  </>
                )}
              </ColumnName>
            );
          })}
          {/* eslint-disable-next-line jsx-a11y/control-has-associated-label */}
          <ColumnName />
        </tr>
      </thead>
      <tbody>{renderData()}</tbody>
    </TableContainer>
  );
};

Table.propTypes = {
  header: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.string),
    PropTypes.arrayOf(
      PropTypes.shape({
        columnName: PropTypes.string,
        oldKey: PropTypes.string,
      }),
    ),
  ]),
  data: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string,
      department: PropTypes.string,
      company: PropTypes.string,
      region: PropTypes.string,
      showModal: PropTypes.func,
    }),
  ).isRequired,
  editIcon: PropTypes.oneOfType([PropTypes.element.isRequired, PropTypes.bool]),
  sortColumnFunc: PropTypes.func,
  showEditButton: PropTypes.bool,
  showOnRowSelection: PropTypes.bool,
  handleSelectedRow: PropTypes.func,
  handleRowClick: PropTypes.func,
};

export default Table;
