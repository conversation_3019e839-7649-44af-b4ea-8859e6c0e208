import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useRecoilState, useSetRecoilState, useRecoilValue } from 'recoil';

import { SalesforceManager } from '@utils/APIManager';

import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import { Header } from '@components/global';
import { SecondaryButton } from '@components/global/Buttons';
import {
  Container,
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  BackButton,
} from '@components/global/Form';

import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import { selectedEventState, isSlotsSearchAtom } from '@recoil/eventSidebar';
import { eventTypesByOidSelectorFamily } from '@recoil/app';

const FindHVACInstallSlotsForm = ({ handleFindSlotsClick, onClickBackButton, searchId = '' }) => {
  const [slotInfo, setSlotInfo] = useRecoilState(selectedEventState);
  const {
    type,
    jobLength,
    sfIds: { accountId },
    workOrders,
    oid,
    oids,
  } = slotInfo;
  const eventTypesForUser = useRecoilValue(eventTypesByOidSelectorFamily(oid || oids[0]));
  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);

  useEffect(() => {
    if (eventTypesForUser?.length) {
      const { key: eventTypeName } = eventTypesForUser.find(({ value }) => {
        return value === type;
      });
      handleFieldChange({ target: { name: 'eventTypeName', value: eventTypeName || '' } });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventTypesForUser]);

  useEffect(() => {
    if (searchId) {
      handleIdChange({ target: { name: 'sfIds.accountId', value: searchId } });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchId]);

  const handleIdChange = async (event) => {
    handleFieldChange(event);
    const { value: id } = event.target;

    if (id.length !== 18) return;

    const contractResponse = await SalesforceManager.getHvacContractObjectsWithDealId('', id);

    if (!contractResponse) return;

    const { workOrders, customer, accountId } = contractResponse;
    // Remove empty object from WorkOrders
    const filteredWorkOrders = workOrders?.filter((value) => Object.keys(value).length !== 0);

    if (filteredWorkOrders?.length > 0) {
      const updatedSfIds = { ...slotInfo.sfIds, accountId };
      const { address, phoneNumber, email, customerName } = customer;
      setSlotInfo({
        ...slotInfo,
        workOrders: filteredWorkOrders,
        sfIds: updatedSfIds,
        address,
        phoneNumber,
        email,
        customerName,
      });
    }
  };

  const handleJobTypeChange = (event) => {
    const {
      target: { value },
    } = event;
    const { workOrderId, projectManager, concierge } = workOrders
      ?.filter(({ workVisitId }) => {
        return !workVisitId;
      })
      .find(({ eventType }) => eventType === value);

    const updatedSfIds = { ...slotInfo.sfIds, workOrderId };
    setSlotInfo({ ...slotInfo, type: value, sfIds: updatedSfIds, projectManager, concierge });
  };
  const handleFieldChange = (e, updatedEvent = slotInfo) => {
    handleFormFieldChange(e, updatedEvent, setSlotInfo);
  };

  const renderWorkOrders = () => {
    const jobTypeOptions = [];

    const scheduledJobs = workOrders?.map(
      ({ workVisitId, workOrderId, eventType: type, crewName, startDate, eventTypeName }) => {
        const date = moment(startDate).format('MM-DD-YYYY');
        if (workVisitId)
          return (
            <Container key={workVisitId}>
              {eventTypeName} scheduled on {date} assigned to {crewName}
            </Container>
          );
        if (eventTypeName && workOrderId && type)
          jobTypeOptions.push({
            key: eventTypeName,
            name: workOrderId,
            value: type,
          });
        return null;
      },
    );
    const filteredScheduledJobs = scheduledJobs?.filter((scheduleJob) => {
      return scheduleJob;
    });

    const unscheduledJobs = jobTypeOptions.length > 0 && (
      <>
        <FormSelect
          title="job type"
          name="type"
          value={type}
          options={jobTypeOptions}
          onChange={handleJobTypeChange}
          placeholder="Select a job to schedule"
        />
        {type && (
          <FormInput
            name="jobLength"
            title="job length"
            value={jobLength}
            type="number"
            min={0.25}
            max={5}
            step={0.25}
            onChange={handleFieldChange}
            placeholder="Select a job to schedule"
          />
        )}
      </>
    );

    return (
      <>
        <Row>
          <Col>
            <Header h2>Scheduled Jobs:</Header>
            {filteredScheduledJobs}
            <br />
            {unscheduledJobs}
          </Col>
        </Row>
      </>
    );
  };

  // HVAC Sidebar is shown with information for HVAC before acutally booking the appointment.
  const handleBookSlot = () => {
    setIsSlotsSearch(false);
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col size={2}>
            <HeaderTitle>
              <BackButton onClick={onClickBackButton} />
              Book Appointment
            </HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            <FormInput
              title="Salesforce Account ID"
              name="sfIds.accountId"
              placeholder="Enter Salesforce Account ID"
              value={accountId}
              onChange={(e) => handleIdChange(e)}
            />
            {workOrders?.length > 0 && <>{renderWorkOrders()}</>}
          </Col>
        </Row>
        <Row>
          <Col>
            <AvailableSlots />
            <BookSlotsButton handleBookSlots={() => handleBookSlot()} />
          </Col>
        </Row>
      </EventSidebarBody>
      <EventSidebarFooter>
        {type && jobLength ? (
          <>
            <SecondaryButton left onClick={() => handleFindSlotsClick()}>
              View Available Slots
            </SecondaryButton>
          </>
        ) : null}
      </EventSidebarFooter>
    </SidebarForm>
  );
};

FindHVACInstallSlotsForm.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
  onClickBackButton: PropTypes.func.isRequired,
  searchId: PropTypes.string,
};

export default FindHVACInstallSlotsForm;
