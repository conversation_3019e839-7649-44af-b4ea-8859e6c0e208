# LeadIntake

Lead Intake generic form fields and form schema structure. How it works ?

## Step 1: Form Schema Fields and CallBacks

```
const formSchema = {
    referredByAuditor: {
        #refers to field name on form state.
    text: 'Auditor name',
        #refers to field label on form render.
    conditional: (values) => values.leadSource === 'Auditor Referral',
       #refers to conditional rendering of elements on form render.
    type: 'autocomplete',
        #refers to types of field of form element, Please check DataIntakeFormElement.js.
    default: '',
        #default values for form element.
    recoilOptions: hesAgentFormOptionsSelector,
        #refers to get data from recoil state. Mostly used to fetch dropdown values for form select.
    required: true,
        #refers to show required * mark on form render.
    api: async (): any => {}
        #refers to make api backend call.
    options:[]
        #refers to show static dropdown values.['Yes', 'No']
    conditionalSetter: (values, fieldValue) => {}
        #refers to set different field value if specfic condtional criteria is match
        #values are complete form values & fieldValue is value for current field on change
    validation: (values) => {}
        #values are complete form values so we can validate a field value and show swal pop ups.
    conditionalDisable: (values, unitNum) => {}
        #values are complete form values & unitNum is unit number field value, This callback is useful if we
        #want to conditional disable any form values
        #it will return true or false and on DataIntakeFormField.js we have check for conditionalDisable it #will disable the field by passing readOnly prop to form element.
    },
... more fields
}
```

## Step 2: Script Schema Fields (Optional)

Scripts are basically just a readme script text on form for users.

```
const scriptFields = {
  intro: {
    text:
      'Before we get into scheduling an audit, I need to ask some questions to ensure that you are eligible for an assessment.',
    type: 'readonly',
  },
    customerInfoIntro: {
    text:
      'Great, based on what you told me, you should be eligible for an assessment. To get the final approval from MassSave, I need to get some more information.',
    type: 'readonly',
  },
  ... more script text objects
}
```

## Step 3: Section Schema Fields (Optional)

Sections are basically combinations of multiple fields in a category which we want to render.

```
const sectionFields = {
  unitInfo: {
    type: 'section',
    fields: [
      leadIntakeFields.unitNumber,
      leadIntakeFields.occupantType,
      leadIntakeFields.heatingFuel,
      leadIntakeFields.gasProvider,
      leadIntakeFields.hasAlternateGasBillName,
      leadIntakeFields.gasBillName,
      leadIntakeFields.gasAccountNumber,
      leadIntakeFields.electricProvider,
      leadIntakeFields.hasAlternateElectricBillName,
      leadIntakeFields.electricBillName,
      leadIntakeFields.electricAccountNumber,
    ],
    perUnit: true,
    conditional: (values) => values.singleOrMultiFamily === 'Single Family',
  },
    customerInfoReview: {
    type: 'section',
    text: 'Customer Info',
    fields: [
      { ...leadIntakeFields.customerFirstName, readOnly: true },
      { ...leadIntakeFields.customerLastName, readOnly: true },
      { ...leadIntakeFields.customerEmail, readOnly: true },
      { ...leadIntakeFields.customerPrimaryPhoneNumber, readOnly: true },
      { ...leadIntakeFields.customerAddress, readOnly: true },
    ],
    perUnit: false,
  },
  ...more sections
}
```

## Step 4: Generate Fields from schema

```
const leadIntakeFields = addKeyNameToObject(formSchema);
```

## Step 5: Generate Scripts from script schema

```
const script = addKeyNameToObject(scriptFields);
```

## Step 6: Generate Section from section schema

```
const sections = parseDataIntakeSections(sectionFields, leadIntakeFields);
```

## Step 7: Finally create a map for leadIntake

```
const leadIntakeMap = {
  preliminaryQuestions: {
    text: 'Preliminary Questions',
    fields: [
      script.intro,
      leadIntakeFields.atAnEvent,
      sections.unitInfo,
      sections.multiUnitInfo,
      sections.facilitatorInfo,
      ...more fields
    ]
  },
  customerInfo: {
    text: 'Customer Info',
    fields: [
      leadIntakeFields.removeFromMailingLists,
      script.customerInfoIntro,
      script.customerName,
      ...more fields
    ]
  },
review: {
    text: 'Review',
    fields: [sections.customerInfoReview, sections.utilityInfoReview],
  },
... more tabs
}
```

## Step 8: Export all

```
export { leadIntakeFields, script, sections, leadIntakeMapBAs, leadIntakeMap };
export default addKeyNameToObject(leadIntakeMap);
```

## Tabs Rendering with Their Fields

Create Tabs structure with Component and Pass leadIntake map.

```
import { leadIntakeMap } from './FormSchema/leadIntakeMap';
export const leadIntakeTabs = [
    {
      name: 'preliminaryQuestions',
      title: 'Preliminary Questions',
      component: <DataIntakeFormPage map={leadIntakeMap.preliminaryQuestions} />,
    },
    {
      name: 'review',
      title: 'Review',
      component: <DataIntakeFormPage map={leadIntakeMap.review} />,
    },
    ...more Tabs
  ];
```

## Form Actions For Next and Prev Page

```
  const formActions = (
    <ActionsContainer>
      {activeTabIndex > 0 && (
        <PrimaryButton width="75px" onClick={() => handlePrev()}>
          Back
        </PrimaryButton>
      )}
      <PrimaryButton width="75px" onClick={() => handleNext()}>
        Next
      </PrimaryButton>
    </ActionsContainer>
  );
```

## Form Step validation

Create a basic required field object

```
  const requiredFields = {
    preliminaryQuestions: {
      'At Event': formValues?.atAnEvent,
      'Campaign ID': formValues?.atAnEvent ? formValues?.atAnEvent : true,
      'Preferred Language': formValues?.
    customerInfo: {
      'First Name': formValues?.customerFirstName,
      'Last Name': formValues?.customerLastName,
    }
  }
```

Then listen to formValues in dependency and add this effect. This will allow user to move to next or prev step if the current step validation is passed.

```
const [stepValidation, setStepValidation] = React.useState([false, false, false, false]);
  useEffect(() => {
    const stepValidationList = Object.values(requiredFields).map((section) => {
      const isEmptyObject = Object.keys(section).length === 0;
      if (isEmptyObject) {
        return false;
      }
      return !validateRequiredParams(section).length > 0;
    });
    setStepValidation(stepValidationList);
  }, [formValues]);
```

## Passing All Items to Generic DataIntakeForm Component

```
<DataIntakeForm
  map={leadIntakeMap}
  fields={leadIntakeFields}
  valuesState={leadIntakeValuesState}
  tabs={leadIntakeTabs}
  actions={formActions}
  stepValidation={stepValidation}
/>
```