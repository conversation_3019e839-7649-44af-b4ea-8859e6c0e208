import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

import { Tachometer as DashboardIcon } from '@styled-icons/boxicons-regular/Tachometer';

import Dashboard from '@components/Dashboard/Dashboard';
import WeeklyPayrollSummary from '@components/Dashboard/WeeklyPayrollSummary';

import { UtilityManager } from '@utils/APIManager';

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const DashboardContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  // This lets the border radius show over the rest of the cells.overflow
  // Might need to take this out for the tooltip.
  overflow: hidden;
  background-color: ${({ theme }) => theme.secondary[100]};
`;

const DashboardTopContainer = styled.div`
  flex: 0 1 auto;
  background-color: #f3f3f3;
  border-bottom: 2px solid #0070d2;
`;

const DashboardRowsContainer = styled.div`
  display: inline;
  flex: 1 1 auto;
  overflow: auto;
  padding: 10px;
`;

const HeaderStaticContainer = styled.div`
  display: flex;
  flex-direction: row;
`;

const HeaderTextContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const DashboardText = styled.div`
  color: ${({ theme }) => theme.primary[400]};
  font-size: 13px;
`;
const DashboardTypeText = styled.div`
  color: ${({ theme }) => theme.primary[400]};
  font-size: 28px;
`;

const DashboardIconStyle = styled(DashboardIcon)`
  height: 52px;
  margin-right: 5px;
  margin-bottom: 2px;
  color: white;
  background-color: #707172;
  padding: 4px;
  border-radius: 2px;
`;

const DashboardHeader = styled.div`
  display: flex;
  flex-direction: row;
  margin: 12px 12px;
  font-size: 24px;
  font-weight: 500;
  padding: 8px;
  color: #032d60;
  @media (max-width: 450px) {
    font-size: 20px;
    padding-bottom: 10px;
    align-self: flex-start;
  }
`;

const HEAPayDashboard = () => {
  const [recordDetails, setRecordDetails] = useState(null);

  useEffect(() => {
    const getDashboardDetails = async () => {
      await getHesPayDashboard();
    };
    getDashboardDetails();
  }, []);

  const getHesPayDashboard = async () => {
    const records = await UtilityManager.getHesPayDashboard();
    setRecordDetails(records);
  };

  return (
    <Wrapper>
      <DashboardContainer>
        <DashboardTopContainer>
          <DashboardHeader>
            <HeaderStaticContainer>
              <DashboardIconStyle />
              <HeaderTextContainer>
                <DashboardText>Dashboard</DashboardText>
                <DashboardTypeText>HEA Pay</DashboardTypeText>
              </HeaderTextContainer>
            </HeaderStaticContainer>
          </DashboardHeader>
        </DashboardTopContainer>
        <DashboardRowsContainer>
          <WeeklyPayrollSummary details={recordDetails?.summary} />
          {recordDetails && <Dashboard details={recordDetails.reports} />}
        </DashboardRowsContainer>
      </DashboardContainer>
    </Wrapper>
  );
};

export default HEAPayDashboard;
