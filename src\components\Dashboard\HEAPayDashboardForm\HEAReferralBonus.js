import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const HEAReferralBonus = ({ record = {} }) => {
  const { dealId, referralBonus, heaVisitResult, startTime, siteId } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="referralBonus"
            value={referralBonus}
            title="Referral Bonus"
            placeholder=""
          />
          <FormInput
            readOnly
            name="startTime"
            value={startTime}
            title="Start Time"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput readOnly name="siteId" value={siteId} title="Site Id" placeholder="" />
          <FormInput
            readOnly
            name="heaVisitResult"
            value={heaVisitResult}
            title="HEA Visit Result"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

HEAReferralBonus.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    referralBonus: PropTypes.string,
    heaVisitResult: PropTypes.string,
    startTime: PropTypes.string,
    siteId: PropTypes.string,
  }),
};

export default HEAReferralBonus;
