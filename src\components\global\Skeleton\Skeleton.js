import React from 'react';
import styled, { keyframes } from 'styled-components';

const shimmer = keyframes`
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
`;

const SkeletonContainer = styled.div`
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: transparent;
  position: relative;
`;

const ShimmerEffect = styled.div`
  width: ${(props) => props.width || '100%'};
  height: ${(props) => props.height || '100%'};
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: ${shimmer} 1.5s infinite;
  margin-bottom: 18px;
`;

const Skeleton = () => (
  <SkeletonContainer>
    <ShimmerEffect width="25%" height="26px" />
    <ShimmerEffect width="25%" height="26px" />
    <ShimmerEffect width="25%" height="36px" />
    <ShimmerEffect width="35%" height="46px" />
    <ShimmerEffect width="75%" height="60px" />
    <ShimmerEffect width="85%" height="60px" />
    <ShimmerEffect width="65%" height="60px" />
    <ShimmerEffect width="85%" height="60px" />
    <ShimmerEffect width="85%" height="60px" />
  </SkeletonContainer>
);

export default Skeleton;
