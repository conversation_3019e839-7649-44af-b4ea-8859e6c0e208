# Swap Days 

## Intended Functionality

A feature to help HES schedule which weekends they are going to work, and which weekdays they will take off instead.

- An HES will be reminded every time they refresh the browser to schedule their weekends on/weekdays off, starting at 1 week and 1 month before the unscheduled month. For example, for the month of May, a user will be reminded starting the last week of March.

- This is calculated by the value in the `last_updated_swap_month` column in the `users` table, which stores a date in the month of the last month that has swap days that have been scheduled. This defaults to null for users.

- When a user goes to schedule their Swap Days, a sidebar opens that has a datepicker with the relevant month selected. All of the days they will be working that month is highlighted. In most cases, it will just be every weekday in that month. After a user has selected (2) weekend days to work, they will then be able to deselect 2 weekdays and click save. 

- After scheduling swap days, the `Select Swap Days` button in the upper right corner of the page and the recurring notification modal will disappear until the next month. 