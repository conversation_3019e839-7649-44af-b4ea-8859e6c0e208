import React from 'react';
import { useRecoilState } from 'recoil';
import styled from 'styled-components';
import { FilterAlt } from '@styled-icons/boxicons-regular/FilterAlt';

import { showFilterBarState } from '@recoil/calendar';

const StyledFilterToggle = styled.button`
  height: 30px;
  background: ${({ isActive, theme }) => {
    return isActive ? theme.colors.eventGreen : theme.secondary[100];
  }};
  color: ${({ isActive, theme }) => {
    return isActive ? theme.secondary[100] : theme.colors.eventGreen;
  }};
  border: 1px solid ${({ theme }) => theme.colors.eventGreen};
  border-radius: 4px;
  padding: 0 4px;
  margin-right: 10px;
  text-align: center;
`;
const FilterToggleIcon = styled(FilterAlt)`
  height: 22px;
`;

const FilterToggle = () => {
  const [showFilterBar, setShowFilterBar] = useRecoilState(showFilterBarState);

  return (
    <StyledFilterToggle isActive={showFilterBar} onClick={() => setShowFilterBar(!showFilterBar)}>
      <FilterToggleIcon />
    </StyledFilterToggle>
  );
};

export default FilterToggle;
