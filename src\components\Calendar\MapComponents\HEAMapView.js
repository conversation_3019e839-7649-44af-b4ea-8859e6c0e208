import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useRecoilValue, useSetRecoilState } from 'recoil';

// Custom hooks
import {
  useGoogleMapInitialization,
  useHESAgentHomes,
  useMapMarkers,
  useHEAMapViewState,
} from '@hooks';

// Recoil atoms
import {
  heaSelectedDateAtom,
  heaActiveFiltersAtom,
  heaAppointmentsAtom,
  heaAgentsAtom,
} from '@recoil/heaMapView/heaMapViewAtoms';

import { HESFilterPanel, MapStatsDisplay, MapControlButtons } from './components';

import { MapContainer, MapControlsContainer } from './HEAMapViewStyles';

const HEAMapView = ({
  startDate = moment().startOf('day'),
  endDate = moment().endOf('day'),
  type = '010000', // CT HEA type
  selectedDate = null,
}) => {
  // Initialize Google Maps
  const { googleMapRef, mapRef, mapInitialized } = useGoogleMapInitialization();

  // Manage HEA map view state using Recoil
  const {
    selectedHesOid,
    toggleAgentFilter,
    selectAllAgents,
    deselectAllAgents,
    resetFilters,
    loadAppointments,
  } = useHEAMapViewState({
    startDate,
    endDate,
    type,
  });

  // Read state directly from Recoil atoms to ensure updates trigger re-renders
  const appointments = useRecoilValue(heaAppointmentsAtom);
  const hesAgents = useRecoilValue(heaAgentsAtom);
  const activeFilters = useRecoilValue(heaActiveFiltersAtom);

  // Load HES agent home addresses
  const { hesAgentsWithHomes } = useHESAgentHomes(hesAgents);

  // Update selected date in Recoil when prop changes
  const setSelectedDate = useSetRecoilState(heaSelectedDateAtom);
  useEffect(() => {
    if (selectedDate) {
      setSelectedDate(selectedDate);
      resetFilters();
    }
  }, [selectedDate, setSelectedDate, resetFilters]);

  // Create and manage map markers/routes
  useMapMarkers({
    googleMapRef,
    mapInitialized,
    appointments,
    hesAgents,
    hesAgentsWithHomes,
    activeFilters,
  });

  return (
    <MapContainer>
      <MapControlsContainer>
        <HESFilterPanel
          hesAgents={hesAgents}
          activeFilters={activeFilters}
          selectedHesOid={selectedHesOid}
          onToggleFilter={toggleAgentFilter}
          onSelectAll={selectAllAgents}
          onDeselectAll={deselectAllAgents}
          onReset={resetFilters}
          onRefresh={loadAppointments}
        />

        <MapStatsDisplay
          hesAgents={hesAgents}
          activeFilters={activeFilters}
          appointments={appointments}
          startDate={startDate}
        />

        <MapControlButtons googleMapRef={googleMapRef} />
      </MapControlsContainer>

      <div ref={mapRef} style={{ width: '100%', height: '100%' }} />
    </MapContainer>
  );
};

HEAMapView.propTypes = {
  startDate: PropTypes.instanceOf(moment),
  endDate: PropTypes.instanceOf(moment),
  type: PropTypes.string,
  selectedDate: PropTypes.instanceOf(moment),
};

export default HEAMapView;
