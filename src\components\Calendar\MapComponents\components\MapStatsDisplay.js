import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { useRecoilValue } from 'recoil';
import { searchAdminState } from '@recoil/admin';
import { MapStats } from '../HEAMapViewStyles';

/** visible appointments and agents */
const MapStatsDisplay = ({ hesAgents, activeFilters, appointments }) => {
  // Get search term from Recoil
  const searchTerm = useRecoilValue(searchAdminState);

  // Filter agents based on search term and exclude agents with 0 appointments
  const filteredHesAgents = useMemo(() => {
    if (!searchTerm) {
      return hesAgents.filter((agent) => agent.appointmentCount > 0);
    }
    return hesAgents.filter((agent) => {
      if (agent.appointmentCount === 0) return false;
      const searchableText = [
        agent.name,
        agent.displayName,
        agent.firstname,
        agent.lastname,
        `${agent.firstname} ${agent.lastname}`.trim(),
      ]
        .filter(Boolean)
        .join(' ')
        .toLowerCase();
      return searchableText.includes(searchTerm.toLowerCase());
    });
  }, [searchTerm, hesAgents]);

  // Calculate visible appointments count
  const visibleAppointments = useMemo(
    () => appointments.filter((apt) => activeFilters.has(apt.hesOid)),
    [appointments, activeFilters],
  );

  const totalAppointments = appointments.length;
  const visibleHESCount = activeFilters.size;

  const filteredVisibleHESCount = useMemo(
    () => filteredHesAgents.filter((agent) => activeFilters.has(agent.oid)).length,
    [filteredHesAgents, activeFilters],
  );

  return (
    <MapStats>
      <div>
        Showing:{' '}
        {searchTerm
          ? `${filteredVisibleHESCount} of ${filteredHesAgents.length} filtered`
          : `${visibleHESCount} of ${hesAgents.length}`}{' '}
        HES
      </div>
      <div>
        Appointments: {visibleAppointments.length} of {totalAppointments}
      </div>
    </MapStats>
  );
};

MapStatsDisplay.propTypes = {
  hesAgents: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  activeFilters: PropTypes.instanceOf(Set).isRequired,
  appointments: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  startDate: PropTypes.shape({}),
};

export default MapStatsDisplay;
