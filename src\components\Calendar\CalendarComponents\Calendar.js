import React, { useState, useEffect } from 'react';
import { useRecoilValue, useSetRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';
import { decodeEventType } from '@homeworksenergy/utility-service';
import { addEventChangedListener, reloadCalendar } from '@utils/EventEmitter';
import { searchAdminState } from '@recoil/admin';
import { departmentZipcodesAtom } from '@recoil/utility';
import { calendarTypeAtom } from '@recoil/app';
import { managerTruckFilterAtom } from '@recoil/event';
import { UtilityManager } from '@utils/APIManager';
import CalendarRow from './CalendarRow';
import CalendarHeader from './CalendarHeader';
import GroupHeader from './GroupHeader';
import CalendarControlHeader from './CalendarControlHeader';

import CalendarTopContainer from './CalendarTopContainer';

const StyledCalendar = styled.div`
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  width: 100%;
  height: 100%;
  border-radius: 5px;
  // This lets the border radius show over the rest of the cells.overflow
  // Might need to take this out for the tooltip.
  overflow: hidden;
  background-color: ${({ theme }) => theme.secondary[100]};
`;

const CalendarRowsContainer = styled.div`
  flex: 1 1 auto;
  overflow: auto;
`;

const Calendar = React.memo(function Calendar({
  rows = [],
  id = null,
  includeRegionHeaders = false,
  includeAgentTypeHeaders = false,
  onMapViewClick = null,
}) {
  const [startDate, setStartDate] = useState(moment().startOf('week'));
  const [regionFilter, setRegionFilter] = useState(null);
  const [eventTypeFilter, setEventTypeFilter] = useState(null);
  const [agentTypeFilter, setAgentTypeFilter] = useState(null);
  const managerTrucksFilter = useRecoilValue(managerTruckFilterAtom);
  const searchCrew = useRecoilValue(searchAdminState);
  const calendarType = useRecoilValue(calendarTypeAtom);
  const [holidays, setHolidays] = useState({});
  const setDepartmentZipcodesAtom = useSetRecoilState(departmentZipcodesAtom);

  useEffect(() => {
    const fetchHolidays = async () => {
      const holidays = await UtilityManager.getAllHolidays();
      setHolidays(holidays);
    };
    fetchHolidays();
  }, []);

  useEffect(() => {
    const fetchHvacSalesZipCodes = async () => {
      const zipcodes = await UtilityManager.getZipCodesByDepartment(2);
      setDepartmentZipcodesAtom(zipcodes);
    };
    if (calendarType && calendarType === '000000') fetchHvacSalesZipCodes();
  }, [calendarType, setDepartmentZipcodesAtom]);

  useEffect(() => {
    // Reload calendar on date change
    reloadCalendar(startDate, id);
    // Update EventChanged listener (fired on reschedule, cancel, reassign etc) to reflect the new date
    const removeEventChangedListener = addEventChangedListener(() => reloadCalendar(startDate, id));
    return () => {
      removeEventChangedListener();
    };
  }, [startDate, id]);

  const handleDateChange = (date) => {
    setStartDate(moment(date).startOf('week'));
  };
  const changeWeek = (newWeek = moment(), date = null) => {
    if (date) {
      handleDateChange(date);
    } else {
      if (moment.isMoment(newWeek)) return setStartDate(moment(newWeek).startOf('week'));
      if (newWeek === '+') return setStartDate(moment(startDate).add(1, 'week'));
      if (newWeek === '-') return setStartDate(moment(startDate).subtract(1, 'week'));
    }
    return null;
  };

  const getRowDetails = (agentInfo) => {
    const {
      attributeNames,
      eventTypeNames,
      regionName,
      agentAddress,
      userPrograms = [],
    } = agentInfo;
    let details = '';

    if (!calendarType) return details;

    const { business: department, state } = decodeEventType(calendarType);
    if (state === 'CT') {
      if (department === 'HEA') {
        const cityString = agentAddress?.city || 'Set Day Start Address';
        const attributesString = `${attributeNames?.length > 0 ? attributeNames.join(', ') : '-'}`;
        details = `${cityString} - ${attributesString}`;
      }
    } else if (department === 'Insulation') {
      details = `${attributeNames?.length > 0 ? attributeNames.join(', ') : '-'}`;
    } else if (department === 'HVAC Install') {
      details = `${eventTypeNames?.length > 0 ? eventTypeNames.join(', ') : '-'}`;
    } else if (department === 'HEA') {
      const programsString = userPrograms.length ? userPrograms.join(', ') : 'No Programs Assigned';
      const cityString = agentAddress?.city || 'Set Day Start Address';
      const regionString = regionName || 'No Region Found';
      details = `${cityString} - ${regionString} - ${programsString}`;
    } else if (department === 'HVAC Sales') {
      details = agentAddress?.city || 'Set Day Start Address';
    }

    return details;
  };

  const renderCalendarRows = () => {
    let previousRegion = '';
    let previousAgentType = '';
    let isNewRegion = false;
    let isNewAgentType = false;
    let filteredRows = rows;
    if (regionFilter) {
      // setting upper limit of sub regions if it splits further
      const subregions = regionFilter % 100 ? regionFilter : regionFilter + 10;
      let data = rows;
      if (eventTypeFilter) data = filteredRows;
      filteredRows = data.filter((user) => {
        const userRegion = user.info.regionId;
        return userRegion >= regionFilter && userRegion <= subregions;
      });
    }
    if (managerTrucksFilter) {
      filteredRows = rows.filter((user) => user.info.isManager);
    }
    if (eventTypeFilter?.length) {
      let data = rows;
      if (regionFilter) data = filteredRows;
      filteredRows = data.filter((user) => user?.info?.eventTypes?.includes(eventTypeFilter));
    }
    if (agentTypeFilter)
      filteredRows = rows.filter((user) => user?.info?.agentType === agentTypeFilter);

    return filteredRows.map((schedule) => {
      const {
        dates,
        info,
        info: { oid, displayName, regionName, attributes, eventTypes, agentTypeName },
      } = schedule;

      isNewRegion = includeRegionHeaders && regionName !== previousRegion;
      isNewAgentType = includeAgentTypeHeaders && agentTypeName !== previousAgentType;
      previousRegion = regionName;
      previousAgentType = agentTypeName;

      if (displayName?.toLowerCase().includes(searchCrew?.toLowerCase())) {
        const details = getRowDetails(info);
        return (
          <React.Fragment key={JSON.stringify(schedule)}>
            {isNewRegion && <GroupHeader title={regionName} defaultTitle="No Region" />}
            {isNewAgentType && <GroupHeader title={agentTypeName} defaultTitle="No Group" />}
            <CalendarRow
              key={JSON.stringify(schedule)}
              oid={oid}
              rowTitle={displayName}
              details={details}
              attributes={attributes}
              eventTypes={eventTypes}
              startDate={startDate}
              dates={dates}
              holidays={holidays}
            />
          </React.Fragment>
        );
      }
      return null;
    });
  };

  return (
    <StyledCalendar>
      <CalendarTopContainer>
        <CalendarControlHeader
          scrollCalendar={changeWeek}
          startDate={moment(startDate)}
          isMonthly={false}
          regionTypeSelected={regionFilter}
          eventTypeSelected={eventTypeFilter}
          agentTypeSelected={agentTypeFilter}
          setRegionFilter={setRegionFilter}
          setEventTypeFilter={setEventTypeFilter}
          setAgentTypeFilter={setAgentTypeFilter}
          onMapViewClick={onMapViewClick}
        />
        <CalendarHeader id={id} startDate={startDate} setStartDate={setStartDate} />
      </CalendarTopContainer>
      {/* Id is necessary here for the auto scroll on drag/drop */}
      <CalendarRowsContainer
        isHvac={['000400', '000300'].includes(calendarType)}
        id="calendar-rows"
      >
        {renderCalendarRows()}
      </CalendarRowsContainer>
    </StyledCalendar>
  );
});

Calendar.propTypes = {
  rows: PropTypes.arrayOf(PropTypes.shape({})),
  id: PropTypes.string,
  includeRegionHeaders: PropTypes.bool,
  includeAgentTypeHeaders: PropTypes.bool,
  onMapViewClick: PropTypes.func,
};

export default Calendar;
