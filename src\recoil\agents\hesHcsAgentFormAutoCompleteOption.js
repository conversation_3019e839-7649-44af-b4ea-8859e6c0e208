import { selector } from 'recoil';
import hesAgentFormAutoCompleteOptionsSelector from './hesHcsAgentsFormAutoCompleteState';

const hesHcsAgentFormAutoCompleteOptionsSelector = selector({
  key: 'hesAgentFormAutoCompleteOptionsSelector',
  get: ({ get }) => {
    const agents = get(hesAgentFormAutoCompleteOptionsSelector);
    return agents?.map((agent) => ({ name: agent.name, value: agent.value }));
  },
});

export default hesHcsAgentFormAutoCompleteOptionsSelector;
