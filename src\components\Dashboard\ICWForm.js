import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import styled from 'styled-components';

import { Row, Col, FormInput, FormTextBox } from '@components/global/Form';
import { openPhoneCall, displayPhoneNumber } from '@utils/functions';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const ICWForm = ({ record = {} }) => {
  const {
    dealId,
    daysSinceIcw,
    leadVendor,
    timeStampClosedWon,
    timeStampIncorrectlyClosedWonByIAM,
    incorrectlyCwNotes,
    hesOriginalContractAmount,
    phoneNumber,
  } = record;

  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="daysSinceIcw"
            value={daysSinceIcw}
            title="Days Since ICW"
            placeholder=""
          />
          <FormInput
            readOnly
            name="timeStampClosedWon"
            value={moment(timeStampClosedWon).format('MM-DD-YYYY  h:mm A')}
            title="Time Stamp Closed Won"
            placeholder=""
          />
          <FormInput
            readOnly
            name="phoneNumber"
            value={displayPhoneNumber(phoneNumber)}
            title="Phone Number"
            onClick={() => openPhoneCall(phoneNumber)}
            placeholder="Phone Number"
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="leadVendor"
            value={leadVendor}
            title="Lead Vendor"
            placeholder=""
          />
          <FormInput
            readOnly
            name="timeStampIncorrectlyClosedWonByIAM"
            value={moment(timeStampIncorrectlyClosedWonByIAM).format('MM-DD-YYYY  h:mm A')}
            title="Timestamp Incorrectly Closed Won by IAM"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hesOriginalContractAmount"
            value={`${hesOriginalContractAmount}`}
            title="HES Original Contract Amount"
            placeholder=""
          />
        </Col>
      </Row>
      <FormTextBox
        readOnly
        name="incorrectlyCwNotes"
        value={incorrectlyCwNotes}
        title="Incorrectly CW Notes"
        placeholder=""
      />
    </>
  );
};

ICWForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    daysSinceIcw: PropTypes.string,
    leadVendor: PropTypes.string,
    timeStampClosedWon: PropTypes.string,
    timeStampIncorrectlyClosedWonByIAM: PropTypes.string,
    incorrectlyCwNotes: PropTypes.string,
    hesOriginalContractAmount: PropTypes.string,
    phoneNumber: PropTypes.string,
  }),
};

export default ICWForm;
