import React, { useEffect } from 'react';
import { useRecoilState, useResetRecoilState, useSetRecoilState } from 'recoil';
import Cookies from 'js-cookie';
import Swal from 'sweetalert2/dist/sweetalert2';
import { useHasChanged } from '@hooks';
import {
  formSettingsState,
  formValuesState,
  activeTabState,
  resetFormRecoilState,
  activeFormState,
} from '@recoil/dataIntakeForm';
import { selectedEventState } from '@recoil/eventSidebar';
import { parseJSXContentForSwalPopup, setCookie } from '@utils/functions';
import { isAuthorized, getUserCookie } from '@utils/AuthUtils';
import { useParams } from 'react-router-dom';
import { activeTabIndexAtom } from '@recoil/app';
import { CampaignManager } from '@utils/APIManager';
import { renovationsRecently, homeAssesmentRecently, houseBuiltAndArea } from '../consts';
import { useFieldsAndTabsForIntake } from '../getters/useTabsAndFieldsForIntake';
import { ScriptText } from '../../LeadIntakeScript/styles';

export const doYouHaveHeatSwalPopup = () =>
  Swal.fire({
    title: 'Customer Not Eligible',
    html: parseJSXContentForSwalPopup(
      <ScriptText>
        The MASSCAP program does not allow HomeWorks to perform assessments at homes that currently
        do not have heat. If you would still like to have access to any energy efficiency programs
        available, please contact the statewide client services center to learn more. Would you like
        the number for that? (You can reach them directly at 833 888-1326).
      </ScriptText>,
    ),
    confirmButtonText: 'OK',
    icon: 'info',
  });

export const isCustomerWorkingWithLocalAgencySwalPopup = () =>
  Swal.fire({
    icon: 'warning',
    title:
      'Stop! The customer should be referred back to the local CAP, we should not schedule an HEA',
    confirmButtonText: 'OK',
    showCancelButton: false,
  });

const LeadIntakeEffects = ({ children }) => {
  const { leadId } = useParams();
  const [formValues, setFormValues] = useRecoilState(formValuesState);
  const [formSettings, setFormSettings] = useRecoilState(formSettingsState);
  const setSelectedEvent = useSetRecoilState(selectedEventState);
  const setActiveForm = useSetRecoilState(activeFormState);
  const resetFormState = useResetRecoilState(resetFormRecoilState);
  const setActiveTab = useSetRecoilState(activeTabState);
  const setActiveTabIndex = useSetRecoilState(activeTabIndexAtom(['tabs']));
  const { leadIntakeTabs } = useFieldsAndTabsForIntake();
  const {
    majorRenovations,
    leadSource,
    campaignId,
    isCampaignIdValid,
    campaign,
    numUnitsSchedulingToday = 1,
    numUnitsInBuilding = 1,
    howManyUnitsAreInAssociation,
    isAuditorValueOnSf,
    discountedRateCodeForCIA,
    doYouHaveHeat,
    isWorkingWithLocalCapAgency,
    howLongAgoEnergyAssesmentConducted,
    houseBuilt,
    squareFeet,
  } = formValues;

  const numUnitChanged = useHasChanged(numUnitsSchedulingToday);

  useEffect(() => {
    const isBrandAmbassador =
      Cookies.get('campaign-id') &&
      Cookies.get('campaign') &&
      Cookies.get('campaign') &&
      isAuthorized('Agent', 'Marketing', true);

    if (isBrandAmbassador) {
      setFormValues({
        isCampaignIdValid: true,
        campaignId: Cookies.get('campaign-id'),
        campaign: Cookies.get('campaign'),
        atAnEvent: 'Yes',
        leadSource: 'Field Marketing',
      });
    }
  }, []);

  useEffect(() => {
    if (campaignId?.length < 10) return;
    if (isCampaignIdValid && isAuthorized('Agent', 'Marketing', true)) {
      setCookie('campaign-id', campaignId, 24);
      setCookie('campaign', campaign, 24);
    }
  }, [isCampaignIdValid, campaignId, campaign]);

  useEffect(() => {
    const isMajorRenovations = majorRenovations === 'Yes';
    if (isMajorRenovations) {
      Swal.fire({
        title: 'Customer Not Eligible',
        html: parseJSXContentForSwalPopup(<ScriptText>{renovationsRecently}</ScriptText>),
        confirmButtonText: 'OK',
        icon: 'info',
      });
    }
  }, [majorRenovations]);

  useEffect(() => {
    if (
      howLongAgoEnergyAssesmentConducted?.length > 0 &&
      howLongAgoEnergyAssesmentConducted !== '6+ Years'
    ) {
      Swal.fire({
        title: 'Customer Not Eligible',
        html: parseJSXContentForSwalPopup(<ScriptText>{homeAssesmentRecently}</ScriptText>),
        confirmButtonText: 'OK',
        icon: 'info',
      });
    }
  }, [howLongAgoEnergyAssesmentConducted]);

  useEffect(() => {
    const houseYearQualify = houseBuilt < 1950;
    const houseAreaQualify = squareFeet > 5000;
    if (houseYearQualify && houseAreaQualify) {
      Swal.fire({
        title: 'Full Day Schedule',
        html: parseJSXContentForSwalPopup(<ScriptText>{houseBuiltAndArea}</ScriptText>),
        confirmButtonText: 'OK',
        icon: 'info',
      });
    }
  }, [houseBuilt, squareFeet]);

  useEffect(() => {
    const isLeadSourceCustomerReferral = leadSource === 'Customer Referral';
    if (isLeadSourceCustomerReferral) {
      Swal.fire({
        title: 'TODO !!!',
        text: 'TODO',
        confirmButtonText: 'OK',
        icon: 'info',
      });
    }
    const isLeadAndAuditorValueIsNotValid = !leadId && !isAuditorValueOnSf;
    if (isLeadAndAuditorValueIsNotValid) {
      setFormValues({
        referredByAuditor: null,
        referredByCustomer: null,
      });
    }
  }, [leadSource, leadId, isAuditorValueOnSf]);
  useEffect(() => {
    if (!numUnitChanged) return;

    const numUnit = parseInt(numUnitsSchedulingToday, 10);

    // Needed for multi-unit scheduling
    // This way we can access "numUnit" in the BookSlotsButton, whether numUnit is set from the leadIntake or the calendar sidebar
    // Then we only show BookSlotsButton if selectedSlots.length === numUnit
    setSelectedEvent({ numUnit });

    setFormSettings({ ...formSettings, numUnit });
  }, [numUnitsSchedulingToday, numUnitChanged, formSettings, setFormSettings, setSelectedEvent]);

  useEffect(() => {
    setActiveForm(null);
    return () => {
      setActiveTab(leadIntakeTabs[0]?.name);
      setActiveTabIndex(0);
      resetFormState();
    };
  }, []);

  useEffect(() => {
    if (isAuthorized('Agent', 'CIA')) {
      setFormValues({ discountedRateCode: discountedRateCodeForCIA });
    }
  }, [discountedRateCodeForCIA]);

  useEffect(() => {
    if (howManyUnitsAreInAssociation === '5+') {
      Swal.fire({
        title: 'Customer Not Eligible',
        html: parseJSXContentForSwalPopup(
          <ScriptText>
            Unfortunately, the MassSave program does not allow HomeWorks to perform assessments at
            homes that are part of a 5 or more unit condo association. The good news is that you
            should still be able to get an assessment through MassSave directly. Would you like the
            number for that? (You can reach them directly at ************).
          </ScriptText>,
        ),
        confirmButtonText: 'OK',
        icon: 'info',
      });
    }
  }, [howManyUnitsAreInAssociation]);

  useEffect(() => {
    if (numUnitsInBuilding === '5+') {
      Swal.fire({
        title: 'Customer Not Eligible',
        html: parseJSXContentForSwalPopup(
          <ScriptText>
            Unfortunately, the MassSave program does not allow HomeWorks to perform assessments at
            homes that are part of a 5 or more unit multi-family. The good news is that you should
            still be able to get an assessment through MassSave directly. Would you like the number
            for that? (You can reach them directly at ************).
          </ScriptText>,
        ),
        confirmButtonText: 'OK',
        icon: 'info',
      });
    }
  }, [numUnitsInBuilding]);

  useEffect(() => {
    if (doYouHaveHeat === 'No') {
      doYouHaveHeatSwalPopup();
    }
  }, [doYouHaveHeat]);

  useEffect(() => {
    if (isWorkingWithLocalCapAgency === 'Yes') {
      isCustomerWorkingWithLocalAgencySwalPopup();
    }
  }, [isWorkingWithLocalCapAgency]);

  useEffect(() => {
    const validateCampaignID = async () => {
      const campaignIdRegex = /^\d{4}-\d{5}$/;
      if (campaignId?.match(campaignIdRegex)) {
        const response = await CampaignManager.validateCampaignID(campaignId);
        if (response?.Id) {
          return setFormValues({
            isCampaignIdValid: true,
            campaign: response.Id,
            leadSource: 'Field Marketing',
          });
        }
        Cookies.remove('campaign-id');
        Cookies.remove('campaign');
        Swal.fire({
          icon: 'error',
          title: 'Campaign Id not valid!',
        });
        return setFormValues({
          atAnEvent: 'No',
          campaignId: '',
          campaign: '',
          isCampaignIdValid: false,
          leadSource: '',
        });
      }
      return false;
    };
    validateCampaignID();
  }, [campaignId]);

  useEffect(() => {
    const { company } = getUserCookie();
    if (company === 'BA_PARTNERS') {
      setFormValues({
        leadSource: 'Partners',
      });
    }
  }, [leadSource]);

  return children;
};

export default LeadIntakeEffects;
