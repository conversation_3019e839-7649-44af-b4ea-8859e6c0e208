import {
  validateHeatingFuel,
  validateElectricProvider,
  validateGasProvider,
} from '@utils/leadIntake/leadintake';
import { validatePhoneNumber, validateEmail } from '@utils/functions';

const getLeadIntakeRequiredFields = (formValues = {}) => {
  const {
    atAnEvent,
    preferredLanguage,
    numUnitsSchedulingToday,
    singleOrMultiFamily,
    heaOrHvac,
    condoAssociation,
    electricProvider,
    heatingFuel,
    occupantType,
    customerFirstName,
    customerLastName,
    customerPrimaryPhoneNumber,
    customerPrimaryPhoneNumberType,
    customerAddress,
    customerEmail,
    emailOptOut,
    campaignId,
    isCampaignIdValid = false,
    callSource,
    leadSource,
    gasProvider,
    howManyUnitsAreInAssociation,
    discountedRateCodeForCIA,
    unitNumber,
  } = formValues;

  const validateParams = {
    numUnitsSchedulingToday,
    heatingFuel,
    electricProvider,
    gasProvider,
    heaOrHvac,
  };

  const heatingFuelValidation = () => {
    const valid = validateHeatingFuel(numUnitsSchedulingToday, heatingFuel);
    return valid;
  };

  const electricProviderValidation = () => {
    const valid = validateElectricProvider(validateParams);
    return valid;
  };

  const gasProviderValidation = () => {
    const valid = validateGasProvider(validateParams);
    return valid;
  };

  const requiredFields = {
    preliminaryQuestions: {
      'Campaign ID': atAnEvent === 'Yes' ? campaignId : true,
      'Preferred Language': preferredLanguage,
      'Single Or Multi Family': singleOrMultiFamily,
      'Hea Or Hvac': heaOrHvac,
      'Condo Association': condoAssociation,
      'Electric Provider': electricProviderValidation(),
      'Gas Provider': gasProviderValidation(),
      'Heating Fuel': heatingFuelValidation(),
      'Occupant Type': occupantType?.[0],
      'Condo Units Association': !(
        condoAssociation === 'Yes' && howManyUnitsAreInAssociation === '5+'
      ),
    },
    customerInfo: {
      'First Name': customerFirstName,
      'Last Name': customerLastName,
      'Primary Phone Number': validatePhoneNumber(customerPrimaryPhoneNumber),
      'Phone Type': customerPrimaryPhoneNumberType,
      Address: customerAddress,
      Email: emailOptOut ? true : validateEmail(customerEmail),
    },
    sourceInfo: {
      'Call Source': callSource,
      'Lead Source': leadSource,
      'Discounted Rate': heaOrHvac === 'HEA' ? discountedRateCodeForCIA : true,
    },
    review: {},
  };

  if (Number(numUnitsSchedulingToday) > 1) {
    for (let itr = 0; itr < Number(numUnitsSchedulingToday); itr++) {
      requiredFields.preliminaryQuestions[`Unit Number For Unit ${itr + 1} `] = unitNumber?.[itr];
      requiredFields.preliminaryQuestions[`Occupant Type For Unit ${itr + 1} `] =
        occupantType?.[itr];
      requiredFields.preliminaryQuestions[`Heating Fuel For Unit ${itr + 1} `] = heatingFuel?.[itr];
      requiredFields.preliminaryQuestions[`Electric Provider For Unit ${itr + 1} `] =
        electricProvider?.[itr];
      if (heatingFuel?.[itr] === 'Gas') {
        requiredFields.preliminaryQuestions[`Gas Provider For Unit ${itr + 1} `] =
          gasProvider?.[itr];
      }
    }
  }

  const requiredFieldsForBAs = {
    preliminaryQuestions: requiredFields.preliminaryQuestions,
    customerInfo: requiredFields.customerInfo,
    review: {},
    openSlots: {},
    wrapUp: {},
  };
  return isCampaignIdValid && atAnEvent === 'Yes' ? requiredFieldsForBAs : requiredFields;
};

const getLeadIntakeRequiredFieldsCT = (formValues = {}) => {
  const {
    hadPreviousAssessmentCT,
    // howLongAgoEnergyAssesmentConducted,
    // receivedFinancialAssistance,
    // programEnrolled,
    houseHoldSize,
    houseHoldMaxAnnualIncome,
    // openConstructionRemodeling,
    // anyMoldAsbestosORVermiculitePresent,
    singleOrMultiFamily,
    customerFirstName,
    customerLastName,
    customerPrimaryPhoneNumber,
    customerPrimaryPhoneNumberType,
    customerAddress,
    customerEmail,
    textMessageOptIn,
    emailOptOut,
    leadSourceCT,
    leadType,
    houseBuilt,
    occupantType,
    squareFeet,
    receivedFinancialAssistanceFromUtility,
    financialAssistanceFromState,
    openConstructionRemodeling,
    heatingFuelCT,
    electricProviderCT,
    leadSentToCompany,
    customerIEPreApproved,
    marketRateIncomeEligible,
    initialIEApplicationSentVia,
    helpOfferedForApplication,
    reasonOfNonAssistance,
    incomeFollowUpStatus,
    incomeFollowUpDate,
    incomeEligibleOrMarketRate,
  } = formValues;

  // check if customer is pre-approved
  const isPreApproved = Array.isArray(customerIEPreApproved)
    ? customerIEPreApproved[0] === true
    : customerIEPreApproved === true;

  // checking if customer has already qualified for IE through other questions
  const isAlreadyQualifiedForIE = Array.isArray(incomeEligibleOrMarketRate)
    ? ['Income Eligible', 'Application Sent To Customer'].includes(incomeEligibleOrMarketRate[0])
    : ['Income Eligible', 'Application Sent To Customer'].includes(incomeEligibleOrMarketRate);

  return {
    customerInfo: {
      'First Name': customerFirstName,
      'Last Name': customerLastName,
      Address: customerAddress,
      'Invalid Address': customerAddress?.displayAddress,
      Email: emailOptOut ? true : validateEmail(customerEmail),
      'Primary Phone Number': validatePhoneNumber(customerPrimaryPhoneNumber),
      'Phone Type': customerPrimaryPhoneNumberType,
      'Text Message Consent': textMessageOptIn,
      'Lead Sent to Company': leadSentToCompany,
    },
    preliminaryQuestions: {
      'Previous Energey Assesment Performed': hadPreviousAssessmentCT,
      'Single Multi Family': singleOrMultiFamily,
      'Year Built': houseBuilt,
      'Occupant Type': occupantType,
      'Square Feet': squareFeet,
      'Financial Assistance from Utility': customerIEPreApproved
        ? true
        : receivedFinancialAssistanceFromUtility,
      'Financial Assistance from State': customerIEPreApproved
        ? true
        : financialAssistanceFromState,
      'Open Construction/Remodeling': openConstructionRemodeling,
      'Heating Fuel': heatingFuelCT,
      'Electric Provider': electricProviderCT,
      'Market Rate/Income Eligible': isPreApproved ? true : marketRateIncomeEligible,
      'Initial IE application sent via':
        Array.isArray(marketRateIncomeEligible) &&
        [
          'Income Eligible Waiting On Application',
          'Income Eligible - Waiting on Approval',
          'Income Eligible',
        ].includes(marketRateIncomeEligible[0]) &&
        !isPreApproved
          ? initialIEApplicationSentVia
          : true,
      'Help Offered for Application?':
        Array.isArray(marketRateIncomeEligible) &&
        [
          'Income Eligible Waiting On Application',
          'Income Eligible - Waiting on Approval',
          'Income Eligible',
        ].includes(marketRateIncomeEligible[0]) &&
        !isPreApproved
          ? helpOfferedForApplication
          : true,
      'Reason of Non-Assistance':
        Array.isArray(marketRateIncomeEligible) &&
        [
          'Income Eligible Waiting On Application',
          'Income Eligible - Waiting on Approval',
          'Income Eligible',
        ].includes(marketRateIncomeEligible[0]) &&
        !isPreApproved
          ? reasonOfNonAssistance
          : true,
      'Income Follow-Up Status':
        Array.isArray(marketRateIncomeEligible) &&
        [
          'Income Eligible Waiting On Application',
          'Application Sent To Customer',
          'Moderate Income',
        ].includes(marketRateIncomeEligible[0])
          ? incomeFollowUpStatus
          : true,
      'Income Follow-Up Date':
        Array.isArray(marketRateIncomeEligible) &&
        [
          'Income Eligible Waiting On Application',
          'Income Eligible - Waiting on Approval',
          'Income Eligible',
        ].includes(marketRateIncomeEligible[0])
          ? incomeFollowUpDate
          : true,
      // Household Size - not required when customer is pre-approved or already qualified for IE
      'Household Size': isPreApproved || isAlreadyQualifiedForIE ? true : houseHoldSize?.[0],
      // Household Maximum Annual Income - not required when customer is pre-approved or already qualified for IE (field is hidden)
      'Household Maximum Annual Income':
        isPreApproved || isAlreadyQualifiedForIE ? true : houseHoldMaxAnnualIncome?.[0],
      // 'LAst energy Assessment Condicted': howLongAgoEnergyAssesmentConducted,
      // 'Financial Assistance': receivedFinancialAssistance,
      // 'Programmed Enrolled': programEnrolled,
      // 'Open Construction Remodeling': openConstructionRemodeling,
      // 'Mold, Asbestos or Vermiclite': anyMoldAsbestosORVermiculitePresent,
    },
    sourceInfo: {
      'Lead Source': leadSourceCT,
      'Lead Type': leadType,
    },
    review: {},
  };
};

export { getLeadIntakeRequiredFields, getLeadIntakeRequiredFieldsCT };
