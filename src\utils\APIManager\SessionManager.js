import { throwError } from '@utils/EventEmitter';
import axios from './utils/AxiosConfig';

const getCurrentUserInfo = async () => {
  const url = '/api/user/userInfo';
  const response = await axios.get(url);
  const { user } = response.data;
  return user;
};

const logOut = async () => {
  try {
    localStorage.removeItem('user');
    await axios.get('/api/user/logout');
    window.location.reload();
  } catch (error) {
    throwError({ message: 'Error logging out.' });
  }
};

export default {
  getCurrentUserInfo,
  logOut,
};
