import React, { useState } from 'react';
import { useSetRecoilState } from 'recoil';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import { ButtonContainer } from '@components/global/Form';
import { showSidebarState } from '@recoil/eventSidebar';

const FooterContainer = styled.nav`
  position: absolute;
  bottom: 0;
  padding: 6px 12px;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.secondary[200]};
`;

const DesktopFooterContainer = styled(FooterContainer)`
  ${({ theme }) => theme.screenSize.down(theme.breakpoints.laptopM)} {
    display: none;
  }
  width: ${({ $width }) => $width || '100%'};
`;

const MobileFooterContainer = styled(FooterContainer)`
  ${({ theme }) => theme.screenSize.up(theme.breakpoints.laptopM + 1)} {
    display: none;
  }
  ${({ theme }) => theme.screenSize.down(theme.breakpoints.mobileL)} {
    display: flex;
    flex-direction: column;
    height: auto;
    button {
      margin-bottom: 10px;
    }
  }
  width: ${({ $width }) => $width || '100%'};
`;

const ActionsMenu = styled.nav`
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 60px;
  right: 0;
  width: 100%;
  background-color: ${({ theme }) => theme.secondary[200]};
  padding: 6px 12px;
  button {
    width: 100%;
    margin: 6px auto;
  }
`;

const MobileFooter = ({ children = <></>, $width = '100%' }) => {
  const setShowSidebar = useSetRecoilState(showSidebarState);
  const [showActions, setShowActions] = useState(false);

  // This function is to handle how React passes children by default
  // Children will be nested for each html component
  // EX: 2 button wrapped in a React Fragment will send children as an Object with props.children
  // In the props.children will be the 2 buttons.
  // If 2 buttons are not wrapped in anything, React will pass the children as an Array with the 2 buttons
  const reduceChildren = (children) => {
    const nodes = [];
    if (!children) return nodes;
    if (children?.props?.children && Array.isArray(children.props.children))
      nodes.push(...reduceChildren(children.props.children));
    else if (Array.isArray(children))
      nodes.push(
        ...children.reduce((acc, curr) => {
          if (!curr) return acc;
          if (curr?.props?.children && Array.isArray(curr.props.children))
            acc.push(...reduceChildren(curr.props.children));
          else acc.push(curr);
          return acc;
        }, []),
      );
    else nodes.push(children);
    return nodes;
  };

  const buttons = reduceChildren(children);

  const toggleActions = () => {
    setShowActions(!showActions);
  };

  const closeSidebar = () => setShowSidebar(false);

  return (
    <MobileFooterContainer $width={$width}>
      <CancelButton left onClick={closeSidebar}>
        Close
      </CancelButton>
      {buttons?.length > 1 ? (
        <>
          <PrimaryButton right onClick={toggleActions}>
            {showActions ? 'Hide Actions' : 'Actions'}
          </PrimaryButton>
          {showActions && <ActionsMenu>{buttons}</ActionsMenu>}
        </>
      ) : (
        buttons
      )}
    </MobileFooterContainer>
  );
};

// Allow either buttons to be passed directly as children and put on the left side of the footer,
// Or pass buttons as leftButtons and rightButtons props
const EventSidebarFooter = ({
  children = null,
  leftButtons = null,
  rightButtons = null,
  $width = '100%',
}) => {
  return (
    <>
      <DesktopFooterContainer $width={$width}>
        {children && <ButtonContainer marginDirections={['right']}>{children}</ButtonContainer>}
        {leftButtons && (
          <ButtonContainer marginDirections={['right']}>{leftButtons}</ButtonContainer>
        )}
        {rightButtons && <ButtonContainer>{rightButtons}</ButtonContainer>}
      </DesktopFooterContainer>
      <MobileFooter $width={$width}>
        {children}
        {rightButtons}
        {leftButtons}
      </MobileFooter>
    </>
  );
};

EventSidebarFooter.propTypes = {
  children: PropTypes.node,
  leftButtons: PropTypes.node,
  rightButtons: PropTypes.node,
  $width: PropTypes.string,
};

MobileFooter.propTypes = {
  children: PropTypes.node,
  $width: PropTypes.string,
};

export default EventSidebarFooter;
