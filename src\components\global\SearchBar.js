import React from 'react';
import PropTypes from 'prop-types';
import { useRecoilState } from 'recoil';
import styled from 'styled-components';
import { Search } from '@styled-icons/boxicons-regular/Search';
import { searchAdminState } from '@recoil/admin';

const StyledSearchBar = styled.div`
  display: flex;
  width: 150%;
  margin-bottom: 8px;
  border: ${({ showBorder, theme }) => {
    return showBorder ? `solid 1px ${theme.secondary[200]}` : '';
  }};
  border-radius: ${({ showBorder }) => {
    return showBorder ? '5px' : '';
  }};
  margin-left: 15px;
`;

const SearchIconWrapper = styled.div`
  margin-left: 13px;
  margin-top: 9px;
  margin-bottom: 7px;
  color: ${({ theme }) => theme.secondary[500]};
`;

const SearchIcon = styled(Search)`
  color: ${({ theme }) => theme.secondary[500]};
`;

const SearchBox = styled.input`
  border: none;
  color: ${({ theme }) => theme.secondary[500]};
  width: 70%;
  border: solid 0.5px white;
  border-radius: 5px;
  &:focus {
    color: ${({ theme }) => theme.secondary[700]};
  }
`;

const SearchBar = ({ placeholder = '', showBorder = false }) => {
  const [searchTerm, setSearchTerm] = useRecoilState(searchAdminState);

  const handleSearchTermChange = (e) => {
    const { value: newSearchTerm } = e.target;
    setSearchTerm(newSearchTerm);
  };

  return (
    <StyledSearchBar showBorder={showBorder}>
      <SearchIconWrapper>
        <SearchIcon height={22} width={22} />
      </SearchIconWrapper>
      <SearchBox placeholder={placeholder} value={searchTerm} onChange={handleSearchTermChange} />
    </StyledSearchBar>
  );
};

SearchBar.propTypes = {
  showBorder: PropTypes.bool,
  placeholder: PropTypes.string,
};

export default SearchBar;
