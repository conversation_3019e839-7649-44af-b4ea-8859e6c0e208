import { selector } from 'recoil';
import availableAgentsSelector from '@recoil/agents/availableAgentsSelector';

const agentFormOptionsSelector = selector({
  key: 'agentFormOptions',
  get: ({ get }) => {
    // Get all agents for the business type
    const agents = get(availableAgentsSelector);

    return agents.map(({ displayName, oid, region }) => {
      return { key: displayName, value: oid, region };
    });
  },
});

export default agentFormOptionsSelector;
