import { selector } from 'recoil';

import { getAuthorizedDepartments } from '@utils/AuthUtils';
import { departmentsSelector } from '@recoil/app';

const authorizedDepartmentsSelector = selector({
  key: 'authorizedDepartmentsSelector',
  get: async ({ get }) => {
    const adminAuthDepartment = getAuthorizedDepartments();

    // Keep track of which departments the current user is authorized for in each state
    // example: { '00': [1, 6], '01': [6] }
    const authorizedDepartmentsForStatesMap = adminAuthDepartment.reduce(
      (acc, { departmentId, state }) => {
        if (!acc[departmentId]) acc[departmentId] = [];
        acc[departmentId].push(state);
        return acc;
      },
      {},
    );

    const allDepartments = get(departmentsSelector);

    const authorizedDepartmentsWithState = Object.keys(authorizedDepartmentsForStatesMap).reduce(
      (acc, authorizedDepartmentId) => {
        const departmentObject = allDepartments.find(({ value: departmentId }) => {
          return departmentId === Number(authorizedDepartmentId);
        });

        const statesForDepartment = authorizedDepartmentsForStatesMap[authorizedDepartmentId];
        statesForDepartment.forEach((state) => {
          acc.push({ ...departmentObject, state });
        });
        return acc;
      },
      [],
    );

    return authorizedDepartmentsWithState;
  },
});

export default authorizedDepartmentsSelector;
