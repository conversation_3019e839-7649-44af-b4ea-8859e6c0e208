import React from 'react';
import styled from 'styled-components';
import { formValuesState, activeFormState } from '@recoil/dataIntakeForm';
import { useRecoilState, useRecoilValue } from 'recoil';
import { calculateVentMathRound } from '@utils/functions';
import {
  VentilationNeeded,
  LowVentingOptions,
  VentilationRequired,
  HighVentingOptions,
  LowVenting,
  HighVenting,
} from './index';
import { ventingOptions } from '../consts';

const VentingCalculationContainer = styled.div`
  padding: 20px;
`;

const VentingContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
`;

const HighLowVentingContainer = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
`;

export const VentingCalculations = () => {
  const activeForm = useRecoilValue(activeFormState);
  const [formValuesList, setFormValues] = useRecoilState(formValuesState);
  const formValues = formValuesList[activeForm];
  const { atticSqFt, highVentingTotal, lowVentingTotal } = formValues;

  const calculateTotal = (parent, ratio, name, ventingname) => {
    const arr = [];
    ventingOptions[ventingname].forEach((option) => {
      if (name === option.fieldName) {
        arr.push(Number(ratio));
      } else {
        arr.push(Number(formValues[`${option.fieldName}_rt`]));
      }
    });
    const total = arr.reduce((a, b) => a + b);
    setFormValues({ [parent]: total, index: activeForm });
  };

  const handleVentingChange = (e) => {
    const {
      name,
      value,
      dataset: { price, parent, ventingname },
    } = e.target;
    const re = /^[0-9\b]+$/;
    if (value === '' || re.test(value)) {
      const ratio = Number(value) * Number(price);
      setFormValues({
        [name]: Number(value),
        [`${name}_rt`]: calculateVentMathRound(ratio),
        index: activeForm,
      });
      calculateTotal(parent, ratio, name, ventingname);
    }
  };

  return (
    <VentingCalculationContainer>
      <VentingContainer>
        <VentilationRequired atticSqFt={atticSqFt || 0} setFormValues={setFormValues} />
        <VentilationNeeded
          atticSqFt={atticSqFt}
          highVentingTotal={highVentingTotal}
          lowVentingTotal={lowVentingTotal}
        />
        <HighVenting handleVentingChange={handleVentingChange} formValues={formValues} />
        <LowVenting formValues={formValues} handleVentingChange={handleVentingChange} />
      </VentingContainer>
      <HighLowVentingContainer>
        <HighVentingOptions atticSqFt={atticSqFt} highVentingTotal={highVentingTotal} />
        <LowVentingOptions atticSqFt={atticSqFt} lowVentingTotal={lowVentingTotal} />
      </HighLowVentingContainer>
    </VentingCalculationContainer>
  );
};
