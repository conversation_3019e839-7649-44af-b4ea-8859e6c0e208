import React from 'react';
import DataIntakeFormPage from '@components/DataIntakeForm/components/DataIntakeFormPage';
import { cloneDeep } from 'lodash';
import { isAuthorized } from '@utils/AuthUtils';
import { leadIntakeMap, leadIntakeMapCT } from '../../FormSchema/leadIntakeMap';
import { ScheduleEvent } from '../../Slots/ScheduleEvent';
import { WrapUp } from '../../WrapUp';
import { leadIntakeTabs as intakeTabs, leadIntakeTabsCT as intakeTabsCT } from '../consts';

const fieldMapsAndTabsDict = {
  MA: { leadIntakeMap, intakeTabs },
  CT: { leadIntakeMap: leadIntakeMapCT, intakeTabs: intakeTabsCT },
};

export const useFieldsAndTabsForIntake = (
  intake = 'HEA',
  atAnEvent = false,
  isCampaignValid = false,
  leadId = false,
  isAuditorValueOnSf = false,
  nextTab,
  state = 'MA',
) => {
  const { intakeTabs, leadIntakeMap } = fieldMapsAndTabsDict[state];
  if (!intakeTabs && !leadIntakeMap) return [];
  const leadIntakeTabs = intakeTabs.map(({ name, title }) => {
    const isReviewSection = name === 'review';
    const isScheduleEventSection = name === 'scheduleEvent';
    const isWrapUpSection = name === 'wrapUp';
    const isSourceInfoTabAndLeadIsValid = leadId && name === 'sourceInfo';

    if (isScheduleEventSection && nextTab)
      return { name, title, component: <ScheduleEvent nextTab={nextTab} state={state} /> };

    if (isWrapUpSection) return { name, title, component: <WrapUp /> };
    const map = cloneDeep(leadIntakeMap[name]);
    if (isSourceInfoTabAndLeadIsValid && map) {
      const updatedFields = map?.fields?.map((field) => {
        if (
          (field?.name === 'referredByAuditor' || field?.name === 'leadSource') &&
          isAuditorValueOnSf
        ) {
          return { ...field, readOnly: true };
        }
        return field;
      });
      map.fields = updatedFields;
    }
    return {
      name,
      title,
      component: <DataIntakeFormPage map={map} readOnlyForReview={isReviewSection} />,
    };
  });

  const isBA = isCampaignValid && atAnEvent === 'Yes' && isAuthorized('Agent', 'Marketing', true);
  const isHVAC = intake === 'HVAC';
  switch (true) {
    case isBA:
      // If it's BrandAmbassador, remove Source Info Tab, because BrandAmbassador dont fill in Source Info Tab.
      leadIntakeTabs.splice(2, 1);

      if (isHVAC) {
        // If it's BrandAmbassador and Intake is HVAC, then remove Source Info and Add Home heating system after Customer Info Tab.
        leadIntakeTabs.splice(2, 0, {
          name: 'homeHeatingSystem',
          title: 'Home Heating System',
          component: <DataIntakeFormPage map={leadIntakeMap.homeHeatingSystem} />,
        });
      }
      break;

    // If it's not BrandAmbassador and Intake is HVAC, then Add Home heating system after Source Info Tab
    case !isBA && isHVAC:
      leadIntakeTabs.splice(3, 0, {
        name: 'homeHeatingSystem',
        title: 'Home Heating System',
        component: <DataIntakeFormPage map={leadIntakeMap.homeHeatingSystem} />,
      });
      break;

    // default Tabs for CIA's
    default:
      break;
  }
  return {
    leadIntakeTabs,
    leadIntakeMap,
  };
};
