import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Dollar as DollarIcon } from '@styled-icons/boxicons-regular/Dollar';

import { Header } from '@components/global';

const DashboardHeader = styled.div`
  display: flex;
  flex-direction: row;
  margin: 12px 12px;
  font-size: 24px;
  font-weight: 500;
  padding: 8px;
  color: #032d60;
  @media (max-width: 450px) {
    font-size: 20px;
    padding-bottom: 10px;
    align-self: flex-start;
  }
`;

const DollarIconStyle = styled(DollarIcon)`
  height: 35px;
  margin-right: 5px;
  margin-bottom: 2px;
  color: ${({ theme }) => theme.secondary[100]};
  background-color: #707172;
  padding: 4px;
  border-radius: 2px;
`;

const DashboardCard = styled.div`
  margin-bottom: 10px;
  border: 1px solid black;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.secondary[100]};
`;

const HeaderContainer = styled.div`
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: #f3f3f3;
  border-bottom: 1px solid #c9c9c9;
`;

const ReportMetricsContainer = styled.div`
  position: relative;
  overflow: visible;
  border-bottom: 1px solid #c9c9c9;
  padding-bottom: 0.75rem;
  display: flex;
  flex-direction: row;
`;

const TotalContainer = styled.div`
  width: 50%;
  padding: 8px;
  grid-column: ${({ column }) => {
    return column;
  }};
  grid-row: ${({ row }) => {
    return row;
  }};
`;

const WeeklyPayrollSummary = ({ details = [] }) => {
  return (
    <DashboardCard>
      <HeaderContainer>
        <DashboardHeader>
          <DollarIconStyle />
          Weekly Payroll Summary
        </DashboardHeader>
      </HeaderContainer>
      <ReportMetricsContainer>
        {Object.keys(details).map((key, index) => {
          return (
            <TotalContainer key={key} column={index + 1} row={0}>
              <Header h2>
                {details?.[key]?.title}: {details?.[key]?.value}
              </Header>
              {Object.keys(details?.[key]?.summary).map((summaryKey) => {
                const summary = details?.[key]?.summary?.[summaryKey];
                return (
                  <Header h4 key={summaryKey}>
                    {summary?.title}: {summary?.value}
                  </Header>
                );
              })}
            </TotalContainer>
          );
        })}
      </ReportMetricsContainer>
    </DashboardCard>
  );
};

WeeklyPayrollSummary.propTypes = {
  details: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      value: PropTypes.string,
      summary: PropTypes.arrayOf(PropTypes.shape({})),
    }),
  ),
};

export default WeeklyPayrollSummary;
