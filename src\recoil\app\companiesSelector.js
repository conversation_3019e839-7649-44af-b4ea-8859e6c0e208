import { selectorFamily } from 'recoil';
import { CompaniesManager } from '@utils/APIManager';

const companiesSelector = selectorFamily({
  key: 'companies',
  get: (state) => async () => {
    let companies = await CompaniesManager.getCompanies();
    companies = companies
      .filter(({ state: companyState }) => {
        return companyState === '*' || companyState === state;
      })
      .map((company) => {
        return { ...company, key: company.name, value: company.key };
      });
    return companies;
  },
});

export default companiesSelector;
