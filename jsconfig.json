{
  "compilerOptions": {
    // This must be specified if "paths" is set
    "baseUrl": ".",
    // Relative to "baseUrl"
    "paths": {
      "@assets/*": ["./src/assets/*"],
      "@components/*": ["./src/components/*"],
      "@config/*": ["./src/config/*"],
      "@contexts/*": ["./src/contexts/*"],
      "@utils/*": ["./src/utils/*"],
      "@style/*": ["./src/style/*"],
      "@recoil/*": ["./src/recoil/*"],
      "@hooks/*": ["./src/hooks/*"],
      "@pages/*": ["./src/pages/*"]
    }
  },
  "exclude": [
    ".git",
    ".app-cache",
    ".npm",
    ".npm-tmp",
    "dist",
    "dist*",
    "node_modules",
    "**/dist/*",
    "**/node_modules/*"
  ]
}
