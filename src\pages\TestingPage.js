import React, { useState } from 'react';
import moment from 'moment';
import { SyncAllButton } from '@components/global';
import axios from '@utils/APIManager/utils/AxiosConfig';
import styled from 'styled-components';
import { TempManager } from '@utils/APIManager';
import { displaySuccessMessage, startLoading, stopLoading, throwError } from '@utils/EventEmitter';
import { GoogleAddressInput } from '@components/global/Form';
import { parseGoogleAutocomplete } from '@utils/functions';

const Container = styled.div``;
const MainButton = styled.div`
  display: flex;
  flex-direction: column;
`;
const ButtonDiv = styled.div`
  margin: 15px;
  border: solid black 1px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: fit-content;
`;
const TestingButton = styled.button`
  margin: 20px;
`;

const TestingPage = () => {
  const [customerDealId, setCustomerDealId] = useState('');
  const [mismatchedEventType, setMismatchedEventType] = useState('');
  const [mismatchedEvents, setMismatchedEvents] = useState([]);

  // ADDRESS
  const [address, setAddress] = useState('');

  const findMismatchedVisits = async () => {
    const mismatchedEvents = await TempManager.findMismatchedVisits(mismatchedEventType);
    setMismatchedEvents(mismatchedEvents);
  };

  const ohcsSubmitData = async () => {
    try {
      const url = 'https://hosttestapi.homeworksenergy.com/ohcsSubmit';
      startLoading('Submitting data...');
      const data = await axios.post(url);
      stopLoading();
      console.log('SUBMIT DATA ==>', data);
      return displaySuccessMessage('Success! Check the browser console for details.');
    } catch (error) {
      console.log('Error ->', error);
      return displaySuccessMessage(`Request Failed! Please see error ----> ${error}`);
    }
  };

  const ohcsSyncLead = async () => {
    try {
      const url = 'https://hosttestapi.homeworksenergy.com/ohcssynconlinehvacevent';
      startLoading('Syncing lead...');
      const response = await axios.get(url);
      stopLoading();
      console.log('data is here!--->', response.data);
      return displaySuccessMessage(response.data);
    } catch (error) {
      console.log(error);
      return throwError(error);
    }
  };

  const insertCustomer = async () => {
    const url = `/api/customers/insertCustomer?accountId=${customerDealId}`;
    startLoading('Inserting Customer...');
    const response = await axios.get(url);
    stopLoading();
    console.log(response.data);
    if (response.data.error) return throwError(response.data.error);
    return displaySuccessMessage(response.data.message);
  };

  const createWxPayrollCsvFiles = async () => {
    try {
      const url = 'https://hosttestapi.homeworksenergy.com/createWxPayrollCsvFiles';
      startLoading('Creating WX Payroll CSV Files...');
      const data = await axios.get(url);
      stopLoading();
      console.log('WX Payroll CSV Files Created ==>', data);
      return displaySuccessMessage('Successfully created CSV Files for WX Payroll!');
    } catch (error) {
      console.log('Error ->', error);
      return displaySuccessMessage(`Request Failed! Please see error ----> ${error}`);
    }
  };

  const closeHolidays = async () => {
    try {
      const url = '/api/utility/closeAllHolidaysThisYear';
      startLoading('Closing Holidays...');
      await axios.post(url);
      stopLoading();
      return displaySuccessMessage('Successfully closed all holidays for this year!');
    } catch (error) {
      return displaySuccessMessage(`Request Failed! Please see error ---> ${error}`);
    }
  };

  const insertAddress = async () => {
    try {
      const url = '/api/addresses/insertAddress';
      startLoading('Inserting address...');
      const { data } = await axios.post(url, { address });
      if (data.error) throw Error(data.error);

      stopLoading();
      return displaySuccessMessage('Successfully inserted address!');
    } catch (error) {
      stopLoading();
      return displaySuccessMessage(`Request Failed! Please see error ---> ${error}`);
    }
  };

  // Used when you click an address in the google auto complete
  const handleGoogleLocationInput = (autocomplete) => {
    const updateObject = parseGoogleAutocomplete(autocomplete);
    setAddress(updateObject);
  };

  // Only used for address change for google auto complete
  const handleAddressChange = (address) => {
    setAddress(address);
  };

  // Utilization Report Testing
  const handleUtilizationReport = async () => {
    try {
      const url = '/api/reporting/generateUtilizationReport';
      startLoading('Generating Report...');
      const { data } = await axios.post(url, { address });
      if (data.error) throw Error(data.error);

      stopLoading();
      console.log('DATA HERE -->', data);
      return displaySuccessMessage('Successfully generated report!');
    } catch (error) {
      stopLoading();
      return displaySuccessMessage(`Request Failed! Please see error ---> ${error}`);
    }
  };

  return (
    <Container>
      <h2>WELCOME TO THE BEAUTIFUL PAGE OF BUTTONS!</h2>
      <MainButton>
        <ButtonDiv>
          <GoogleAddressInput
            title="Address"
            name="address"
            value={address}
            onPlaceChange={(autocomplete) => handleGoogleLocationInput(autocomplete)}
            onChange={handleAddressChange}
            uniqueId="address"
          />
          <TestingButton onClick={insertAddress} type="button">
            Insert address
          </TestingButton>
        </ButtonDiv>
        {/* TODO: does this work/is it still necessary? */}
        <ButtonDiv>
          <TestingButton onClick={createWxPayrollCsvFiles} type="button">
            Wx Payroll Report Data
          </TestingButton>
        </ButtonDiv>
        {/* TODO: does this work? */}
        <ButtonDiv>
          <TestingButton onClick={ohcsSubmitData} type="button">
            OHCSsendCustomerInfo
          </TestingButton>
        </ButtonDiv>

        {/* TODO: does this work? */}
        <ButtonDiv>
          <Container>Insert Customer</Container>
          <input
            onChange={(event) => setCustomerDealId(event.target.value)}
            value={customerDealId}
            id="customerDealId"
          />
          <TestingButton type="button" onClick={insertCustomer}>
            Submit
          </TestingButton>
        </ButtonDiv>
        {/* TODO: does this work? */}
        <ButtonDiv>
          <TestingButton type="button" onClick={ohcsSyncLead}>
            Sync Online HVAC Lead
          </TestingButton>
        </ButtonDiv>
        <ButtonDiv>
          <TestingButton type="button" onClick={closeHolidays}>
            Close All Holidays For This Year
          </TestingButton>
        </ButtonDiv>
        <ButtonDiv>
          <SyncAllButton startDate={moment()} />
        </ButtonDiv>
        <ButtonDiv>
          <TestingButton type="button" onClick={handleUtilizationReport}>
            Utilization Report
          </TestingButton>
        </ButtonDiv>
        <ButtonDiv>
          <select
            value={mismatchedEventType}
            onChange={(e) => setMismatchedEventType(e.target.value)}
          >
            <option value="">Select Event Type</option>
            <option value="0000">MA HEA</option>
            <option value="0100">CT HEA</option>
          </select>
          <TestingButton type="button" onClick={findMismatchedVisits}>
            Get Mismatched Visits
          </TestingButton>
          {mismatchedEvents.join(', ')}
        </ButtonDiv>
      </MainButton>
    </Container>
  );
};

export default TestingPage;
