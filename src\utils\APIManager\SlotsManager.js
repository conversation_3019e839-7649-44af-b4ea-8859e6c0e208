import moment from 'moment';
import {
  startLoading,
  stopLoading,
  throwError,
  eventChanged,
  displaySuccessMessage,
} from '@utils/EventEmitter';
import axios, { handleApiCall } from '@utils/APIManager/utils/AxiosConfig';
import { decodeEventType } from '@homeworksenergy/utility-service';

const getNextAvailableSlots = async (params) => {
  const url = '/api/slots/get';

  const { eventDuration } = params;
  let { numDays } = params;

  if (!numDays) {
    const isCap = params.type === '000006';
    const isHVAC = params.type.slice(0, 4) === '0004';
    const { state } = decodeEventType(params.type);

    // TODO: this logic is all duplicated in the useGetNextAvailableSlots hook
    // This should be refactored to one location
    const isCT = state === 'CT';

    if (isCT) numDays = 260;
    else if (isCap || isHVAC) numDays = 120;
    else numDays = 60;
  }

  const postObject = {
    ...params,
    startDate: moment(),
    eventDuration,
    numDays,
  };

  const response = await handleApiCall({
    url,
    method: 'post',
    params: postObject,
    loadingMessage: 'Finding available slots...',
  });

  if (!response) return false;

  return response;
};

const getAvailableUsersForReassign = async (params) => {
  const url = '/api/slots/getUsersForReassign';
  startLoading('Getting users for reassign...');
  const response = await axios.post(url, params);
  stopLoading();
  const { error } = response.data;
  if (error) throwError(error);
  return response.data;
};

const openAgents = async (params) => {
  const url = '/api/slots/openCrews';
  startLoading('Opening crew schedules...');
  const response = await axios.post(url, params);
  stopLoading();
  const { error } = response.data;
  if (error) return throwError(error);
  return displaySuccessMessage('Successfully opened crews.');
};

const closeAgents = async (params) => {
  const url = '/api/slots/closeCrews';
  startLoading('Closing crew schedules...');
  const response = await axios.post(url, params);
  stopLoading();
  const { error } = response.data;
  if (error) return throwError(error);
  return displaySuccessMessage('Successfully closed crews.');
};

const getSlotsForSwap = async (params) => {
  const url = '/api/slots/getSlotsForSwap';
  startLoading('Getting slots for swaps...');
  const response = await axios.post(url, params);
  stopLoading();
  const { error } = response.data;
  if (error) throwError(error);
  return response.data;
};

const updateCapacity = async (params) => {
  const url = '/api/slots/updateCapacity';

  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Updating Capacity...',
    successMessage: 'Successfully updated capacity.',
  });

  if (!response) return false;
  eventChanged();
  return true;
};

export default {
  getAvailableUsersForReassign,
  getNextAvailableSlots,
  getSlotsForSwap,
  openAgents,
  closeAgents,
  updateCapacity,
};
