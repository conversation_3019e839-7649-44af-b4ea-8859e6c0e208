import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput, FormTextBox } from '@components/global/Form';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const CAPHVACFailedSalesQC = ({ record = {} }) => {
  const {
    dealId,
    contractNumber,
    failedSalesQcNotes,
    seNeededNotes,
    roadblockNotes,
    siteid,
    isCap,
  } = record;

  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" isCap={isCap} />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput readOnly name="siteid" value={siteid} title="Site ID" placeholder="" />
          <FormInput
            readOnly
            name="contractNumber"
            value={contractNumber}
            title="Contract Number"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="failedSalesQcNotes"
            value={failedSalesQcNotes}
            title="Failed Sales QC Notes"
            placeholder=""
          />
        </Col>
      </Row>
      <FormTextBox
        readOnly
        name="roadblockNotes"
        value={roadblockNotes}
        title="Roadblocks Notes"
        placeholder=""
      />
      <FormTextBox
        readOnly
        name="seNeededNotes"
        value={seNeededNotes}
        title="SE Needed Notes"
        placeholder=""
      />
    </>
  );
};

CAPHVACFailedSalesQC.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    siteid: PropTypes.string,
    contractNumber: PropTypes.string,
    failedSalesQcNotes: PropTypes.string,
    seNeededNotes: PropTypes.string,
    roadblockNotes: PropTypes.string,
    isCap: PropTypes.string,
  }),
};

export default CAPHVACFailedSalesQC;
