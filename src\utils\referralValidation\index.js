import { validatePhoneNumber, validateEmail } from '@utils/functions';
import { throwError } from '@utils/EventEmitter';

const validateReferralDetails = (params, auditorEmployeeReferral) => {
  let copyParams = [...params];
  copyParams = copyParams.map((param, index) => {
    const { firstName, lastName, phone, email, crewLead } = param;
    const isFirstNameValid = firstName.length > 1;
    const isLastNameValid = lastName.length > 1;
    const isValidEmail = email ? validateEmail(email) : true;
    const isValidPhoneNumber = validatePhoneNumber(phone);
    const validateFields = {
      'First Name': isFirstNameValid,
      'Last Name': isLastNameValid,
      Email: isValidEmail,
      'Phone Number': isValidPhoneNumber,
      'Auditor Employee Referral': auditorEmployeeReferral,
    };
    const invalidParams = Object.keys(validateFields).filter((field) => {
      return !validateFields[field];
    });
    if (invalidParams.length)
      return throwError(`Invalid field Value. Please fill out ${invalidParams.join(', ')}`);
    const { value: crewLeadOid = null } = crewLead?.[0] || {};
    copyParams[index].crewLead = crewLeadOid;
    return { ...param, auditorEmployeeReferral };
  });

  return copyParams;
};

export default validateReferralDetails;
