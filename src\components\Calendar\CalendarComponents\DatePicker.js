/* eslint-disable react/jsx-wrap-multilines */
import React from 'react';
import PropTypes from 'prop-types';
import styled, { useTheme } from 'styled-components';
import moment from 'moment';
import { ChevronDown } from '@styled-icons/boxicons-regular';

import { Clickable } from '@components/global';
import ReactDatePicker from 'react-datepicker';

const StyledDatePicker = styled.div`
  display: flex;
  font-size: 16px;
`;

const Text = styled.div`
  display: flex;
  align-items: center;
  color: ${({ color }) => color};
  padding-right: 4px;

  svg {
    display: block;
  }
`;

const DatePicker = ({
  startDate = moment(),
  isMonthly,
  handleDateChange = () => {},
  showMonthYearPicker,
}) => {
  const theme = useTheme();
  let month = startDate.format('MMMM');
  if (!isMonthly) {
    const weekEndMonth = moment(startDate)
      .add(6, 'days')
      .format('MMMM');
    month += month !== weekEndMonth ? ` -${weekEndMonth}` : '';
  }

  return (
    <Clickable>
      <ReactDatePicker
        customInput={
          <StyledDatePicker>
            <Text color={theme.primary[500]}>{month}</Text>
            <Text color={theme.secondary[300]}>{startDate.format('YYYY')}</Text>
            <Text color={theme.secondary[500]}>
              <ChevronDown size={16} />
            </Text>
          </StyledDatePicker>
        }
        selected={startDate.toDate()}
        onChange={(date) => handleDateChange(null, date)}
        dateFormat="M/dd/yy"
        showMonthYearPicker={showMonthYearPicker}
      />
    </Clickable>
  );
};

DatePicker.propTypes = {
  startDate: PropTypes.instanceOf(moment),
  isMonthly: PropTypes.bool.isRequired,
  handleDateChange: PropTypes.func,
  showMonthYearPicker: PropTypes.bool.isRequired,
};

export default DatePicker;
