import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue } from 'recoil';

import { SecondaryButton } from '@components/global/Buttons';
import {
  Row,
  Col,
  FormSelect,
  FormRadioButtons,
  handleFormFieldChange,
  FormTextBox,
} from '@components/global/Form';
import { Checkbox } from '@components/global';

import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import EventSidebarHeader, {
  HeaderLabel,
  HeaderTitle,
} from '@components/EventSidebar/EventSidebarHeader';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';

import { agentsFormOptionsSelector } from '@recoil/agents';
import { selectedEventState } from '@recoil/eventSidebar';

import { rescheduleReasons } from '@utils/businessLogic/hvacSalesBusinessLogic';

const HVACSalesRescheduleForm = ({ handleRescheduleClick, handleFindSlotsClick }) => {
  const [rescheduleType, setRescheduleType] = useState('');
  const agents = useRecoilValue(agentsFormOptionsSelector);
  const [event, setEvent] = useRecoilState(selectedEventState);

  const {
    address,
    rescheduleReason,
    notes: { rescheduleNotes },
    includeAgents,
    createMileageEvent,
  } = event;

  const handleRescheduleTypeChange = (event) => {
    const { value } = event.target;
    resetSlots();
    setRescheduleType(value);
  };

  const handleChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  const onViewSlotsButtonClick = async () => {
    await handleFindSlotsClick(rescheduleType === 'past');
  };

  const resetSlots = () =>
    setEvent({ ...event, date: null, startTime: null, endTime: null, oid: null });

  const rescheduleTypeOptions = [
    {
      key: 'Now',
      value: 'now',
      description: 'Reschedule now will load future slots.',
    },
    {
      key: 'Later',
      value: 'later',
      description:
        'Reschedule later will not load slots and this is used to cancel a visit \n with the intent to schedule it again later.',
    },
  ];

  const renderSelectOptions = () => (
    <Row>
      <Col>
        <FormSelect
          required
          title="Reason For Reschedule"
          name="rescheduleReason"
          value={rescheduleReason}
          options={rescheduleReasons}
          onChange={handleChange}
        />
      </Col>
      {rescheduleType && ['now', 'past'].includes(rescheduleType) && (
        <Col>
          <FormSelect
            title="Include Agent"
            name="includeAgents"
            value={includeAgents}
            options={agents}
            onChange={handleChange}
            disablePlaceholder={false}
          />
        </Col>
      )}
    </Row>
  );

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col size={2}>
            <HeaderLabel>Reschedule HEA:</HeaderLabel>
            <HeaderTitle>{address?.displayAddress || 'No Address Available'}</HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            <FormRadioButtons
              name="rescheduleType"
              title="Reschedule HVAC Sales"
              options={rescheduleTypeOptions}
              showDescription
              value={rescheduleType}
              onChange={handleRescheduleTypeChange}
            />

            {rescheduleType && renderSelectOptions()}

            <FormTextBox
              required // TODO: should this be required?
              title="notes"
              placeholder="Enter Notes"
              onChange={handleChange}
              name="notes.rescheduleNotes"
              value={rescheduleNotes || ''} // '`value` prop on `textarea` should not be null. Consider using an empty string to clear the component'
            />

            <Checkbox
              label="Create Mileage Event?"
              name="createMileageEvent"
              value={createMileageEvent}
              onChange={handleChange}
            />
            {rescheduleType &&
              (['now', 'past'].includes(rescheduleType) ? (
                <>
                  <AvailableSlots />
                  <BookSlotsButton
                    handleBookSlots={() => handleRescheduleClick(rescheduleType)}
                    allowAgentSelect={false}
                  />
                </>
              ) : (
                <SecondaryButton
                  center
                  onClick={() => {
                    handleRescheduleClick(rescheduleType);
                  }}
                >
                  Reschedule Later
                </SecondaryButton>
              ))}
          </Col>
        </Row>
      </EventSidebarBody>
      {rescheduleType && ['now', 'past'].includes(rescheduleType) && (
        <EventSidebarFooter>
          <SecondaryButton left onClick={onViewSlotsButtonClick}>
            View Available Slots
          </SecondaryButton>
        </EventSidebarFooter>
      )}
    </SidebarForm>
  );
};

HVACSalesRescheduleForm.propTypes = {
  handleRescheduleClick: PropTypes.func.isRequired,
  handleFindSlotsClick: PropTypes.func.isRequired,
};

export default HVACSalesRescheduleForm;
