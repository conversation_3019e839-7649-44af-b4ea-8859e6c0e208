import React, { useEffect } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import {
  handleFormFieldChange,
  Row,
  Col,
  FormSelect,
  FormRadioButtons,
  FormCheckboxes,
} from '@components/global/Form';

import { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';

import { PrimaryButton } from '@components/global/Buttons';
import { programOptions } from '@utils/constants';

import { capHeaResultingQuestionsState } from '@recoil/docRepo';
import { DocRepoManager } from '@utils/APIManager';
import eventValidation from '@utils/eventValidation';
import Swal from 'sweetalert2/dist/sweetalert2';

const HorizontalDivider = styled.hr`
  margin-top: 1px;
  margin-bottom: 3px;
`;

const HeaderInfoTextStyle = styled(HeaderLabel)`
  cursor: pointer;
  font-size: x-large;
  margin-bottom: 30px;
`;

const ResultingQuestionsHEA = ({ uniqueId, department, setShowResulting }) => {
  const event = useRecoilValue(capHeaResultingQuestionsState);
  const [resulting, setResulting] = useRecoilState(capHeaResultingQuestionsState);
  const { visitResult, ampRun, electricProvider, largeAppliances, lta } = resulting;

  const showElectricProvider = event?.fuelType !== 'electric' && event?.program === 'NG';

  useEffect(() => {
    if (event?.fuelType === 'electric' && event?.program === 'NG')
      setResulting({ ...resulting, electricProvider: 'NG' });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const visitResultOptions = [
    { key: 'Qualified Out', value: 'Qualified Out' },
    { key: 'Closed Won', value: 'Closed Won' },
    { key: 'High Prob', value: 'High Prob' },
    { key: 'Pre-Weatherization Barrier', value: 'Pre-Weatherization Barrier' },
    { key: 'Not in EM Home', value: 'Not in EM Home' },
  ];

  const yesNoOptions = [
    { key: 'Yes', value: 'yes' },
    { key: 'No', value: 'no' },
  ];

  const dependentQuestionsDict = {
    visitResult: {
      ampRun: {
        title: 'Did you run an AMP?',
        onChange: (event) => handleDependentQuestionsValueChange(event),
      },
      atticWork: { title: 'Is there attic work?', onChange: (event) => handleFieldChange(event) },
      knt: {
        title: 'Did you find Knob & Tube?',
        onChange: (event) => handleFieldChange(event),
      },
      lta: {
        title: 'Do you have an LTA or Waiver signed?',
        onChange: (event) => handleVisitResultNLTAChange(event),
      },
    },
    ampRun: {
      largeAppliances: {
        title: 'Did you recommend any large appliances?',
        onChange: (event) => handleDependentQuestionsValueChange(event),
      },
    },
    largeAppliances: {
      recommendFridge: {
        title: 'Did you recommend a Fridge?',
      },
      recommendFreezer: {
        title: 'Did you recommend a Freezer?',
      },
      recommendWasher: {
        title: 'Did you recommend a Washer?',
      },
      recommendDehumidifier: {
        title: 'Did you recommend a Dehumidifier?',
      },
      recommendWindowAcUnit: {
        title: 'Did you recommend a Window AC Unit?',
      },
    },
  };

  const handleVisitResultNLTAChange = (event) => {
    const { name, value } = event.target;
    const visitResultValue = name === 'visitResult' ? value : visitResult;
    const ltaValue = name === 'visitResult' ? lta : value;
    if (visitResultValue === 'Closed Won' && ltaValue === 'no') {
      Swal.fire({
        icon: 'error',
        title: "Can't Result this visit as Closed Won When LTA value is selected as No.",
      });
    } else handleFieldChange(event);
  };

  const handleFieldChange = (e, updatedResulting = resulting) => {
    return handleFormFieldChange(e, updatedResulting, setResulting);
  };

  const handleSaveClick = async () => {
    const updateResulting = eventValidation.MA.HEA.capDocRepoResulting(resulting, department);
    if (updateResulting) {
      const response = await DocRepoManager.updateDocRepoResultingQuestions({
        state: 'MA',
        department,
        uniqueId,
        updateResulting,
      });
      if (response) {
        setResulting({ ...resulting, uniqueId });
        setShowResulting(false);
      }
    }
  };

  const handleDependentQuestionsValueChange = (event) => {
    const { name, value } = event.target;
    const updateResulting = { [name]: value };
    const setFieldValueToNull = (dict, field) => {
      Object.keys(dict[field]).forEach((key) => {
        updateResulting[key] = null;
        if (dependentQuestionsDict[key]) setFieldValueToNull(dependentQuestionsDict, key);
      });
    };
    if (value === 'no') setFieldValueToNull(dependentQuestionsDict, name);
    setResulting({ ...resulting, ...updateResulting });
  };

  const renderDependentQuestions = (formField) => {
    return Object.keys(dependentQuestionsDict[formField]).map((key) => {
      const { title, onChange: onFieldValueChange } = dependentQuestionsDict[formField][key];
      return (
        <>
          <Row key={title}>
            <FormRadioButtons
              name={key}
              required
              title={title}
              column={false}
              maxGap
              horizontal
              options={yesNoOptions}
              value={resulting[key]}
              onChange={onFieldValueChange || handleFieldChange}
            />
          </Row>
          <HorizontalDivider />
        </>
      );
    });
  };

  return (
    <>
      <EventSidebarBody>
        <Row>
          <HeaderInfoTextStyle>
            Appliance Visit Performed : {resulting.appliancePerformed || '-'}
          </HeaderInfoTextStyle>
        </Row>
        <HorizontalDivider />
        <Row key="visitResult">
          <Col>
            <FormSelect
              required
              title="HEA Visit Result"
              placeholder="Select Visit Result"
              name="visitResult"
              value={visitResult}
              onChange={handleVisitResultNLTAChange}
              options={visitResultOptions}
            />
          </Col>
        </Row>
        <HorizontalDivider />
        {visitResult && (
          <>
            {renderDependentQuestions('visitResult')}
            {ampRun === 'yes' && (
              <>
                {renderDependentQuestions('ampRun')}
                {largeAppliances === 'yes' && renderDependentQuestions('largeAppliances')}
                {showElectricProvider && (
                  <>
                    <Row size={1}>
                      <FormCheckboxes
                        title="Electric Provider?"
                        name="electricProvider"
                        options={programOptions}
                        value={electricProvider}
                        onChange={handleFieldChange}
                        border
                      />
                    </Row>
                    <HorizontalDivider />
                  </>
                )}
              </>
            )}
          </>
        )}
      </EventSidebarBody>
      {visitResult && (
        <EventSidebarFooter
          leftButtons={
            <PrimaryButton onClick={() => handleSaveClick()}>Save Resulting</PrimaryButton>
          }
        />
      )}
    </>
  );
};

ResultingQuestionsHEA.propTypes = {
  uniqueId: PropTypes.string.isRequired,
  department: PropTypes.string.isRequired,
  setShowResulting: PropTypes.func.isRequired,
};

export default ResultingQuestionsHEA;
