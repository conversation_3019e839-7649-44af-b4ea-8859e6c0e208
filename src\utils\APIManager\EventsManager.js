import swal from 'sweetalert2/dist/sweetalert2';
import moment from 'moment';
import utilityService from '@homeworksenergy/utility-service';
import { displaySuccessMessage, eventChanged, updateAllEvents } from '@utils/EventEmitter';
import eventValidation from '@utils/eventValidation';

import { hasRole } from '@utils/AuthUtils';
import { handleApiCall } from '@utils/APIManager/utils/AxiosConfig';

/**
 * Fetch events from the database
 *
 * @param {*} oids {optional} Specify the agents that you want to fetch events for. If not set, grabs all users with capabilities matching the first 4 digits of the specified eventType
 * @param {*} type The type of events you are looking to fetch. Uses first 4 digits. If no oids, grabs all oids for that type. Also used for sorting based on department specific business logic
 * @param {moment} loadStart date to start event search
 * @param {moment} loadEnd date to end event search
 * @returns
 */
const loadEvents = async (loadStart, loadEnd, type, oids) => {
  const startDate = loadStart.format('YYYY-MM-DD');
  const endDate = loadEnd.format('YYYY-MM-DD');
  const url = '/api/events/get';
  const params = { startDate, endDate, type, oids };

  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Loading events...',
  });
  if (!response) return [];
  const { rows, allEvents } = response;

  updateAllEvents(allEvents);
  return rows;
};

const getEventsWithIds = async ({ id, ids }) => {
  const url = '/api/events/getEventsWithIds';
  const params = { id, ids };

  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Loading visit info...',
  });
  if (!response) return null;

  return response;
};

const reassignEvent = async (eventParams) => {
  const url = '/api/events/reassign';
  const { date, time } = getSwalMessageParams(eventParams);
  const { agentName, ...params } = eventParams;
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Reassigning event...',
    successMessage: `Successfully reassigned event to ${agentName} on ${date} at ${time}.`,
  });

  if (!response) return false;
  const { event } = response;
  eventChanged();

  return event;
};

const swapEvent = async ({ selectedEvent, swapEvent, type }) => {
  const { agentName } = swapEvent;

  const params = { selectedEvent, swapEvent, type };

  const { value: confirmed } = await swal.fire({
    title: `Are you sure you want to swap this appointment with: ${agentName}`,
    confirmButtonText: 'Yes',
    showCancelButton: true,
    cancelButtonText: 'No',
  });
  if (!confirmed) return false;

  const url = '/api/events/swap';
  const successMessage = params?.swapEvent?.displayName
    ? `Successfully swapped events with ${params?.swapEvent?.displayName} on ${params?.selectedEvent
        ?.date || 'Date Not Found'} at ${params?.selectedEvent?.startTime || 'Time Not Found'}`
    : 'Successfully swapped events.';
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Swapping event...',
    successMessage,
  });
  if (!response) return false;

  const { success } = response;
  eventChanged();
  return success;
};

const rescheduleEvent = async (eventParams) => {
  const { type } = eventParams;
  const { date, time } = getSwalMessageParams(eventParams);
  const { agentName, ...params } = eventParams;
  const { state, business: department } = utilityService.decodeEventType(type);
  // HVAC backwards compatibility, insulation First do validation that the event is okay to be rescheduled (multi day same week)
  // TODO: we can get the department inside an eventvalidation function rather than need to calculate it every time
  const postObject =
    department === 'Insulation' ? eventValidation[state][department].reschedule(params) : params;

  if (!postObject) return false;

  const url = '/api/events/reschedule';

  const response = await handleApiCall({
    url,
    method: 'post',
    params: postObject,
    loadingMessage: 'Rescheduling event...',
    successMessage: `Successfully rescheduled event on ${date} at ${time}, assigned to ${agentName}.`,
  });
  if (!response) return false;

  const { event } = response;
  eventChanged();
  return event;
};

const cancelEvent = async (params) => {
  const { id, associatedEventsId, cancelReason, notes, createMileageEvent, agentName } = params;

  const url = '/api/events/cancel';
  const { date, time } = getSwalMessageParams(params);

  console.log(date, time, agentName);

  const response = await handleApiCall({
    url,
    method: 'post',
    params: { id, associatedEventsId, cancelReason, notes, createMileageEvent },
    loadingMessage: 'Canceling event...',
    successMessage: 'Successfully canceled event',
    // TODO: this is broken, figure out why
    // successMessage: `Successfully canceled event assigned to ${params.agentName} on ${date} at ${time}.`,
  });
  if (!response) return false;

  const { event } = response;
  eventChanged();
  return event;
};

/**
 *
 * @param {*} slots list of slots that should be booked.
 * Example:
 * [{
 *  oids: 'EXAMPLE_OID', // the oids of the crews/users to be booked
 *  type: '000000', // the 6 character type for the event to be booked
 *  address: '1600 Pennsylvania Avenue Washington D.C.', // Address for drivetimes?
 *  numUnit: 1, // number of units in the house
 *  startTime: '08:00:00',
 *  endTime: '16:00:00',
 *  date: ''2020-12-21T05:00:00.000Z''
 *  notes: {
 *    officeNotes: 'notes for office',
 *    fieldNotes: 'notes for crew'
 *  },
 *  sfIds: { // some or all of these depending on the event type
 *    accountId: '', // customer's sf account id
 *    dealId: '',
 *    contractId: '',
 *    workOrderId: '',
 *    eventId: '', // sf activity event id
 *  },
 *  eventName, // typically just used for custom blocks
 *  shadow, // typically false, only for custom blocks that can be scheduled over
 * },
 * ...]
 */
const create = async (params) => {
  const url = '/api/events/create';

  const isMultiSlot = Array.isArray(params);
  const objectParams = isMultiSlot ? params[0] : params;

  const { type, agentName, startEndTimes, isHESSchedulingHVAC = false } = objectParams;

  const isHVACInstall = type.slice(0, 4) === '0004';
  const showEventDetails = !isHVACInstall && !isHESSchedulingHVAC;
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: `Booking appointment${isMultiSlot ? 's' : ''}...`,
  });

  if (!response) return false;
  eventChanged();

  const { state, business: department } = utilityService.decodeEventType(type);

  // Extra confirmation for HESs
  const hasExternalSchedulerRole = hasRole('External Scheduler', department) && state === 'MA';
  if (hasExternalSchedulerRole) {
    swal.fire({
      title: 'Successfully created event\n\nReminders',
      confirmButtonText: 'OK',
      icon: 'success',
      html:
        '<div style="text-align:left"></br><ul><li>Arrival Window 9-10 am</li></br><li>Do not run a fireplace or a wood-burning stove 24 hours prior to the install</li></br><li>The crew will need parking for a 20-foot box truck</li></br><li>The scheduling team will reach out 1 week prior to confirm the visit.</li></br></ul></div>',
    });
    return true;
  }
  const { date, time } = getSwalMessageParams({ startEndTimes });
  const successMessage = !showEventDetails
    ? 'Successfully created event'
    : `Successfully created event on ${date} at ${time}, assigned to ${agentName}.`;
  displaySuccessMessage(successMessage);
  return true;
};

const update = async (eventParams) => {
  const { agentName, ...params } = eventParams;
  const response = await handleApiCall({
    url: '/api/events/updateEvent',
    method: 'post',
    params,
    loadingMessage: 'Updating event...',
    successMessage: 'Successfully updated event',
  });
  if (!response) return false;

  eventChanged();
  return true;
};

const saveNotes = async (params) => {
  const response = await handleApiCall({
    url: '/api/events/saveNotes',
    method: 'post',
    params,
    loadingMessage: 'Saving notes...',
    successMessage: 'Successfully saved notes',
  });
  if (!response) return false;
  eventChanged();
  return true;
};

const updateNeedsDocs = async (status, sfField, sfObjectName, uniqueId) => {
  const body = {
    status,
    sfField,
    sfObjectName,
    uniqueId,
  };
  const url = '/api/events/updateNeedsDocs';
  const response = await handleApiCall({ url, method: 'post', body });
  if (!response) return false;

  return true;
};

const searchEvents = async ({ searchTerm, departmentEventTypes, fieldsToSearch }) => {
  const params = { searchTerm, departmentEventTypes, fieldsToSearch };
  const query = new URLSearchParams(params).toString();

  const url = `/api/events/search?${query}`;
  const response = await handleApiCall({ url, method: 'get', loadingMessage: 'Searching...' });
  if (!response) return false;
  const { searchResults, searchResultsMap } = response;

  updateAllEvents(searchResultsMap);

  return searchResults;
};

const rescheduleLater = async (params) => {
  const url = '/api/events/rescheduleLater';

  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Reschedule later...',
    successMessage: 'Successfully rescheduled event.',
  });
  if (!response) return false;

  const { event } = response;
  eventChanged();
  return event;
};

const getSwalMessageParams = ({ startEndTimes }) => {
  const statTime = startEndTimes[0].start;
  const date = moment(statTime).format('L');
  const time = moment(statTime).format('LT');
  return { date, time };
};

const loadPartnerEvents = async (params) => {
  const url = '/api/partnerEvent/get';
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Loading events...',
  });
  if (!response) return [];
  return response;
};

const createPartnerEvent = async (params) => {
  const url = '/api/events/createPartnerEvent';
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Adding Job...',
  });

  if (!response) return false;
  displaySuccessMessage('Successfully created event');
  return true;
};

const cancelPartnerEvent = async (params) => {
  const { id } = params;

  const url = '/api/events/cancelPartnerEvent';

  const response = await handleApiCall({
    url,
    method: 'post',
    params: { id },
    loadingMessage: 'Canceling event...',
    successMessage: 'Successfully canceled event',
  });
  if (!response) return false;

  const { event } = response;
  return event;
};

const updatePartnerEvent = async (params) => {
  const url = '/api/events/updatePartnerEvent';
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Updating Job...',
  });

  if (!response) return false;
  await loadPartnerEvents();
  displaySuccessMessage('Successfully updated event');
  return true;
};

const getEventHistory = async (params) => {
  const url = `/api/events/history/${params}`;
  const response = await handleApiCall({
    url,
    method: 'get',
    loadingMessage: 'Fetching Event History...',
  });
  if (!response) return false;
  return response;
};

export default {
  loadEvents,
  getEventsWithIds,
  create,
  update,
  rescheduleEvent,
  reassignEvent,
  cancelEvent,
  saveNotes,
  updateNeedsDocs,
  searchEvents,
  rescheduleLater,
  swapEvent,
  loadPartnerEvents,
  createPartnerEvent,
  cancelPartnerEvent,
  updatePartnerEvent,
  getEventHistory,
};
