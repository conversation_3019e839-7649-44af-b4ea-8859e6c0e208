import React from 'react';
import { useSetRecoilState } from 'recoil';
import { showSidebarState, isLegendViewAtom } from '@recoil/eventSidebar';
import styled from 'styled-components';

const LegendButton = styled.button`
  background: ${({ theme }) => theme.secondary[100]};
  border: 1px solid ${({ theme }) => theme.colors.eventGreen};
  border-radius: 4px;
  color: ${({ theme }) => theme.colors.eventGreen};
  padding: 0 20px;
  height: 30px;
`;

const Legend = () => {
  const setShowSidebar = useSetRecoilState(showSidebarState);
  const setIsLegendView = useSetRecoilState(isLegendViewAtom);

  const handleClick = () => {
    setShowSidebar(true);
    setIsLegendView(true);
  };

  return <LegendButton onClick={handleClick}>Legend</LegendButton>;
};

export default Legend;
