import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import moment from 'moment';
import DatePicker from 'react-datepicker';

const TitleHeading = styled.h4`
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 5px;
  text-transform: uppercase;
  color: ${({ theme }) => theme.secondary[500]};
`;

const DatePickerBodyWrapper = styled.div`
  padding-bottom: 10px;
  & .react-datepicker-wrapper {
    width: 100%;
    & .react-datepicker__input-container {
      display: block;
      & input {
        width: 100%;
        min-height: 32px;
        background: ${({ theme }) => theme.secondary[100]};
        border: 1px solid ${({ theme }) => theme.secondary[300]};
        box-sizing: border-box;
        border-radius: 4px;
      }
    }
  }
  & .react-datepicker--time-only {
    font-size: 14px;
  }
`;

const FormDateTimePicker = (
  {
    title,
    name,
    value,
    minTime = moment('00:01:00', 'h:mm:ss aa'),
    maxTime = moment('23:59:00', 'h:mm:ss aa'),
    onChange,
    placeholder = '',
    dateFormat = 'MMMM d, yyyy h:mm aa',
    timeIntervals = 15,
    timeCaption = 'Time',
    timeFormat = 'h:mm aa',
    displayTimeFormat = timeFormat,
    useSyntheticEvent = false,
    allowDateSelect = false,
  },
  ...props
) => {
  const handleTimeChange = (newTime) => {
    if (!newTime) return null;
    if (!useSyntheticEvent) return onChange(newTime);
    const value = moment(newTime)
      .startOf('minute')
      .format(timeFormat);
    const syntheticEvent = {
      target: {
        name,
        value,
      },
    };
    return onChange(syntheticEvent);
  };

  return (
    <DatePickerBodyWrapper>
      <TitleHeading> {title} </TitleHeading>
      <DatePicker
        name={name}
        selected={value.toDate()}
        showTimeSelect
        showTimeSelectOnly={!allowDateSelect}
        timeFormat={displayTimeFormat}
        minTime={minTime.toDate()}
        maxTime={maxTime.toDate()}
        onChange={(newTime) => handleTimeChange(newTime)}
        timeIntervals={timeIntervals}
        timeCaption={timeCaption}
        dateFormat={dateFormat}
        placeholderText={placeholder}
        {...props}
      />
    </DatePickerBodyWrapper>
  );
};

FormDateTimePicker.propTypes = {
  title: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  value: PropTypes.instanceOf(moment).isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  dateFormat: PropTypes.string,
  timeIntervals: PropTypes.number,
  timeCaption: PropTypes.string,
  displayTimeFormat: PropTypes.string,
  timeFormat: PropTypes.string,
  minTime: PropTypes.instanceOf(moment),
  maxTime: PropTypes.instanceOf(moment),
  useSyntheticEvent: PropTypes.bool,
  allowDateSelect: PropTypes.bool,
};

export default FormDateTimePicker;
