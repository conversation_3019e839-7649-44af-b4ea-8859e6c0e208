import React from 'react';
import PropTypes from 'prop-types';

import { ScrollableList } from '@components/global/ScreenPartitionView';
import AgentCard from './AgentListCard';

const AgentList = ({ agents }) => {
  return (
    <ScrollableList>
      {agents.map((agent) => {
        return (
          <AgentCard
            key={`${agent.oid}-${agent.departmentName}-${agent.state}`}
            agent={agent}
            showDepartment
            showCompany
          />
        );
      })}
    </ScrollableList>
  );
};

AgentList.propTypes = {
  agents: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
};

export default AgentList;
