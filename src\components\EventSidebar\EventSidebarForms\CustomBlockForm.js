import React, { useState, useEffect, useRef, Suspense } from 'react';

import PropTypes from 'prop-types';
import moment from 'moment';
import { useRecoilState, useRecoilValue } from 'recoil';
import Swal from 'sweetalert2/dist/sweetalert2';

import { parseGoogleAutocomplete } from '@utils/functions';

import { useStartEndTimes } from '@hooks';

import { selectedEventState } from '@recoil/eventSidebar';
import { agentsFormOptionsSelector } from '@recoil/agents';
import { calendarTypeAtom } from '@recoil/app';

import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import {
  Row,
  Col,
  FormInput,
  FormInfo,
  FormInfoField,
  FormTextBox,
  handleFormFieldChange,
  FormStartEndDateTimePickers,
  FormFieldContainer,
  FormMultiselect,
  AddRemoveButtonContainer,
  FormAddRemoveDayButtons,
  GoogleAddressInput,
  BackButton,
} from '@components/global/Form';

import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';

import { isAuthorized, isDeptPartner } from '@utils/AuthUtils';
import { Checkbox } from '@components/global';

const CustomBlockForm = ({ handleCancelClick, handleSaveClick }) => {
  const prevState = useRef();
  const handleTimeChange = useStartEndTimes();
  const [event, setEvent] = useRecoilState(selectedEventState);
  const calendarType = useRecoilValue(calendarTypeAtom);
  const [addressInvalid, setAddressInvalid] = useState(false);

  const {
    id,
    eventName,
    jobLength,
    notes,
    shadow,
    oids,
    date,
    patternName,
    address,
    startEndTimes,
    scheduledBy,
    scheduledDate,
  } = event;

  useEffect(() => {
    // On selection of Address other fields are set to their default value
    // To preserve the original state values, we need to store it in a useRef variable
    // TODO: Find the cause of this issue and remove the useRef
    prevState.event = event;

    // For booking custom blocks from HEA find available slots
    // Since you don't click on a calendar cell, there would be no date
    // Default to today
    if (!date) setEvent({ ...event, date: moment().format('MM/DD/YYYY') });
  }, [date, event, setEvent]);

  const isPartner = isDeptPartner();
  const isCreate = !id;
  const canEdit = isAuthorized('Scheduler', 'All') || isPartner;
  const isHesCalendar = calendarType.slice(0, 4) === '0000';
  const isHVACInstallCalendar = calendarType.slice(0, 4) === '0004';
  const showBackButton = isCreate && !isPartner && !isHVACInstallCalendar;

  const handleBackButtonClick = () => {
    setEvent({ ...event, type: null });
  };

  // Only used for address change for google auto complete
  const handleAddressChange = (e) => {
    handleFieldChange(e);

    const { value: address } = e.target;
    // Sets address invalid if the value exists. This would mean theyve changed the address without selecting it from the autocomplete
    // Sets address valid if value does not exist. This would mean they removed the address from the event.
    setAddressInvalid(!!address);
  };

  // Used when you click an address in the google auto complete
  const handleGoogleLocationInput = (autocomplete) => {
    const updateObject = parseGoogleAutocomplete(autocomplete);

    handleFieldChange({ target: { name: 'address', value: updateObject } });
    setAddressInvalid(false);
  };

  const handleFieldChange = (e, updatedEvent = prevState.event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  const onCancelButtonClicked = async () => {
    const { value: confirmed } = await Swal.fire({
      icon: 'warning',
      title: 'Important: Cancel Action',
      text: `Are you sure you want to cancel ${eventName}?`,
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirmed) return false;
    return handleCancelClick();
  };

  const renderDatePickers = () => (
    <>
      <FormFieldContainer fieldName="jobLength">
        <FormStartEndDateTimePickers startEndTimes={startEndTimes} onChange={handleTimeChange} />
      </FormFieldContainer>
      <AddRemoveButtonContainer>
        <FormAddRemoveDayButtons
          name="jobLength"
          value={jobLength}
          onChange={handleFieldChange}
          amount={1}
        >
          1 Day
        </FormAddRemoveDayButtons>
        <FormAddRemoveDayButtons
          name="jobLength"
          value={jobLength}
          onChange={handleFieldChange}
          amount={0.5}
        >
          1/2 Day
        </FormAddRemoveDayButtons>
        <FormAddRemoveDayButtons
          amount={0.25}
          value={jobLength}
          name="jobLength"
          onChange={handleFieldChange}
        >
          1/4 Day
        </FormAddRemoveDayButtons>
      </AddRemoveButtonContainer>
    </>
  );

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col size={2}>
              <HeaderLabel>
                {showBackButton && <BackButton onClick={handleBackButtonClick} />}
                Custom Event Block
              </HeaderLabel>
            </Col>
          </Row>
        </EventSidebarHeader>
        <EventSidebarBody>
          <Row>
            <Col>
              {patternName && <FormInput title="Pattern" value={patternName} readOnly />}
              <FormInput
                name="eventName"
                value={eventName}
                title="Event Name"
                placeholder=""
                onChange={handleFieldChange}
                disabled={!canEdit}
              />
              <GoogleAddressInput
                title="Address"
                name="address"
                value={address}
                onPlaceChange={(autocomplete) => handleGoogleLocationInput(autocomplete)}
                onChange={handleAddressChange}
                isInvalid={addressInvalid}
                uniqueId="address"
              />
              <FormTextBox
                name="notes.officeNotes"
                value={notes.officeNotes}
                title="Notes"
                placeholder=""
                onChange={handleFieldChange}
                disabled={!canEdit}
              />
              {renderDatePickers()}
              <FormMultiselect
                required
                readOnly={isPartner}
                title={isHesCalendar ? 'Hes(s)' : 'Truck(s)'}
                name="oids"
                recoilOptions={agentsFormOptionsSelector}
                onChange={handleFieldChange}
                value={oids}
              />
              <Checkbox
                name="shadow"
                label="Overlapable Event?"
                checked={shadow}
                value={shadow}
                onChange={handleFieldChange}
                disabled={!canEdit}
              />
            </Col>
          </Row>
          {!isCreate && scheduledBy && scheduledDate && (
            <Row>
              <Col>
                <FormInfo>
                  <FormInfoField title="Scheduled By :" body={scheduledBy} />
                  <FormInfoField
                    title="Scheduled On :"
                    body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
                  />
                </FormInfo>
              </Col>
            </Row>
          )}
        </EventSidebarBody>
        <EventSidebarFooter
          leftButtons={
            !isCreate ? (
              <CancelButton onClick={() => onCancelButtonClicked()}>Cancel Event</CancelButton>
            ) : null
          }
          rightButtons={
            canEdit ? <PrimaryButton onClick={() => handleSaveClick()}>Save</PrimaryButton> : null
          }
        />
      </Suspense>
    </SidebarForm>
  );
};

CustomBlockForm.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default CustomBlockForm;
