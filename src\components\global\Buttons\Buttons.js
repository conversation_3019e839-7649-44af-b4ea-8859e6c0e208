import styled from 'styled-components';

const Button = styled.button`
  display: block;
  background-color: ${({ theme }) => theme.secondary[100]};
  border: 1px solid ${({ theme }) => theme.secondary[400]};
  border-radius: 8px;
  font-size: ${(props) => (props?.shrink ? '14px' : '16px')};
  line-height: unset;
  padding: ${(props) => (props?.shrink ? '0.1em' : '0.4em')};
  color: ${({ theme }) => theme.secondary[500]};
  white-space: nowrap;
  width: ${(props) => (props?.width ? props.width : 'auto')};
  opacity: ${({ disabled = false }) => {
    return disabled ? 0.4 : 1;
  }};

  ${(props) => {
    if (props.left) {
      return 'float:left;';
    }
    if (props.right) {
      return 'float: right;';
    }
    return '';
  }}
`;

const CancelButton = styled(Button)`
  border: 1px solid ${({ theme }) => theme.colors.red};
  color: ${({ theme }) => theme.colors.red};
`;

const PrimaryButton = styled(Button)`
  background-color: ${({ theme }) => theme.colors.eventGreen};
  color: ${({ theme }) => theme.secondary[100]};
  width: ${(props) => (props?.width ? props.width : 'auto')};
`;

const SecondaryButton = styled(Button)`
  color: ${({ theme }) => theme.colors.eventGreen};
  border: 1px solid ${({ theme }) => theme.colors.eventGreen};
`;

export { Button, CancelButton, PrimaryButton, SecondaryButton };
