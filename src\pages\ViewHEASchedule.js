import React from 'react';
import { InstallationScheduledCalendar } from '@components/global';
import { isAuthorized } from '@utils/AuthUtils';

const ViewHEASchedulePage = () => {
  const isHeaScheduler = isAuthorized('Scheduler', 'HEA', null, 'MA');

  return (
    <InstallationScheduledCalendar
      title="HEA Schedule"
      type="000000"
      noButtons={!isHeaScheduler}
      showAllUsersEvents={isHeaScheduler}
    />
  );
};

ViewHEASchedulePage.propTypes = {};

export default ViewHEASchedulePage;
