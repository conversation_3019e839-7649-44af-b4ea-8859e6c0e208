import React from 'react';
import { useSetRecoilState, useResetRecoilState } from 'recoil';
import swal from 'sweetalert2/dist/sweetalert2';

import { getSalesforceUrl, parseJSXContentForSwalPopup } from '@utils/functions';
import { throwError } from '@utils/EventEmitter';
import { SalesforceManager } from '@utils/APIManager';

import { activeTabState, formValuesState, resetFormRecoilState } from '@recoil/dataIntakeForm';
import { useFieldsAndTabsForIntake } from '@pages/LeadIntake/utils/getters/useTabsAndFieldsForIntake';

import { activeTabIndexAtom } from '@recoil/app';

import { ScriptText } from '../../LeadIntakeScript/styles';

const useAccountsCreation = (leadId, nextTab) => {
  const setFormValues = useSetRecoilState(formValuesState);
  const setActiveTab = useSetRecoilState(activeTabState);
  const setActiveTabIndex = useSetRecoilState(activeTabIndexAtom(['tabs']));
  const resetFormState = useResetRecoilState(resetFormRecoilState);
  const { leadIntakeTabs } = useFieldsAndTabsForIntake();

  // TODO: move this to lead intake recoil state setter. Currently stolen from the WrapUp component
  const resetLeadIntake = () => {
    setActiveTab(leadIntakeTabs[0]?.name);
    setActiveTabIndex(0);
    resetFormState();
  };

  const proceedWithAccountsCreation = async (updatedFormValues) => {
    // TODO: I think since we're handling the error in the SalesforceManager now we don't need this try catch.
    // I don't see anything else that would throw an error but should confirm.
    try {
      const processAccountResponse = await SalesforceManager.processAccount(updatedFormValues);

      if (!processAccountResponse) return false;

      let sfIds = {};
      processAccountResponse.forEach(({ recordId }, index) => {
        sfIds = { ...sfIds, [`accountId${index === 0 ? '' : index + 1}`]: recordId };
      });
      const updateFields = {
        isFormSubmitted: true,
        accountId: [sfIds.accountId],
        sfIds,
      };
      setFormValues(updateFields);

      // Check if the user is trying to intake a competitive quote customer
      const { competitiveQuoteType } = updatedFormValues;
      // We don't book windows/hvac through the scheduler right now, so provide a link so they can book it in Salesforce
      if (competitiveQuoteType.some((type) => ['Windows', 'HVAC'].includes(type))) {
        // This should really probably be calculated using an event type or something,
        // but since this file is only used for CT I am hardcoding it for now
        const salesforceUrl = getSalesforceUrl('CT');

        const alert = () =>
          swal.fire({
            title: 'Competitive Windows/HVAC Quotes',
            html: parseJSXContentForSwalPopup(
              <>
                <ScriptText>
                  We cannot currently book quote appointments for{' '}
                  {competitiveQuoteType.filter((type) => type !== 'Insulation').join(' or ')}{' '}
                  through the scheduler.
                </ScriptText>
                <ScriptText>
                  Please use{' '}
                  <a href={`${salesforceUrl}${sfIds.accountId}`} target="_blank" rel="noreferrer">
                    this link
                  </a>{' '}
                  to navigate to the newly created Salesforce page for this customer, and create a
                  new work visit under the HEA opportunity. Then block the time on the scheduler
                  with a custom block.
                </ScriptText>
              </>,
            ),
            confirmButtonText: 'OK',
            icon: 'info',
          });

        // If they're not booking an event type that we can book in the scheduler, return so we don't proceed to the slots search.
        if (!competitiveQuoteType.includes('Insulation')) return alert().then(resetLeadIntake);
        // We do book insulation quotes in the scheduler, so if quote type includes insulation,
        // Search for slots for insulation quotes on the next tab.
        alert();
      }

      return nextTab();
    } catch (err) {
      return throwError(err);
    }
  };

  return proceedWithAccountsCreation;
};

export default useAccountsCreation;
