import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';
import SearchBar from '@components/global/SearchBar';
import { addEventChangedListener, reloadCalendar } from '@utils/EventEmitter';

const rowHeight = '35px';

const StyledCalendarHeader = styled.div`
  display: flex;
  justify-content: space-evenly;
  border-bottom: 1px solid ${({ theme }) => theme.colors.calendarBorder};
  background-color: ${({ theme }) => theme.secondary[100]};

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.tablet)} {
    flex-direction: column;
    margin-bottom: 20px;
  }
`;

const DateCell = styled.div`
  flex-grow: 1;
  flex-basis: 100%;
  width: 0;
  text-align: center;
  min-height: ${rowHeight};
  line-height: ${rowHeight};
  color: ${({ theme }) => theme.secondary[500]};

  ${({ isToday }) => {
    return isToday ? 'font-weight: 700; color: black;' : '';
  }}}

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.tablet)} {
    display: none;
    min-height: unset;
  }
`;

const CalendarHeader = React.memo(function CalendarHeader(props) {
  const { id = null, startDate, isMonthly = false } = props;

  const DATE_FORMAT = isMonthly ? 'ddd' : 'ddd MM/DD';

  useEffect(() => {
    // Reload calendar on date change
    reloadCalendar(startDate, id);
    // Update EventChanged listener (fired on reschedule, cancel, reassign etc) to reflect the new date
    const removeEventChangedListener = addEventChangedListener(() => reloadCalendar(startDate, id));
    return () => {
      removeEventChangedListener();
    };
  }, [startDate, id]);

  const renderDates = () => {
    const date = moment(startDate);
    const dateCells = [];
    for (let ii = 0; ii < 7; ii++) {
      dateCells.push(
        <DateCell key={ii} isToday={date.isSame(moment(), 'day')}>
          {date.format(DATE_FORMAT).toUpperCase()}
        </DateCell>,
      );
      date.add(1, 'days');
    }
    return dateCells;
  };

  return (
    <StyledCalendarHeader>
      {!isMonthly && <SearchBar />}
      {renderDates()}
    </StyledCalendarHeader>
  );
});

CalendarHeader.propTypes = {
  id: PropTypes.string,
  startDate: PropTypes.instanceOf(moment).isRequired,
  isMonthly: PropTypes.bool,
};

export default CalendarHeader;
