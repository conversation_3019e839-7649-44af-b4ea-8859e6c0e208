import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue } from 'recoil';

import { isAuthorized, hasRole } from '@utils/AuthUtils';

import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  FormMultiselect,
  SfIdInputs,
  FormRadioButtons,
  FormTextBox,
} from '@components/global/Form';
import { SecondaryButton } from '@components/global/Buttons/Buttons';
import { LoadingIndicator } from '@components/global';

import { eventTypeOptionsSelectorFamily, jobAttributesSelectorFamily } from '@recoil/app';
import { agentsFormOptionsSelector } from '@recoil/agents';
import { selectedEventState } from '@recoil/eventSidebar';

const ScheduleSealingServiceCT = (props) => {
  const { handleFindSlotsClick, handleSaveClick } = props;

  const [loading, setLoading] = useState(false);

  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);
  const agents = useRecoilValue(agentsFormOptionsSelector);

  const {
    type,
    numUnit,
    attributes,
    includeAgents,
    numberOfActiveBarriers,
    notes: { fieldNotes },
    // Work Order or Account Id can be set from URL in the InstallationScheduledCalendar component
    // If Work Order Id is set, use that instead of Account
    // sfIds,
    eventDuration,
  } = selectedEvent;

  const isHES = hasRole('Agent', 'HEA', 'CT') && !isAuthorized('Scheduler', 'HEA', 'CT');

  const eventTypes = useRecoilValue(
    eventTypeOptionsSelectorFamily({
      departmentName: 'HEA',
      showGroups: false,
      stateCode: '01',
    }),
  );

  const handleFieldChange = (e, updatedEvent = selectedEvent) => {
    handleFormFieldChange(e, updatedEvent, setSelectedEvent);
  };

  const handleBookSlot = () => {
    handleSaveClick();
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <HeaderTitle>Schedule Sealing Services</HeaderTitle>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            <SfIdInputs sfObjectType="account" setLoading={setLoading} />
            <FormSelect
              title="job type"
              name="type"
              value={type}
              options={eventTypes}
              onChange={handleFieldChange}
              placeholder="Event Type"
            />
            <FormRadioButtons
              title="How many units?"
              name="numUnit"
              value={numUnit}
              options={[1, 2, 3, 4]}
              type="number"
              onChange={handleFieldChange}
            />
            <FormInput
              title="Duration (Hours)"
              name="eventDuration"
              value={eventDuration}
              type="number"
              min={0.5}
              step={0.5}
              onChange={handleFieldChange}
              required
            />
            <FormInput
              title="Number of Active Barriers"
              name="numberOfActiveBarriers"
              value={numberOfActiveBarriers}
              type="number"
              min={0}
              readOnly
            />
            <FormMultiselect
              title="requirement(s)"
              name="attributes"
              value={attributes}
              recoilOptions={jobAttributesSelectorFamily({ type })}
              onChange={handleFieldChange}
            />
            {!isHES && (
              <FormMultiselect
                title="Include HES(s)"
                name="includeAgents"
                value={includeAgents}
                options={agents}
                onChange={handleFieldChange}
              />
            )}
            <FormTextBox
              title="Notes"
              name="notes.fieldNotes"
              value={fieldNotes}
              onChange={handleFieldChange}
            />
            <AvailableSlots singleAgent allowAgentSelect={false} />
          </Col>
        </Row>
      </EventSidebarBody>
      <EventSidebarFooter
        leftButtons={<BookSlotsButton handleBookSlots={() => handleBookSlot()} />}
        rightButtons={
          <SecondaryButton center onClick={() => handleFindSlotsClick()}>
            View Available Slots
          </SecondaryButton>
        }
      />
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

ScheduleSealingServiceCT.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default ScheduleSealingServiceCT;
