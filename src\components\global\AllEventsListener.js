import { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import _ from 'lodash';
import { addUpdateAllEventsListener } from '@utils/EventEmitter';
import { allEventsAtom } from '@recoil/event';

const AllEventsListener = () => {
  const [allEvents, setAllEvents] = useRecoilState(allEventsAtom);

  const mergeAllEvents = (newEvents) => {
    const newAllEvents = _.merge({}, allEvents, newEvents);

    setAllEvents(newAllEvents);
  };

  useEffect(() => {
    const removeUpdateAllEventsListener = addUpdateAllEventsListener(mergeAllEvents);
    return () => removeUpdateAllEventsListener();
  });
  return null;
};

export default AllEventsListener;
