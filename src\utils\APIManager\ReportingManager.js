import { handleApiCall } from '@utils/APIManager/utils/AxiosConfig';

const generateUtilizationReport = async (params) => {
  const url = '/api/reporting/generateUtilizationReport';
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Generating utilization report...',
    successMessage: 'Successfully generated report',
  });
  if (!response) return false;
  return response;
};

export default { generateUtilizationReport };
