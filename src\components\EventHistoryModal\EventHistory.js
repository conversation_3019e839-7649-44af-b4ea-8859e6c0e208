import { EventsManager } from '@utils/APIManager';
import { capitalizeFirstLetterOfString } from '@utils/functions';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import moment from 'moment';
import { Calendar2CheckFill } from '@styled-icons/bootstrap/Calendar2CheckFill';
import { CalendarWeekFill } from '@styled-icons/bootstrap/CalendarWeekFill';
import { CalendarEventFill } from '@styled-icons/bootstrap/CalendarEventFill';
import { Header } from '@components/global';
import PropTypes from 'prop-types';

const TimelineContainer = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 10px;
  width: 100%;
`;

const TimelineItem = styled.div`
  display: flex;
  justify-content: ${({ even }) => (even ? 'flex-end' : 'flex-start')};
  gap: 70px;
  align-items: flex-start;
  width: 100%;
  position: relative;
  padding: 10px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const TimelineOppositeContent = styled.div`
  flex: 1;
  text-align: right;
  color: #555;
`;

const TimelineSeparator = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  top: 10px;
  left: ${({ even }) => (even ? 'unset' : '46%')};
  right: ${({ even }) => (even ? '50%' : 'unset')};
  margin: 0;
  height: 80px;
`;

const Reschedule = styled(CalendarEventFill)`
  width: 28px;
  height: 28px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
`;

const Reassign = styled(CalendarWeekFill)`
  width: 28px;
  height: 28px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
`;

const Create = styled(Calendar2CheckFill)`
  width: 28px;
  height: 28px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
`;

const TimelineConnector = styled.div`
  width: 2px;
  background-color: #007bff;
  flex-grow: 1;
  margin-top: 10px;
`;

const TimelineContent = styled.div`
  flex: 1;
`;
const TimelineWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  background-color: #007bff;
  border-radius: 25px;
`;

const EventActionMap = {
  create: <Create />,
  reassign: <Reassign />,
  reschedule: <Reschedule />,
};

export const EventHistory = ({ eventId }) => {
  const [eventHistory, setEventHistory] = useState([]);

  useEffect(() => {
    const getEventHistory = async () => {
      const response = await EventsManager.getEventHistory(eventId);
      setEventHistory(response);
    };
    getEventHistory();
  }, []);

  const formatDateTime = (dateTime) => {
    if (!dateTime || typeof dateTime !== 'string' || Number.isNaN(Date.parse(dateTime))) {
      return 'Time not available';
    }

    return new Intl.DateTimeFormat('en-US', {
      timeZone: 'America/New_York',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    }).format(new Date(dateTime));
  };

  return (
    <TimelineContainer>
      {eventHistory?.map((history, index) => (
        <TimelineItem key={`${history?.firstname}-${history?.dateTime}`}>
          <TimelineOppositeContent>
            <Header h4 marginBottom={0}>
              {history?.dateTime
                ? moment.utc(new Date(history.dateTime)).format('MMM Do YYYY')
                : 'Date not available'}
            </Header>
            <Header h4 marginBottom={0}>
              {formatDateTime(history?.dateTime)}
            </Header>
          </TimelineOppositeContent>
          <TimelineSeparator>
            <TimelineWrapper>{EventActionMap[history.action]}</TimelineWrapper>
            {index !== eventHistory.length - 1 && <TimelineConnector />}
          </TimelineSeparator>
          <TimelineContent>
            <strong>{capitalizeFirstLetterOfString(history.action)}</strong>
            <div>{`${history.firstname} ${history.lastname}`}</div>
          </TimelineContent>
        </TimelineItem>
      ))}
    </TimelineContainer>
  );
};

EventHistory.propTypes = {
  eventId: PropTypes.string.isRequired,
};
