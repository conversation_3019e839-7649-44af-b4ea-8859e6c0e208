import { atom, selector } from 'recoil';
import { AgentsManager } from '@utils/APIManager';

const attributesAtom = atom({
  key: 'attributesAtom',
  default: selector({
    key: 'attributesDefaultSelector',
    get: async () => {
      let { attributes } = await AgentsManager.getAgentAttributes();
      attributes = attributes.map(
        ({ attributeName: key, attributeId: value, group, departmentId }) => {
          return { key, value, group, departmentId };
        },
      );
      return attributes;
    },
  }),
});

export default attributesAtom;
