import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { Header as Title } from '@components/global';
import { formatPhoneNumber } from '@utils/functions';
import { UtilityManager } from '@utils/APIManager';
import { Subtitle, MailIcon, HomeIcon, PhoneIcon } from '../Customers/styles';

const ListContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 34px;
`;
const ListItem = styled.div`
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease;
  padding: 8px;
  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }
`;

const handleItemClick = async (leadId) => {
  UtilityManager.openSfPage(leadId, 'Lead');
};

const ReferralList = ({ list }) => {
  return (
    <ListContainer>
      {list?.map(({ leadId, firstName, lastName, phone, town, email }) => (
        <ListItem key={leadId} onClick={() => handleItemClick(leadId)}>
          <Title h4>{`${firstName} ${lastName}`}</Title>
          <Subtitle>
            <HomeIcon /> {town}
          </Subtitle>
          <Subtitle>
            <PhoneIcon />
            {formatPhoneNumber(phone)}
          </Subtitle>
          <Subtitle>
            <MailIcon /> {email}
          </Subtitle>
        </ListItem>
      ))}
    </ListContainer>
  );
};

ReferralList.propTypes = {
  list: PropTypes.arrayOf(
    PropTypes.shape({
      leadId: PropTypes.string,
      firstName: PropTypes.string,
      lastName: PropTypes.string,
      phone: PropTypes.string,
      town: PropTypes.string,
      email: PropTypes.string,
    }),
  ).isRequired,
};

export default ReferralList;
