import {
  addKeyNameToObject,
  parseDataIntakeSections,
} from '@components/DataIntakeForm/dataIntakeFormHelpers';
import { sectionFields } from './sections';
import { formSchema } from './formSchema';

const leadIntakeFields = addKeyNameToObject(formSchema);
const sections = parseDataIntakeSections(sectionFields, leadIntakeFields);

const leadIntakeMap = {
  preliminaryQuestions: {
    text: 'Preliminary Questions',
    fields: [
      leadIntakeFields.atAnEvent,
      leadIntakeFields.campaignId,
      leadIntakeFields.preferredLanguage,
      leadIntakeFields.heaOrHvac,
      leadIntakeFields.hadPreviousAssessment,
      leadIntakeFields.didYouMoveForwardWithInsulation,
      leadIntakeFields.recentMove,
      leadIntakeFields.majorRenovations,
      leadIntakeFields.majorRenovationsTimeframe,
      leadIntakeFields.discountedRateCode,
      leadIntakeFields.condoAssociation,
      leadIntakeFields.howManyUnitsAreInAssociation,
      leadIntakeFields.singleOrMultiFamily,
      leadIntakeFields.numUnitsInBuilding,
      leadIntakeFields.numUnitsSchedulingToday,
      leadIntakeFields.allUnitsSameInfo,
      sections.unitInfo,
      sections.multiUnitInfo,
      sections.facilitatorInfo,
    ],
  },
  customerInfo: {
    text: 'Customer Info',
    fields: [
      leadIntakeFields.removeFromMailingLists,
      leadIntakeFields.customerFirstName,
      leadIntakeFields.customerLastName,
      leadIntakeFields.customerPrimaryPhoneNumber,
      leadIntakeFields.customerPrimaryPhoneNumberType,
      leadIntakeFields.customerSecondaryPhoneNumber,
      leadIntakeFields.customerSecondaryPhoneNumberType,
      leadIntakeFields.textMessageOptIn,
      leadIntakeFields.textMessageNotice,
      leadIntakeFields.emailOptOut,
      leadIntakeFields.customerEmail,
      leadIntakeFields.customerAddress,
    ],
  },
  sourceInfo: {
    text: 'Source Info',
    fields: [
      sections.siteId,
      leadIntakeFields.leadSource,
      leadIntakeFields.referredByAuditor,
      leadIntakeFields.referredByEmployee,
      leadIntakeFields.outreachProgramInfo,
      leadIntakeFields.partnersInfo,
      leadIntakeFields.referralCode,
      leadIntakeFields.referredByCustomer,
      leadIntakeFields.discountedRateCodeForCIA,
      leadIntakeFields.isWorkingWithLocalCapAgency,
      leadIntakeFields.doYouHaveHeat,
      leadIntakeFields.callSource,
    ],
  },
  homeHeatingSystem: {
    text: 'Home Heating System',
    fields: [sections.homeHeatingSystem],
  },
  review: {
    text: 'Review',
    fields: [sections.customerInfoReview, sections.utilityInfoReview],
  },
};

const leadIntakeMapCT = {
  customerInfo: {
    text: 'Customer Info',
    fields: [
      leadIntakeFields.customerNameScript,
      leadIntakeFields.customerFirstName,
      leadIntakeFields.customerLastName,
      leadIntakeFields.addressScript,
      leadIntakeFields.customerAddress,
      leadIntakeFields.emailScript,
      leadIntakeFields.customerEmail,
      leadIntakeFields.phoneScript,
      leadIntakeFields.customerPrimaryPhoneNumber,
      leadIntakeFields.customerPrimaryPhoneNumberType,
      leadIntakeFields.secondaryPhoneScript,
      leadIntakeFields.customerSecondaryPhoneNumber,
      leadIntakeFields.customerSecondaryPhoneNumberType,
      leadIntakeFields.textMessageOptIn,
      leadIntakeFields.textMessageNotice,
      leadIntakeFields.leadSentToCompany,
      leadIntakeFields.competitiveQuoteType,
    ],
  },
  preliminaryQuestions: {
    text: 'Preliminary Questions',
    fields: [
      leadIntakeFields.hadPreviousAssessmentCT,
      leadIntakeFields.howLongAgoEnergyAssesmentConducted,
      leadIntakeFields.singleOrMultiFamily,
      leadIntakeFields.numUnitsInBuilding,
      leadIntakeFields.numUnitsSchedulingToday,
      leadIntakeFields.allUnitsSameInfo,
      leadIntakeFields.houseBuilt,
      sections.unitInfoCT,
      sections.multiUnitInfoCT,
    ],
  },

  sourceInfo: {
    text: 'Source Info',
    fields: [
      leadIntakeFields.leadSourceCT,
      leadIntakeFields.referredByEmployeeCT,
      leadIntakeFields.leadSourceDetail,
      leadIntakeFields.otherLeadSource,
      leadIntakeFields.leadType,
    ],
  },
  review: {
    text: 'Review',
    fields: [sections.customerInfoReview],
  },
};

const leadIntakeMapBAs = {
  preliminaryQuestions: leadIntakeMap.preliminaryQuestions,
  customerInfo: leadIntakeMap.customerInfo,
  review: leadIntakeMap.review,
};

export { leadIntakeFields, sections, leadIntakeMapBAs, leadIntakeMap, leadIntakeMapCT };
export default addKeyNameToObject(leadIntakeMap);
