import React from 'react';
import PropTypes from 'prop-types';
import { useRecoilState } from 'recoil';
import { FormSelect } from '@components/global/Form';

import { agentEventsAtom } from '@recoil/home';

const EventMultiUnitButton = ({ event }) => {
  const [agentEvents, setAgentEvents] = useRecoilState(agentEventsAtom);
  const {
    sfIds = { dealId: '', operationsId: '' },
    numUnit,
    selectedUnitSfIds = { dealId: '', operationsId: '' },
  } = event;
  const selectedUnitSfIdsOptions = [
    {
      key: 'Unit 1',
      value: JSON.stringify({
        dealId: sfIds?.dealId || '',
        accountId: sfIds?.accountId || '',
        operationsId: sfIds?.operationsId || '',
        opportunityId: sfIds?.opportunityId || '',
        salesVisitId: sfIds?.salesVisitId || '',
        hvacVisitId: sfIds?.hvacVisitId || '',
        unitNumber: 0,
      }),
    },
  ];

  const isMultiFam = numUnit > 1;
  if (isMultiFam)
    [2, 3, 4].forEach((key) => {
      if (
        (sfIds?.[`dealId${key}`] && sfIds?.[`operationsId${key}`]) ||
        (sfIds?.[`accountId${key}`] && sfIds?.[`opportunityId${key}`])
      ) {
        selectedUnitSfIdsOptions.push({
          key: `Unit ${key}`,
          value: JSON.stringify({
            dealId: sfIds?.[`dealId${key}`],
            accountId: sfIds?.[`accountId${key}`],
            operationsId: sfIds?.[`operationsId${key}`],
            opportunityId: sfIds?.[`opportunityId${key}`],
            salesVisitId: sfIds?.[`salesVisitId${key}`],
            hvacVisitId: sfIds?.[`hvacVisitId${key}`],
            siteId: sfIds?.[`siteId${key}`],
            unitNumber: key - 1,
          }),
        });
      }
    });

  const handleFieldChange = (e) => {
    const updateEvent = agentEvents.map((element) => {
      const agentEvent = { ...element };
      if (agentEvent.id === event.id) agentEvent.selectedUnitSfIds = JSON.parse(e.target.value);
      return agentEvent;
    });
    setAgentEvents(updateEvent);
  };

  return (
    <FormSelect
      title=""
      placeholder="Select Unit"
      name="selectedUnitSfIds"
      value={JSON.stringify(selectedUnitSfIds)}
      onChange={(event) => handleFieldChange(event)}
      options={selectedUnitSfIdsOptions}
    />
  );
};

EventMultiUnitButton.propTypes = {
  event: PropTypes.shape({
    id: PropTypes.string,
    numUnit: PropTypes.number,
    sfIds: PropTypes.shape({
      dealId: PropTypes.string,
      accountId: PropTypes.string,
      operationsId: PropTypes.string,
      opportunityId: PropTypes.string,
      salesVisitId: PropTypes.string,
      hvacVisitId: PropTypes.string,
    }),
    selectedUnitSfIds: PropTypes.shape({
      dealId: PropTypes.string,
      accountId: PropTypes.string,
      operationsId: PropTypes.string,
      opportunityId: PropTypes.string,
      salesVisitId: PropTypes.string,
      hvacVisitId: PropTypes.string,
      unitNumber: PropTypes.number,
    }),
  }),
};

export default EventMultiUnitButton;
