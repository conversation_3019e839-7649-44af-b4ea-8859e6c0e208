import React from 'react';
import { useRecoilValue } from 'recoil';
import styled from 'styled-components';

import { agentEventsAtom } from '@recoil/home';

import { Container } from '../../sharedStyledComponents';

import EventColorBar from './EventColorBar';
import EventListBody from './EventListBody';
import EventListFooter from './EventListFooter';
import EventHeader from './EventHeader';

const EventContainer = styled.div`
  display: flex;
  flex-direction: row;
  border: 1px solid ${({ theme }) => theme.secondary[500]};
  border-left: 0;
  border-radius: 15px;
  margin: 10px 0;
  background-color: white;
  overflow: hidden;
`;

// All vertically aligned items, everything but color bar
const EventInfoContainer = styled.div`
  display: flex;
  flex-direction: column;
  font-size: 16px;
  width: 100%;
  @media (max-width: 450px) {
    overflow: hidden;
  }
`;

const EventDetailsContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: 10px;
`;

const EventsList = () => {
  const agentEvents = useRecoilValue(agentEventsAtom);
  return (
    <Container>
      {agentEvents.map((event) => {
        return (
          <EventContainer key={event?.accountId}>
            <EventColorBar eventType={event?.type} />
            <EventInfoContainer>
              <EventHeader event={event} />
              <EventDetailsContainer>
                <EventListBody event={event} />
                <EventListFooter event={event} />
              </EventDetailsContainer>
            </EventInfoContainer>
          </EventContainer>
        );
      })}
    </Container>
  );
};

export default EventsList;
