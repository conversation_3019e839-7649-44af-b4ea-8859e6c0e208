import { useEffect } from 'react';
import moment from 'moment';
import { addErrorListener, stopLoading } from '@utils/EventEmitter';
import swal from 'sweetalert2/dist/sweetalert2';

const ErrorMessage = () => {
  useEffect(() => {
    const removeErrorListener = addErrorListener(showErrorMessage);
    return () => {
      removeErrorListener();
    };
  }, []);

  const showErrorMessage = async (error) => {
    stopLoading();
    if (typeof error === 'string')
      return swal.fire({
        icon: 'error',
        title: error,
      });

    const { message, fatal } = error;
    if (message === 'Please log in to continue.') {
      await swal.fire({ icon: 'error', title: message });
      return window.location.reload();
    }
    return swal.fire({
      icon: 'error',
      title: message,
      text: fatal ? moment().format('Occurred at M/D/YYYY at H:mm:ss') : '',
      footer: fatal ? 'Send <NAME_EMAIL>' : '',
    });
  };
  return null;
};

export default ErrorMessage;
