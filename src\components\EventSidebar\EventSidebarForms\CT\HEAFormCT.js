import React, { Suspense, useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import moment from 'moment';
import _ from 'lodash';
import { decodeEventType } from '@homeworksenergy/utility-service';

import {
  selectedEventState,
  isSchedulingViewAtom,
  schedulingTypeAtom,
  isSlotsSearchAtom,
} from '@recoil/eventSidebar';
import { allAgentsFormOptionsState } from '@recoil/agents';
import { jobAttributesSelectorFamily } from '@recoil/app';

import useStartEndTimes from '@hooks/useStartEndTimes';

import { isAuthorized } from '@utils/AuthUtils';
import { SalesforceManager, UtilityManager } from '@utils/APIManager';
import { openNewTabWithUrl } from '@utils/functions';

import { Cancel<PERSON><PERSON>on, PrimaryButton } from '@components/global/Buttons';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  FormTextBox,
  FormInfoField,
  FormInfo,
  FormStartEndDateTimePickers,
  FormMultiselect,
} from '@components/global/Form';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import EventSidebarHeader, {
  HeaderLabel,
  HeaderTitle,
} from '@components/EventSidebar/EventSidebarHeader';
import Pin from '@components/Calendar/Pin';
import Lock from '@components/Calendar/Lock';
import TownWarnings from '@components/townWarnings/TownWarnings';
import { DropDownMenu, LoadingIndicator } from '@components/global';

const IconContainer = styled.div`
  margin-right: 10px;
  margin-top: auto;
  height: 24px;
  & :hover {
    cursor: pointer;
  }
`;

const confirmationStatusOptions = [
  'Confirmed',
  'Not Confirmed',
  'Rescheduled/Canceled',
  'Text - Confirmed',
  'Text - Not Confirmed',
  'Robocall - Confirmed',
  'Robocall - Not Confirmed',
];

const HEAFormCT = ({ handleCancelClick, handleSaveClick }) => {
  const [event, setEvent] = useRecoilState(selectedEventState);
  const agents = useRecoilValue(allAgentsFormOptionsState);
  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);
  const setIsSchedulingView = useSetRecoilState(isSchedulingViewAtom);
  const setSchedulingType = useSetRecoilState(schedulingTypeAtom);

  const [loading, setLoading] = useState(false);
  const {
    id,
    sfIds,
    customerName,
    notes,
    phoneNumber,
    email,
    oids,
    date,
    scheduledBy,
    scheduledDate,
    type,
    address,
    company,
    lock,
    startEndTimes,
    attributes,
    squareFeet,
    houseBuilt,
    gasProjectNumber,
    electricProjectNumber,
    leadSource,
    incomeEligibleOrMarketRate = '',
    heaConfirmationStatus = '',
    sealingServiceResult = '',
    heaResult = '',
    heaCoPayWaived,
    initialBlowerDoorReading,
    ductSealingOpp,
    numberOfActiveBarriers,
    arrivalWindow,
    hvacResultDetail,
    windowResultDetail,
    wxResultDetail,
    unitNumber,
    leadSourceDetail,
  } = event;

  const isCreate = !id;
  const isSealingService = type === '010005';

  const createEventTypes = [
    { key: 'Custom Block', value: '999999' },
    { key: 'Return Visit', value: '010004' },
  ];

  useEffect(() => {
    if (!date || !isCreate) return;

    // If no type for create event, push to the custom block form
    if (!type) setEvent({ ...event, type: createEventTypes[0].value });
  }, [date, createEventTypes, type, isCreate, setEvent, event]);

  // For some reason, this needs to be below the above useEffect, or else its value gets overwritten with the previous event value
  const handleTimeChange = useStartEndTimes();

  const displayAddress = address?.displayAddress;

  const isSchedulerLevel = isAuthorized('Scheduler', 'HEA', null, 'CT');
  const canPerformActions = isSchedulerLevel && !lock;

  const agent = agents.find((agent) => agent.value === oids[0]);

  const hesName = agent?.key;

  // TODO: should this display an error message if the event type is not found?
  const eventTypeName = type ? decodeEventType(type)?.businessEvent : '';

  const { projectId, opportunityId, accountId, workVisitId } = sfIds;

  const dropDownList = [
    {
      text: 'Go to Salesforce Page',
      onClick: () => onClickAccountId(accountId),
    },
    { text: 'Sync from SF', onClick: () => syncFromSalesforce() },
    {
      text: 'Doc Repo link',
      onClick: () => openNewTabWithUrl(`/doc-repo/CT/HEA/${opportunityId}`),
    },
  ];

  if (projectId) {
    dropDownList.push({
      text: 'Doc Repo link (OLD)',
      onClick: () => openNewTabWithUrl(`/doc-repo/CT/HEA/${projectId}`),
    });
  }

  // CONVERT OPTIONS
  // Don't show convert or schedule options if it is already that type
  if (type !== '010000') {
    dropDownList.push({
      text: 'Convert to HEA Visit',
      onClick: () => handleConvertClick('010000'),
    });
  }
  if (type !== '010001') {
    dropDownList.push({
      text: 'Convert to Insulation Quote Visit',
      onClick: () => handleConvertClick('010001'),
    });
  }
  if (type === '010006') {
    dropDownList.push({
      text: 'Convert to Sealing Service',
      onClick: () => handleConvertClick('010005'),
    });
  }
  if (type !== '010006') {
    dropDownList.push({
      text: 'Convert to HEA + Sealing',
      onClick: () => handleConvertClick('010006'),
    });
  }
  if (!['010005', '010006'].includes(type)) {
    dropDownList.push({
      text: 'Schedule Sealing Services',
      onClick: () => handleScheduleCoreServicesClick(),
    });
  }
  if (!['010000', '010001'].includes(type)) {
    dropDownList.push({
      text: 'Schedule Sealing Service Revisit',
      onClick: () => handleScheduleSealingServiceRevisitClick(),
    });
  }

  const syncFromSalesforce = async () => {
    const salesforceInfo = await SalesforceManager.getCTHEAEventInfoWithAccountId(
      [accountId],
      [workVisitId],
    );
    if (!salesforceInfo) return false;

    return setEvent({ ...event, ...salesforceInfo });
  };

  const handleScheduleCoreServicesClick = async () => {
    const updatedEvent = _.cloneDeep(event);
    updatedEvent.id = null;
    updatedEvent.type = '010005';
    updatedEvent.startEndTimes = [];
    // ToDO: support multifamily
    updatedEvent.sfIds = { accountId: sfIds.accountId, opportunityId: sfIds.opportunityId };
    setEvent(updatedEvent);
    setIsSlotsSearch(true);
  };

  const handleScheduleSealingServiceRevisitClick = async () => {
    const updatedEvent = _.cloneDeep(event);
    updatedEvent.id = null;
    updatedEvent.type = '010003';
    updatedEvent.startEndTimes = [];
    // ToDO: support multifamily
    updatedEvent.sfIds = { accountId: sfIds.accountId, opportunityId: sfIds.opportunityId };
    setEvent(updatedEvent);
    setIsSlotsSearch(true);
  };

  const handleConvertClick = async (type) => {
    return handleSaveClick({ ...event, type });
  };

  const handleSchedulingActionClick = (action) => {
    setSchedulingType(action);
    setIsSchedulingView(true);
  };

  const handleFieldChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  const onClickAccountId = (accountId) => {
    if (!isCreate && accountId) UtilityManager.openSf2Page(accountId, 'Account');
  };

  const arriwalWindowWarning = [
    {
      townWarningName: `Arrival Window: ${arrivalWindow}`,
      townWarningDescription:
        'The arrival window shown is for communication with the customer. Any changes to the official start time of the visit need to be communicated to the utilities.',
    },
  ];
  const renderFormFields = () => {
    return (
      <>
        {arrivalWindow && <TownWarnings townWarnings={arriwalWindowWarning} />}
        <EventSidebarBody>
          <Row>
            <Col size={1}>
              <SfIdInputs sfObjectType="account" title="salesforce" setLoading={setLoading} />
            </Col>
            <Col size={1}>
              <SfIdInputs sfObjectType="workVisit" readOnly setLoading={setLoading} />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput readOnly name="date" value={date} title="date" placeholder="" />
            </Col>
            <Col size={1}>
              {isCreate ? (
                <FormSelect
                  required
                  title="Visit Type"
                  placeholder="Select Visit Type"
                  name="type"
                  value={type}
                  onChange={handleFieldChange}
                  options={createEventTypes}
                />
              ) : (
                <FormInput
                  readOnly
                  name="eventTypeName"
                  value={eventTypeName}
                  title="Visit Type"
                  placeholder=""
                />
              )}
            </Col>
          </Row>
          <Row>
            <Col size={2}>
              <FormStartEndDateTimePickers
                key="startEndTime"
                name="startEndTime"
                displayDay={false}
                startEndTimes={startEndTimes}
                onChange={handleTimeChange}
                dateFormat="h:mm aa"
                allowDateSelect={false}
                direction="row"
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormInput
                readOnly
                name="address"
                value={displayAddress || ''}
                title="location"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormInput
                readOnly
                name="unitNumber"
                value={unitNumber || ''}
                title="Unit Number"
                placeholder=""
              />
            </Col>
            <Col size={1}>
              <FormInput
                readOnly
                name="incomeEligibleOrMarketRate"
                value={incomeEligibleOrMarketRate}
                title="Income Eligible/Market Rate"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput
                readOnly
                name="gasProjectNumber"
                value={gasProjectNumber}
                title="Gas Project Id"
                placeholder=""
              />
            </Col>
            <Col size={1}>
              <FormInput
                readOnly
                name="electricProjectNumber"
                value={electricProjectNumber}
                title="Electric Project Id"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput
                readOnly
                name="leadSource"
                value={leadSource}
                title="Lead Source"
                placeholder=""
              />
            </Col>
            <Col>
              <FormInput
                readOnly
                name="leadSourceDetail"
                value={leadSourceDetail || ''}
                title="Lead Source Detail"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              {/* TODO: this is a duplicate field on the event, we already had a confirmation status field */}
              <FormSelect
                options={confirmationStatusOptions}
                name="heaConfirmationStatus"
                value={heaConfirmationStatus}
                onChange={handleFieldChange}
                title="HEA Confirmation Status"
                placeholder=""
                readOnly
              />
            </Col>
            <Col size={1}>
              <FormInput
                readOnly
                name="sealingServiceResult"
                value={sealingServiceResult}
                title="Sealing Service Result"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col size={1}>
              <FormInput
                readOnly
                name="heaResult"
                value={heaResult}
                title="HEA Result"
                placeholder=""
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormInput
                readOnly
                title="HEA Co Pay Waived"
                name="heaCoPayWaived"
                value={heaCoPayWaived}
                onChange={handleFieldChange}
              />
            </Col>
            <Col>
              <FormInput readOnly title="HEA Company" name="company" value={company} />
            </Col>
          </Row>
          <Row>
            <Col>
              <FormInput
                required
                title="Square Feet"
                name="squareFeet"
                type="number"
                value={squareFeet}
                onChange={handleFieldChange}
              />
            </Col>
            <Col>
              <FormInput
                required
                title="House Built"
                name="houseBuilt"
                type="number"
                value={houseBuilt}
                onChange={handleFieldChange}
              />
            </Col>
          </Row>
          {isSealingService && (
            <>
              <Row>
                <Col>
                  <FormInput
                    title="Blower Door Pre"
                    name="initialBlowerDoorReading"
                    value={initialBlowerDoorReading}
                    type="number"
                    min={0}
                    onChange={handleFieldChange}
                    readOnly
                  />
                </Col>
                <Col>
                  <FormInput
                    title="Duct Sealing Opp"
                    name="ductSealingOpp"
                    value={ductSealingOpp}
                    type="number"
                    min={0}
                    onChange={handleFieldChange}
                    readOnly
                  />
                </Col>
              </Row>
              <Row>
                <Col>
                  <FormInput
                    title="HVAC Result Detail"
                    name="hvacResultDetail"
                    value={hvacResultDetail}
                    min={0}
                    onChange={handleFieldChange}
                    readOnly
                  />
                </Col>
                <Col>
                  <FormInput
                    title="Window Result Detail"
                    name="windowResultDetail"
                    value={windowResultDetail}
                    min={0}
                    onChange={handleFieldChange}
                    readOnly
                  />
                </Col>
              </Row>
              <Row>
                <Col>
                  <FormInput
                    title="WX Result Detail"
                    name="wxResultDetail"
                    value={wxResultDetail}
                    min={0}
                    onChange={handleFieldChange}
                    readOnly
                  />
                </Col>
                <Col>
                  <FormInput
                    title="Number of Active Barriers"
                    name="numberOfActiveBarriers"
                    value={numberOfActiveBarriers}
                    type="number"
                    min={0}
                    readOnly
                  />
                </Col>
              </Row>
            </>
          )}
          <Row>
            {attributes.length > 0 && (
              <Col>
                <FormMultiselect
                  required
                  title="Requirement(s)"
                  name="attributes"
                  recoilOptions={jobAttributesSelectorFamily({ type })}
                  onChange={handleFieldChange}
                  value={attributes}
                />
              </Col>
            )}
          </Row>
          <Row>
            <Col>
              {/* TODO: is it okay to handle customer notes like this? */}
              <FormTextBox
                name="notes.officeNotes"
                value={notes.officeNotes}
                title="Lead Scheduling Notes"
                placeholder=""
                onChange={handleFieldChange}
              />
            </Col>
            <Col>
              <FormTextBox
                name="notes.fieldNotes"
                value={notes.fieldNotes}
                title="HEA/HES Tech Notes"
                placeholder=""
                onChange={handleFieldChange}
              />
            </Col>
          </Row>

          <Row>
            <Col size={1}>
              <FormInput
                readOnly
                name="phoneNumber"
                value={phoneNumber}
                title="Customer Phone Number"
                placeholder=""
              />
            </Col>
            <Col size={1}>
              <FormInput
                readOnly
                name="email"
                value={email}
                title="Customer Email"
                placeholder=""
              />
            </Col>
          </Row>
          {isSchedulerLevel && !isCreate && (
            <Row>
              <Col>
                <FormInfo>
                  <FormInfoField title="Scheduled By :" body={scheduledBy} />
                  <FormInfoField
                    title="Scheduled On :"
                    body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
                  />
                </FormInfo>
              </Col>
            </Row>
          )}
        </EventSidebarBody>
      </>
    );
  };

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col size={2}>
              <HeaderLabel>Customer Name:</HeaderLabel>
              <HeaderTitle>{customerName}</HeaderTitle>
            </Col>
            <Col size={1} right>
              <HeaderLabel>HES:</HeaderLabel>
              <HeaderTitle>{hesName}</HeaderTitle>
            </Col>
            <Col size={0} left>
              <Row>
                <IconContainer>
                  {canPerformActions && <Pin event={event} onSidebar />}
                </IconContainer>
                <IconContainer>{isSchedulerLevel && <Lock event={event} />}</IconContainer>
                <DropDownMenu listItems={dropDownList} />
              </Row>
            </Col>
          </Row>
        </EventSidebarHeader>
        {renderFormFields()}
        <EventSidebarFooter
          leftButtons={
            <>
              <PrimaryButton onClick={() => handleSaveClick()}>Save</PrimaryButton>
              {canPerformActions && !isCreate && (
                <CancelButton onClick={() => handleCancelClick()}>Cancel Event</CancelButton>
              )}
            </>
          }
          rightButtons={
            canPerformActions ? (
              <>
                <PrimaryButton onClick={() => handleSchedulingActionClick('reschedule')}>
                  Reschedule
                </PrimaryButton>
                <PrimaryButton onClick={() => handleSchedulingActionClick('reassign')}>
                  Reassign
                </PrimaryButton>
              </>
            ) : null
          }
        />
      </Suspense>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

HEAFormCT.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
  history: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
};

export default withRouter(HEAFormCT);
