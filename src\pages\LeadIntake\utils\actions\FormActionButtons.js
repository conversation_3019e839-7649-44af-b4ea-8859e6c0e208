import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { useRecoilValue, useRecoilState } from 'recoil';

import { PrimaryButton } from '@components/global/Buttons';
import { activeTabIndexAtom } from '@recoil/app';
import { formValuesState, activeTabState } from '@recoil/dataIntakeForm';

const ActionsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  width: 40%;
  margin-top: 10px;
  gap: 12px;
`;

const FormActionButtons = (props) => {
  const {
    leadIntakeTabs,
    handleSubmit,
    validateNextTab,
    setResetDuplicateLeadRecordsTable,
  } = props;
  const formValues = useRecoilValue(formValuesState);
  const [activeTab, setActiveTab] = useRecoilState(activeTabState);
  const [activeTabIndex, setActiveTabIndex] = useRecoilState(activeTabIndexAtom(['tabs']));

  const { isFormSubmitted } = formValues;

  const handleNextTabButtonClick = () => {
    setResetDuplicateLeadRecordsTable(false);
    validateNextTab();
  };

  const backTab = (tabIndex = null) => {
    setResetDuplicateLeadRecordsTable(true);
    setActiveTabIndex(tabIndex !== null ? tabIndex : activeTabIndex - 1);
    setActiveTab(
      tabIndex !== null ? leadIntakeTabs[0]?.name : leadIntakeTabs[activeTabIndex - 1]?.name,
    );
  };

  const isBackButtonEnable = activeTabIndex > 0 && activeTabIndex < leadIntakeTabs.length - 1;
  const isPreviewButtonEnable =
    (activeTab !== 'review' && activeTabIndex < leadIntakeTabs.length - 1) || isFormSubmitted;
  const isSubmitButtonEnable = activeTab === 'review' && !isFormSubmitted;

  return (
    <ActionsContainer>
      {isBackButtonEnable && (
        <PrimaryButton width="75px" onClick={() => backTab()}>
          Back
        </PrimaryButton>
      )}
      {isPreviewButtonEnable && (
        <PrimaryButton width="75px" onClick={handleNextTabButtonClick}>
          Next
        </PrimaryButton>
      )}
      {isSubmitButtonEnable && (
        <PrimaryButton width="75px" onClick={() => handleSubmit()} disabled={isFormSubmitted}>
          Submit
        </PrimaryButton>
      )}
    </ActionsContainer>
  );
};

FormActionButtons.propTypes = {
  handleSubmit: PropTypes.func.isRequired,
  validateNextTab: PropTypes.func.isRequired,
  setResetDuplicateLeadRecordsTable: PropTypes.func.isRequired,
  /* eslint-disable react/forbid-prop-types */
  leadIntakeTabs: PropTypes.arrayOf(PropTypes.any).isRequired,
};

export default FormActionButtons;
