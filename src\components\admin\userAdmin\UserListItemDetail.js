import React from 'react';
import { useRecoilValue, useRecoilState } from 'recoil';
import Swal from 'sweetalert2/dist/sweetalert2';

import { selectedUserState, allAuthorizedUsersForUserSelector } from '@recoil/admin/users';

import { refreshAdminListState, adminInfoChangesState } from '@recoil/admin';

import { useInvalidateSelector } from '@recoil/hooks';

import { UsersManager } from '@utils/APIManager';

import ListItemDetail from '@components/global/ScreenPartitionView/ListItemDetail';

import EditUserInfoForm from './EditUserInfoForm';
import EditUserRolesForm from './EditUserRoles/EditUserRolesForm';

const UserListItemDetail = () => {
  const users = useRecoilValue(allAuthorizedUsersForUserSelector);
  const refreshUsers = useInvalidateSelector(refreshAdminListState);
  const [selectedUser, setSelectedUser] = useRecoilState(selectedUserState);
  const userInfoChanges = useRecoilValue(adminInfoChangesState);

  const { oid, displayName, companyName, departments: userDepartments } = selectedUser;

  // Only show ' | ' for values that exist
  const detailText =
    [companyName, userDepartments.join(', ')].filter((detail) => detail).join(' | ') ||
    'More details will show here';

  const tabs = [
    {
      name: 'info',
      title: 'User Info',
      component: <EditUserInfoForm />,
    },
    {
      name: 'capabilities',
      title: 'User Roles',
      component: <EditUserRolesForm />,
    },
  ];

  const updateUser = async () => {
    return UsersManager.updateUser({ oid, ...userInfoChanges });
  };

  const deleteUser = async () => {
    const alert = await Swal.fire({
      title: 'Do you want to deactivate the user?',
      showCloseButton: true,
      showCancelButton: true,
      showConfirmButton: true,
      confirmButtonText: 'Deactivate',
      cancelButtonText: 'Cancel',
    });
    if (!alert?.value) return;

    await UsersManager.updateUser({ oid, active: false });
    refreshUsers();
  };

  return (
    <ListItemDetail
      tabs={tabs}
      listItems={users}
      selectedItem={selectedUser}
      setSelectedItem={setSelectedUser}
      onSaveButtonClick={updateUser}
      onDeleteButtonClick={deleteUser}
      headerText={displayName}
      detailText={detailText}
    />
  );
};

export default UserListItemDetail;
