import { throwError, startLoading, stopLoading, displaySuccessMessage } from '@utils/EventEmitter';
import axios from './utils/AxiosConfig';

const createAgent = async (params) => {
  const url = '/api/crews/createCrew';
  startLoading('Creating new crew...');
  const response = await axios.post(url, params);
  stopLoading();
  const { error } = response.data;
  if (error) return throwError(error);
  displaySuccessMessage('Successfully created a new crew!');
  return response;
};

// updating agents info in users table dynamically as per params passed
const updateAgent = async (oid, params) => {
  const url = `/api/crews/${oid}`;
  startLoading('Updating Agent Info...');
  const { data: updatedAgent } = await axios.put(url, params);
  stopLoading();
  if (!updatedAgent) return throwError('An error occurred when updating the agent.');
  const { error } = updatedAgent;
  if (error) return throwError(error);
  if (updatedAgent.active) displaySuccessMessage('Successfully updated agent info.');
  else displaySuccessMessage('Successfully deactivated agent.');

  return updatedAgent;
};

// get agents by multiple departments and state
const getCrewsByStateAndDepartment = async (params) => {
  const url = '/api/crews/getCrewsByStateAndDepartment';
  const response = await axios.post(url, params);
  const { error } = response.data;
  if (error) return throwError(error);
  return response.data;
};

const getAgentAttributes = async () => {
  const url = '/api/crews/getCrewAttributes';
  const response = await axios.get(url);
  const { error } = response.data;
  if (error) return throwError(error);
  return response.data;
};

export default {
  createAgent,
  updateAgent,
  getCrewsByStateAndDepartment,
  getAgentAttributes,
};
