import { selector } from 'recoil';
import hesAgentFormOptionsState from './hesAgentFormOptionsState';

const hesAgentFormOptionsSelector = selector({
  key: 'hesAgentFormOptionsSelector',
  get: ({ get }) => {
    const auditors = get(hesAgentFormOptionsState);
    if (typeof auditors?.[0] === 'string') {
      return auditors;
    }
    return auditors?.map((agent) => ({ name: agent.displayName, value: agent.sfId }));
  },
});

export default hesAgentFormOptionsSelector;
