import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useSetRecoilState } from 'recoil';
import { useHistory, useLocation } from 'react-router-dom';

import { showSidebarState } from '@recoil/eventSidebar';

const ContainerWrapper = styled.nav`
  position: fixed;
  left: 100%;
  top: 0;
  display: ${({ show = false }) => (show ? 'unset' : 'none')};
  width: 100%;
  height: 100%;
  transition: transform 300ms;
  transform: ${({ show = false }) => (show ? 'translateX(-100%)' : 'translateX(3em)')};
  background-color: ${({ theme }) => theme.colors.transparent};
  z-index: 999;
`;

const EventDetailContainer = styled.div`
  position: absolute;
  display: flex;
  flex-direction: column;
  right: 0;
  min-width: 320px;
  width: 45%;
  height: 100%;
  background-color: ${({ theme }) => theme.secondary[100]};
  box-shadow: 0px 2px 34px ${({ theme }) => theme.colors.greyShadow};

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.laptop)} {
    width: 75%;
  }

  ${({ theme }) => theme.screenSize.down(theme.breakpoints.tablet)} {
    width: 95%;
  }

  // Fit close button on mobile
  ${({ theme }) => theme.screenSize.down(theme.breakpoints.mobileL)} {
    width: 100%;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  opacity: 1;
  border: none;
  width: 35px;
  height: 35px;
  left: -3em;
  top: 0;
  border-radius: 50%;
  color: ${({ theme }) => theme.secondary[400]};
  background: ${({ theme }) => theme.secondary[100]};
  box-shadow: 0px 7px 12px ${({ theme }) => theme.colors.greyShadow};
`;

const EventSidebarBase = ({ children, ...props }) => {
  const history = useHistory();
  const location = useLocation();

  const setShowSidebar = useSetRecoilState(showSidebarState);
  const closeSidebar = () => {
    setShowSidebar(false);
    // Remove find-slots from URL on sidebar close
    if (location.pathname.includes('find-slots')) {
      const newPath = location.pathname.split('/')[1];
      history.push(`/${newPath}`);
    }
  };

  return (
    <>
      <ContainerWrapper {...props}>
        <EventDetailContainer
          onClick={(e) => {
            e.stopPropagation();
          }}
          {...props}
        >
          <CloseButton onClick={closeSidebar}>x</CloseButton>
          {children}
        </EventDetailContainer>
      </ContainerWrapper>
    </>
  );
};

EventSidebarBase.propTypes = {
  children: PropTypes.node.isRequired,
  show: PropTypes.bool,
};

export default EventSidebarBase;
