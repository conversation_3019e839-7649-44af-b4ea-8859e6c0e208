import { atom, selector, DefaultValue } from 'recoil';

const defaultValues = { paymentType: '', amount: '', customAmount: '', location: '' };
const paymentFieldStates = {};

Object.keys(defaultValues).forEach((fieldName) => {
  paymentFieldStates[fieldName] = atom({
    key: `payment-${fieldName}Atom`,
    default: defaultValues[fieldName],
  });
});

const paymentLinkSelector = selector({
  key: 'paymentLinkSelector',
  get: ({ get }) => {
    const payment = {};
    const propertyNames = Object.keys(paymentFieldStates);

    // Get value of each atom, then return together in an object
    propertyNames.forEach((propertyName) => {
      payment[propertyName] = get(paymentFieldStates[propertyName]);
    });

    return payment;
  },
  set: ({ set, reset }, newValue) => {
    if (newValue instanceof DefaultValue) {
      const propertyNames = Object.keys(paymentFieldStates);
      propertyNames.forEach((propertyName) => reset(paymentFieldStates[propertyName]));
      return;
    }
    const propertyNames = Object.keys(newValue);
    propertyNames.forEach((propertyName) => {
      if (!paymentFieldStates[propertyName]) return;
      set(paymentFieldStates[propertyName], newValue[propertyName]);
    });
  },
});

export default paymentLinkSelector;
