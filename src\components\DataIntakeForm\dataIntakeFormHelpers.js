/* eslint-disable no-param-reassign */

// <PERSON> through a dictionary of objects and add the dictionary 'key' inside the 'value'
// This is used in the form config to avoid typos in the field or section names
// By adding the key name to the section, it allows setting of the values object based on the field name when we don't have access to the outer 'key' value
const addKeyNameToObject = (object) => {
  const objectWithKeyName = Object.keys(object).reduce((objectWithKeyName, keyName) => {
    // Add 'name' to fields. Set to the string value of the object key
    object[keyName].name = keyName;

    objectWithKeyName[keyName] = object[keyName];

    return objectWithKeyName;
  }, {});

  return objectWithKeyName;
};

/**
 * Used to add 'perUnit' to the fields inside of a form 'section' (such as the unitInfo on the leadIntake preliminary questions)
 * These are needed to set their default values properly below.
 */
const parseDataIntakeSections = (sections, allFields) => {
  const parsedSections = addKeyNameToObject(sections);

  Object.values(parsedSections).forEach((section) => {
    const { fields, perUnit } = section;

    // If the section is repeated perUnit, add the perUnit flag to all the fields underneath it
    // This mutates the fields object that was passed to the function
    if (perUnit)
      fields.forEach(({ name: fieldName }) => {
        allFields[fieldName].perUnit = true;
      });
  });

  return parsedSections;
};

/**
 * Helper function for getDataIntakeFormValues.
 * Pulls out logic that serves a few purposes:
 * - Changes undefined default values to null. This is helpful for setting values on form components and recoil states
 * - Preserves values that have already been filled out on the form without resetting them. This is so we can change the numUnit without resetting the entire form
 * - Set values 'perUnit' fields as an array of numUnit
 * - Sets the values of all other fields to their set default values
 */
const getFieldValue = (fieldName, defaultValue, perUnit, numUnit, existingValues) => {
  defaultValue = defaultValue === undefined ? null : defaultValue;
  // If it is a perUnit field and they have indicated it is a multifamily, the default value is repeated in an array numUnit times, ie: [defaultValue, defaultValue, defaultValue]
  if (perUnit)
    defaultValue = [...Array(numUnit)].map((_, index) => {
      // Preserve already added data when adding another unit
      if (existingValues?.[fieldName]?.[index]) return existingValues[fieldName][index];
      return defaultValue;
    });
  // Preserve already added data when adding another unit
  else if (existingValues?.[fieldName]) return existingValues[fieldName];
  return defaultValue;
};

/**
 * Compiles the default values from the fields for easy use in a {[fieldName]: defaultValue} type object
 * Also used to get the defaultValues in [defaultValue, defaultValue...] format when changing numUnit
 * Uses the existingValues (if passed) to prevent overwriting values that have already been filled out
 */
const getDataIntakeFormValues = (dataIntakeFields, existingValues, numUnit = 1) => {
  const defaultValues = Object.keys(dataIntakeFields).reduce((defaultValues, fieldName) => {
    const currentField = dataIntakeFields[fieldName];
    const { perUnit } = currentField;

    // Map through to find all default values
    const { default: defaultValue } = currentField;

    // If defaultValue is an object, use it as is; otherwise, create an object
    defaultValues[fieldName] =
      typeof defaultValue === 'object'
        ? defaultValue
        : getFieldValue(fieldName, defaultValue, perUnit, numUnit, existingValues);
    return defaultValues;
  }, {});

  return defaultValues;
};

export { addKeyNameToObject, parseDataIntakeSections, getDataIntakeFormValues };
