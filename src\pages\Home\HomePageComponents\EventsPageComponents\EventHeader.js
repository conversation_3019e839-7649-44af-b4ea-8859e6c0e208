import React, { useRef } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import TownWarnings from '@components/townWarnings/TownWarnings';
import CopyText from '@components/global/Icons/CopyText';
import ScheduledOnline from '@components/global/Icons/ScheduledOnline';
import { Payment } from '@components/Calendar/EventComponents';

import EventMultiUnitButton from './EventMultiUnitButton';

const StyledEventHeader = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const CopyButtonStyled = styled(CopyText)``;
const ScheduledOnlineStyled = styled(ScheduledOnline)``;

const IconsContainer = styled.div`
  display: flex;
  height: 50px;
  margin: 5px;
  padding: 5px;
  justify-content: space-between;
`;

const PaymentContainer = styled.div``;

const EventHeader = ({ event }) => {
  const paymentRef = useRef();
  const { townWarnings, numUnit, selectedUnitSfIds, scheduledOnline, type } = event;
  const isHVACSales = type.slice(0, 4) === '0001';
  const isHEA = type.slice(0, 4) === '0000';
  const isWx = type.slice(0, 4) === '0006';
  const copyTextValue = isHVACSales
    ? selectedUnitSfIds?.opportunityId?.slice(0, 15)
    : selectedUnitSfIds?.dealId?.slice(0, 15);
  const dealId = selectedUnitSfIds?.dealId?.slice(0, 15);
  const opportunityId = selectedUnitSfIds?.opportunityId?.slice(0, 15);
  const payzerId = isHVACSales ? selectedUnitSfIds?.opportunityId : selectedUnitSfIds?.operationsId;
  const siteId = selectedUnitSfIds?.siteId;
  const isMultiFam = numUnit > 1;
  const allowPaymentDept = isHVACSales || isHEA || isWx;
  const isUnitSelected = isMultiFam ? selectedUnitSfIds.unitNumber >= 0 : true;
  const allowPayment = allowPaymentDept && isUnitSelected;

  return (
    <StyledEventHeader>
      <TownWarnings townWarnings={townWarnings} />
      <IconsContainer>
        <PaymentContainer>
          {allowPayment && (
            <Payment
              ref={paymentRef}
              onClick={() =>
                paymentRef.current.handlePaymentButtonClick(
                  selectedUnitSfIds.unitNumber,
                  isMultiFam,
                )
              }
              event={event}
            />
          )}
        </PaymentContainer>
        <CopyButtonStyled
          copyTextValue={copyTextValue}
          dealId={dealId}
          opportunityId={opportunityId}
          payzerId={payzerId}
          siteId={siteId}
          useDropDown
          alignConfirm
        />
        {scheduledOnline && <ScheduledOnlineStyled hoverText="Scheduled Online" />}
        {isMultiFam && <EventMultiUnitButton event={event} />}
      </IconsContainer>
    </StyledEventHeader>
  );
};

EventHeader.propTypes = {
  event: PropTypes.shape({
    selectedUnitSfIds: PropTypes.shape({
      dealId: PropTypes.string,
      opportunityId: PropTypes.string,
      operationsId: PropTypes.string,
      siteId: PropTypes.string,
      unitNumber: PropTypes.number,
    }),
    townWarnings: PropTypes.arrayOf(PropTypes.shape({})),
    numUnit: PropTypes.number,
    scheduledOnline: PropTypes.bool,
    type: PropTypes.string,
  }).isRequired,
};

export default EventHeader;
