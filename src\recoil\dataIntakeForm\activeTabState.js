import { atom, selector } from 'recoil';

import formMapState from './formMapState';

const activeTabState = atom({
  key: 'activeTabAtom',
  default: selector({
    key: 'activeTabDefault',
    get: ({ get }) => {
      const formMap = get(formMapState);

      if (!formMap) return null;

      const [defaultTab] = Object.keys(formMap);

      return defaultTab;
    },
  }),
});

export default activeTabState;
