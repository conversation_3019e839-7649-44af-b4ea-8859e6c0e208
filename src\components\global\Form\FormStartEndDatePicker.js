import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import FormDatePicker from './FormDatePicker';
import FormFieldContainer from './FormFieldContainer';

const DatePickerContainer = styled(FormFieldContainer)`
  display: flex;
  flex-direction: ${({ direction }) => direction};
`;

const FormStartEndDatePicker = ({
  onChange = () => {},
  endDate,
  required = false,
  startDate,
  direction = 'column',
  compact = false,
}) => {
  return (
    <DatePickerContainer required={required} direction={direction} compact={compact}>
      <FormDatePicker title="Start Date" name="startDate" value={startDate} onChange={onChange} />
      <FormDatePicker title="End Date" name="endDate" value={endDate} onChange={onChange} />
    </DatePickerContainer>
  );
};

FormStartEndDatePicker.propTypes = {
  onChange: PropTypes.func,
  required: PropTypes.bool,

  startDate: PropTypes.shape({}).isRequired,
  endDate: PropTypes.shape({}).isRequired,
  direction: PropTypes.oneOf(['row', 'column']),
  compact: PropTypes.bool,
};

export default FormStartEndDatePicker;
