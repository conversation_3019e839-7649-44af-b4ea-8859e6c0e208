import React from 'react';
import { useSetRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useTheme } from 'styled-components';

import { getCityAndZipcodeFromAddress, chopString } from '@utils/functions';
import SalesforceManager from '@utils/APIManager/SalesforceManager';

import { selectedEventState } from '@recoil/eventSidebar';

import Event from '@components/Calendar/EventComponents/Event';

const SubEventCT = ({ event, ...otherProps }) => {
  const theme = useTheme();
  const setSelectedEvent = useSetRecoilState(selectedEventState);

  const {
    type,
    startTime,
    endTime,
    address,
    notes,
    lock,
    numUnit,
    sfIds: { accountId, workVisitId },
  } = event;

  // TODO: do we need the customer names on the calendar events at all?
  let { customerName } = event;
  if (!customerName) customerName = 'No customer found';

  const displayTime = `${moment(startTime, 'hh:mm:ss').format('h:mm a')} - ${moment(
    endTime,
    'hh:mm:ss',
  ).format('h:mm a')}`;

  const cityAndZipcode = getCityAndZipcodeFromAddress(address) || 'No Address Found';

  const customerNameString = customerName ? chopString(customerName, 30) : '';

  const bodyHeader = <>{customerNameString}</>;

  const bodyText = (
    <>
      {cityAndZipcode}
      {numUnit && numUnit > 1 ? (
        <>
          <br />
          {`${numUnit} units`}
        </>
      ) : (
        ''
      )}
    </>
  );

  const eventColorMap = {
    '010600': theme.colors.eventL, // Window Visit
    '010601': theme.colors.eventJ, // HVAC Visit
  };

  const eventColor = eventColorMap[type];

  const syncFromSalesforce = async () => {
    if (!accountId) return false;
    const salesforceInfo = await SalesforceManager.getCTHEAEventInfoWithAccountId(
      [accountId],
      [workVisitId],
    );
    if (!salesforceInfo) return false;

    return setSelectedEvent({ ...event, ...salesforceInfo });
  };

  return (
    <Event
      event={event}
      backgroundColor={eventColor}
      tooltip={notes?.fieldNotes}
      headerText={displayTime}
      bodyHeader={bodyHeader}
      bodyText={bodyText}
      pinnable
      lockable
      lock={lock}
      onClick={syncFromSalesforce}
      {...otherProps}
    />
  );
};

SubEventCT.propTypes = {
  event: PropTypes.shape({
    address: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        city: PropTypes.string,
        postalCode: PropTypes.string,
      }),
    ]),
    customerName: PropTypes.string,
    date: PropTypes.string,
    endTime: PropTypes.string,
    id: PropTypes.string,
    lock: PropTypes.bool,
    notes: PropTypes.shape({
      officeNotes: PropTypes.string,
      fieldNotes: PropTypes.string,
    }),
    sfIds: PropTypes.shape({
      accountId: PropTypes.string,
      workVisitId: PropTypes.string,
    }),
    numUnit: PropTypes.number,
    type: PropTypes.string,
    startTime: PropTypes.string,
  }).isRequired,
};

export default SubEventCT;
