import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilValue, useResetRecoilState } from 'recoil';

import { selectedFiltersState } from '@recoil/admin';

import { SecondaryButton } from '@components/global/Buttons';
import FilterDropdown from './FilterDropdown';

const FiltersContainer = styled.div`
  display: flex;
  justify-content: space-between;
`;
const ClearFiltersButton = styled(SecondaryButton)``;

// For example we're using it in the event patterns section
const Filters = ({ filters = [] }) => {
  const selectedFilters = useRecoilValue(selectedFiltersState);
  const resetSelectedFilters = useResetRecoilState(selectedFiltersState);

  // TODO: might be a better way to do this. Using an object for the selectedFilters is nice to use but makes checking for selected a little ugly
  const filterSelected = Object.values(selectedFilters).some((filter) => filter);

  const clearFilters = () => {
    resetSelectedFilters();
  };

  return (
    <>
      <FiltersContainer>
        {filters.map((filter) => {
          return <FilterDropdown filter={filter} key={filter.name} />;
        })}
      </FiltersContainer>
      {filterSelected && (
        <ClearFiltersButton onClick={clearFilters}>Clear Filters</ClearFiltersButton>
      )}
    </>
  );
};

Filters.propTypes = {
  filters: PropTypes.arrayOf(PropTypes.shape({})),
};

export default Filters;
