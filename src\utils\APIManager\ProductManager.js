import { handleApiCall } from '@utils/APIManager/utils/AxiosConfig';

const getAllProductsByOpportunityId = async (oppId) => {
  const url = `/api/products/getProductsByOppId/${oppId}`;
  const response = await handleApiCall({
    url,
    method: 'get',
    loadingMessage: 'Fetching Products...',
  });
  if (!response) return null;

  return response;
};

const deleteProductById = async ({ productId }) => {
  const url = `/api/products/deleteProductById/${productId}`;
  const response = await handleApiCall({
    url,
    method: 'get',
    loadingMessage: 'Deleting Product...',
    successMessage: 'Delete Successfull...',
  });
  if (!response) return null;
  return response;
};

const getProductsOptions = async () => {
  const url = '/api/products/getActiveProducts/';
  const response = await handleApiCall({
    url,
    method: 'get',
    loadingMessage: 'Fetching Products...',
  });
  if (!response) return null;
  return response;
};

const createHvacProduct = async (params) => {
  const url = '/api/products/createHvacProduct/';
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Adding New Product...',
  });
  if (!response) return null;
  return response;
};

const updateHvacProduct = async (params) => {
  const url = '/api/products/updateHvacProduct/';
  const response = await handleApiCall({
    url,
    method: 'put',
    params,
    loadingMessage: 'Updating Product...',
    successMessage: 'Successfully Updated...',
  });
  if (!response) return null;
  return response;
};

export default {
  getAllProductsByOpportunityId,
  deleteProductById,
  getProductsOptions,
  createHvacProduct,
  updateHvacProduct,
};
