import React, { memo } from 'react';
import styled from 'styled-components';
import { calculateVentMathRound } from '@utils/functions';
import PropTypes from 'prop-types';
import {
  DarkGreenCell,
  LightGreenCell,
  LightYellowCell,
  OrangeCell,
  RedCell,
  TableCell,
  TableHeader,
  TableRow,
  Table,
} from './TableCellsColors';

const Label = styled.span`
  font-size: 12px;
`;

const VentilationContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 300px;
`;

export const VentilationNeeded = memo(function VentilationNeeded({
  atticSqFt = 0,
  highVentingTotal = 0,
  lowVentingTotal = 0,
}) {
  return (
    <VentilationContainer>
      <Table>
        <thead>
          <TableRow>
            <TableHeader colSpan="3">Ventilation Needed</TableHeader>
          </TableRow>
        </thead>
        <thead>
          <TableRow>
            {/* eslint-disable-next-line react/self-closing-comp */}
            <TableHeader></TableHeader>
            <TableHeader>High</TableHeader>
            <TableHeader>Low</TableHeader>
          </TableRow>
        </thead>
        <tbody>
          <TableRow>
            <DarkGreenCell>50/50</DarkGreenCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.5 - highVentingTotal), 0)}
            </TableCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.5 - lowVentingTotal), 0)}
            </TableCell>
          </TableRow>
          <TableRow>
            <LightGreenCell>60/40</LightGreenCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.6 - highVentingTotal), 0)}
            </TableCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.4 - lowVentingTotal), 0)}
            </TableCell>
          </TableRow>
          <TableRow>
            <LightGreenCell>40/60</LightGreenCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.4 - highVentingTotal), 0)}
            </TableCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.6 - lowVentingTotal), 0)}
            </TableCell>
          </TableRow>
          <TableRow>
            <LightYellowCell>75/25</LightYellowCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.75 - highVentingTotal), 0)}
            </TableCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.25 - lowVentingTotal), 0)}
            </TableCell>
          </TableRow>
          <TableRow>
            <LightYellowCell>25/75</LightYellowCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.25 - highVentingTotal), 0)}
            </TableCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0.75 - lowVentingTotal), 0)}
            </TableCell>
          </TableRow>
          <TableRow>
            <OrangeCell>100/0</OrangeCell>
            <TableCell>
              {Math.max(calculateVentMathRound(atticSqFt / 150 - highVentingTotal), 0)}
            </TableCell>
            <TableCell>
              {Math.max(calculateVentMathRound((atticSqFt / 300) * 0 - lowVentingTotal), 0)}
            </TableCell>
          </TableRow>
          <TableRow>
            <RedCell>0/100</RedCell>
            <TableCell>.35</TableCell>
            <TableCell>
              {Math.max(calculateVentMathRound(atticSqFt / 150 - lowVentingTotal), 0)}
            </TableCell>
          </TableRow>
        </tbody>
      </Table>
      <Label>** You may be within .09 of the required venting (in total) **</Label>
    </VentilationContainer>
  );
});

VentilationNeeded.propTypes = {
  atticSqFt: PropTypes.number,
  highVentingTotal: PropTypes.number,
  lowVentingTotal: PropTypes.number,
};
