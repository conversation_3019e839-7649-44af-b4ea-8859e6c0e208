import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const WXManagerIncentiveForm = ({ record = {} }) => {
  const {
    finalContractAmount,
    dealId,
    hesOriginalContractAmount,
    hesBmsCommissionTotal,
    managerIncentive,
  } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="hesOriginalContractAmount"
            value={hesOriginalContractAmount}
            title="HES Original Contract Amount"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="finalContractAmount"
            value={finalContractAmount}
            title="Final Contract Amount"
            placeholder=""
          />
          <FormInput
            readOnly
            name="hesBmsCommissionTotal"
            value={hesBmsCommissionTotal}
            title="HES BMS Comission Total"
            placeholder=""
          />
          <FormInput
            readOnly
            name="managerIncentive"
            value={managerIncentive}
            title="Manager Incentive"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

WXManagerIncentiveForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    finalContractAmount: PropTypes.string,
    hesOriginalContractAmount: PropTypes.string,
    hesBmsCommissionTotal: PropTypes.string,
    managerIncentive: PropTypes.string,
  }),
};

export default WXManagerIncentiveForm;
