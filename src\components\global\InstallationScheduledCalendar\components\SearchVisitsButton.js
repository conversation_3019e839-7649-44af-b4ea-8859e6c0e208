import React from 'react';
import { useSetRecoilState } from 'recoil';
import { SecondaryButton } from '@components/global/Buttons';
import { showSidebarState, isSlotsSearchAtom } from '@recoil/eventSidebar';
import { isEventsSearchState } from '@recoil/eventsSearch';

const SearchVisitsButton = () => {
  const setShowSidebar = useSetRecoilState(showSidebarState);
  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);
  const setIsEventSearch = useSetRecoilState(isEventsSearchState);

  const showSearchSidebar = () => {
    setIsSlotsSearch(false);
    setIsEventSearch(true);
    setShowSidebar(true);
  };

  return <SecondaryButton onClick={showSearchSidebar}>Search for a Visit</SecondaryButton>;
};

export default SearchVisitsButton;
