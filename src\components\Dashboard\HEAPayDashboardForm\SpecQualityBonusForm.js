import React from 'react';
import PropTypes from 'prop-types';

import { Row, Col, FormInput } from '@components/global/Form';

const SpecQualityBonusForm = ({ record = {} }) => {
  const { dealId, finalContractAmount, goodSpecAreas, badSpecAreas, goodSpec } = record;
  return (
    <>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="goodSpecAreas"
            value={goodSpecAreas}
            title="Good Spec Areas"
            placeholder=""
          />
          <FormInput readOnly name="goodSpec" value={goodSpec} title="Good Spec" placeholder="" />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="finalContractAmount"
            value={finalContractAmount}
            title="Final Contract Amount"
            placeholder=""
          />
          <FormInput
            readOnly
            name="badSpecAreas"
            value={badSpecAreas}
            title="Bad Spec Areas"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

SpecQualityBonusForm.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    finalContractAmount: PropTypes.string,
    goodSpecAreas: PropTypes.string,
    badSpecAreas: PropTypes.string,
    goodSpec: PropTypes.string,
  }),
};

export default SpecQualityBonusForm;
