import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import styled from 'styled-components';

import { decodeEventType } from '@homeworksenergy/utility-service';

import { useSetRecoilState, useResetRecoilState } from 'recoil';

import Swal from 'sweetalert2/dist/sweetalert2';

import { Button } from '@components/global/Buttons';

import { handleFormFieldChange } from '@components/global/Form';
import { Payment } from '@components/Calendar/EventComponents';

import {
  selectedEventState,
  availableSlotsAtom,
  isSlotsSearchAtom,
  showSidebarState,
} from '@recoil/eventSidebar';

import { UtilityManager } from '@utils/APIManager';
import { redirectScheduleWx } from '@utils/functions';

const EventFooterContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-bottom: 10px;
  @media (max-width: 1149px) {
    display: flex;
    flex-direction: column-reverse;
    align-items: inherit;
  }
`;

const ScheduleButtonContainer = styled.div`
  display: flex;
  @media (max-width: 1149px) {
    padding-top: 10px;
  }
  @media (max-width: 450px) {
    display: flex;
    flex-direction: column;
  }
`;

const StyledScheduleButton = styled(Button)`
  color: ${({ theme }) => theme.colors.eventGreen};
  border-color: ${({ theme }) => theme.colors.eventGreen};
  margin-right: 10px;
  @media (max-width: 1149px) {
    margin-left: 0px;
    margin-right: 32px;
    width: 175px;
  }
  @media (max-width: 450px) {
    margin-top: 10px;
    margin-bottom: 10px;
  }
`;

const StyledLink = styled.a`
  padding-right: 15px;
  padding-left: 15px;
  text-decoration: underline;
  @media (max-width: 1149px) {
    padding-left: 3.2px;
    padding-right: 32px;
  }
  @media (max-width: 450px) {
    padding-top: 6px;
    padding-bottom: 6px;
  }
`;

const EventLinksContainer = styled.div`
  @media (max-width: 1149px) {
    padding-top: 10px;
  }
  @media (max-width: 450px) {
    display: flex;
    flex-direction: column;
  }
`;

const EventListFooter = ({ event, history = { push: '' } }) => {
  const {
    type,
    phoneNumber = '',
    address = {
      street: '',
      city: '',
      state: '',
      postalCode: '',
      displayAddress: '',
      country: 'USA',
    },
    selectedUnitSfIds: { dealId: selectedDealId, operationsId, opportunityId },
    customerName = '',
    leadVendor = '',
    numUnit = 1,
    notes = {},
    sfIds = {},
  } = event;

  const { state, business } = decodeEventType(type);
  const eventDepartment = business.replace(' ', '_');
  const paymentRef = useRef();

  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);
  const setSelectedEvent = useSetRecoilState(selectedEventState);
  const setShowSidebar = useSetRecoilState(showSidebarState);
  const resetAvailableSlots = useResetRecoilState(availableSlotsAtom);

  const slicedEventType = type.slice(0, 4);
  const isHVACSales = slicedEventType === '0001';
  const isWx = slicedEventType === '0005';
  const isCT = ['0100', '0106'].includes(slicedEventType);
  const isWxCAP = isWx && leadVendor === 'CAP';
  const displayScheduleHVACButton = !isHVACSales && !isWx && selectedDealId && address;
  let docRepoLink = '';
  let salesforceLinkId = selectedDealId;
  if (salesforceLinkId) {
    docRepoLink = UtilityManager.openDocRepo(
      selectedDealId,
      customerName,
      leadVendor,
      numUnit,
      true,
    );
  }
  if (isHVACSales) {
    salesforceLinkId = opportunityId;
    docRepoLink = `/doc-repo/${state}/${eventDepartment}/${opportunityId}`;
  }

  const showNewDocRepo = type === '000006' || isWxCAP;

  // eslint-disable-next-line consistent-return
  const handleHVACScheduleClick = async () => {
    if (notes?.fieldNotes.includes('DO NOT book HVAC Sales call')) {
      return Swal.fire({
        icon: 'error',
        title:
          'This customer already has an HVAC appointment booked with our partner company. DO NOT BOOK AN HVAC SALES APPOINTMENT.',
      });
    }
    const canSchedule = await UtilityManager.validateZipCode(address?.postalCode, '000100');
    if (canSchedule) {
      handleFormFieldChange(
        { target: { name: 'type', value: '000100' } },
        { ...event, notes: { ...event.notes, fieldNotes: '' } },
        setSelectedEvent,
      );
      resetAvailableSlots();
      setIsSlotsSearch(true);
      setShowSidebar(true);
    } else {
      Swal.fire({ icon: 'error', title: 'Out of Territory Zip Code for HVAC Schedule.' });
    }
  };

  const handleCapDocRepoClick = () => {
    history.push(`/view-doc-repo/MA/HEA-CAP/${event.accountId}}`);
  };

  const handlePaymentButtonClick = () => {
    setSelectedEvent(event);
    paymentRef.current.handlePaymentButtonClick();
  };

  const renderWorkReceiptLink = () => {
    if (!isHVACSales && !isWx) {
      const { dealId, dealId2, dealId3, dealId4 } = sfIds;
      const unitDealIdsArr = [dealId, dealId2, dealId3, dealId4];
      const filteredDeals = unitDealIdsArr.filter((item) => item);
      const isMulti = filteredDeals.length > 1;
      const href = `${window.location.origin}/work-receipt-dev?dealsParams=${selectedDealId}&isCap=${isWxCAP}&isOffline=true&isMulti=${isMulti}`;
      return (
        <StyledLink href={href} target="_blank" rel="noopener">
          Work Receipt
        </StyledLink>
      );
    }
    return null;
  };

  const renderDocRepoLink = () => {
    if (showNewDocRepo) {
      return (
        <StyledLink href="#" target="_self" onClick={handleCapDocRepoClick}>
          CAP Doc Repo
        </StyledLink>
      );
    }
    if (!showNewDocRepo) {
      return (
        <StyledLink href={docRepoLink} target="_blank" rel="noopener">
          MR Doc Repo
        </StyledLink>
      );
    }
    return null;
  };

  return (
    <EventFooterContainer>
      <ScheduleButtonContainer>
        {displayScheduleHVACButton && (
          <StyledScheduleButton onClick={() => handleHVACScheduleClick()}>
            Schedule HVAC
          </StyledScheduleButton>
        )}
        {!isHVACSales && !isWx && operationsId && (
          <StyledScheduleButton onClick={() => redirectScheduleWx(operationsId)}>
            Schedule Insulation
          </StyledScheduleButton>
        )}
        {!isCT && (
          <Payment ref={paymentRef} showButton onClick={() => handlePaymentButtonClick()} />
        )}
      </ScheduleButtonContainer>
      <EventLinksContainer>
        {phoneNumber?.length && <StyledLink href={`tel:${phoneNumber}`}>Call</StyledLink>}
        {address && (
          <StyledLink
            href={`http://maps.google.com/?q=${address.street},${address.city},${address.state} ${address.postalCode}`}
            target="_blank"
            rel="noopener"
          >
            Directions
          </StyledLink>
        )}
        {!isCT && salesforceLinkId?.length && (
          <>
            {renderDocRepoLink()}

            <StyledLink
              href={
                window.location.origin === 'https://sch.homeworksenergy.com'
                  ? `https://na9.salesforce.com/${salesforceLinkId}`
                  : `https://homeworks--uatsandbox.my.salesforce.com/${salesforceLinkId}`
              }
              target="_blank"
              rel="noopener"
            >
              Salesforce
            </StyledLink>
            {renderWorkReceiptLink()}
          </>
        )}
      </EventLinksContainer>
    </EventFooterContainer>
  );
};

EventListFooter.propTypes = {
  event: PropTypes.shape({
    phoneNumber: PropTypes.string,
    address: PropTypes.shape({
      street: PropTypes.string,
      city: PropTypes.string,
      state: PropTypes.string,
      postalCode: PropTypes.string,
      displayAddress: PropTypes.string,
      country: PropTypes.string,
    }),
    selectedUnitSfIds: PropTypes.shape({
      dealId: PropTypes.string,
      operationsId: PropTypes.string,
      opportunityId: PropTypes.string,
      salesVisitId: PropTypes.string,
      hvacVisitId: PropTypes.string,
    }),
    sfIds: PropTypes.objectOf(PropTypes.string),
    type: PropTypes.string,
    notes: PropTypes.objectOf(PropTypes.string),
    customerName: PropTypes.string,
    leadVendor: PropTypes.string,
    numUnit: PropTypes.number,
    accountId: PropTypes.string,
  }),
  history: PropTypes.shape({ push: PropTypes.string }),
};

export default withRouter(EventListFooter);
