import React from 'react';
import Swal from 'sweetalert2/dist/sweetalert2';
import { parseJSXContentForSwalPopup } from '@utils/functions';
import styled from 'styled-components';

const Text = styled.div`
  font-size: 16px;
`;

export const capWorkReceiptValidations = (formValues, deals) => {
  let isFormValid = true;
  const errorMessage = {
    customerInfo: 'Invalid Field Entry on Customer Info',
    roadblocksInfo: 'Invalid Field Entry on Roadblocks Info',
    roadblocksInfoPrelim: 'Invalid Year Entry on Roadblocks Info',
    basInfo: 'Invalid Field Entry on BAS Info',
    hvacInfo: 'Invalid Field Entry on HVAC Info',
    incomeEligibleProjInfo: 'Invalid Field Entry on Income Eligible Project Info',
    ampInfo: 'Invalid Field Entry on AMP Info',
    postHea: 'Invalid Field Entry on Post HEA',
    propertyOwner: 'Invalid Field Entry on Property Owner',
  };

  const showError = (tab, field, dealNumber, dealId, message) => {
    Swal.fire({
      icon: 'error',
      title: `${message}: Deal #${dealNumber} - ${dealId}`,
      html: parseJSXContentForSwalPopup(
        <Text>
          Error in field {field} on tab {tab}.
        </Text>,
      ),
      confirmButtonText: 'OK',
      showCancelButton: false,
    });
  };

  for (let iterator = 0; iterator < formValues.length; iterator++) {
    const {
      customerName,
      customerAddress,
      cityStateZip,
      customerEmail,
      customerPhone,
      siteId,
      whatRegionIsThis,
      auditorName,
      auditorEmail,
      intakeIssue,
      hvacQuality,
      heaHvacNotes,
      hvacInterest,
      gasAvailable,
      disclosureWallsTempAccess,
      interestedInHvac,
      disclosureCstFaileddraftAstmosphericNaturalConditions,
      disclosureCstFailedSpillageSealedSystem,
      disclosureCstFailedCoAtmostphericSystem,
      disclosureCstFailedCoSealedSystem,
      disclosureCstDraftSpillageDetail,
      disclosureCstHighCoDetail,
      systemsFailingCoMultiselect,
      heatingSystemCO,
      domesticHotWaterCarbonMonoxide,
      failedCstOther,
      otherCombustionAppliance,
      failedDraftDetailMulti,
      failedSpillageDetailMulti,
      disclosureCstFailedCoGasOvenLevel2,
      yearHouseBuilt,
      buildingType,
      finishedSqFt,
      numberOfUnitsPerformedToday,
      conditionedStories,
      sidingMaterial,
      basementType,
      ampEligible,
      ampPerformed,
      ownerOccupancyType,
      preferredLanguage,
      heaVisitResult,
    } = formValues[iterator];
    if (
      !customerName ||
      !customerAddress ||
      !cityStateZip ||
      !customerEmail ||
      !customerPhone ||
      !siteId ||
      !whatRegionIsThis ||
      !auditorName ||
      !auditorEmail ||
      !intakeIssue
    ) {
      showError(
        'Customer Info',
        'One or more fields',
        iterator + 1,
        deals[iterator],
        errorMessage.customerInfo,
      );
      isFormValid = false;
      break;
    }

    if (!disclosureWallsTempAccess) {
      showError(
        'Roadblocks Info',
        'Disclosure Walls Temp Access',
        iterator + 1,
        deals[iterator],
        errorMessage.roadblocksInfoPrelim,
      );
      isFormValid = false;
      break;
    }

    if (disclosureCstFailedCoGasOvenLevel2 === 'Yes' && !otherCombustionAppliance) {
      showError(
        'Roadblocks Info',
        'Other Combustion Appliance',
        iterator + 1,
        deals[iterator],
        errorMessage.roadblocksInfo,
      );
      isFormValid = false;
      break;
    }
    if (
      [
        disclosureCstFaileddraftAstmosphericNaturalConditions,
        disclosureCstFailedSpillageSealedSystem,
      ].includes('Yes')
    ) {
      if (!disclosureCstDraftSpillageDetail) {
        showError(
          'Roadblocks Info',
          'Disclosure Cst Draft Spillage Detail',
          iterator + 1,
          deals[iterator],
          errorMessage.roadblocksInfo,
        );
        isFormValid = false;
        break;
      }

      if (
        disclosureCstFaileddraftAstmosphericNaturalConditions === 'Yes' &&
        (!failedDraftDetailMulti.length || !failedSpillageDetailMulti.length)
      ) {
        showError(
          'Roadblocks Info',
          'Failed Draft Detail or Failed Spillage Detail',
          iterator + 1,
          deals[iterator],
          errorMessage.roadblocksInfo,
        );
        isFormValid = false;
        break;
      }
      if (disclosureCstFailedSpillageSealedSystem === 'Yes' && !failedSpillageDetailMulti.length) {
        showError(
          'Roadblocks Info',
          'Failed Spillage Detail Multi',
          iterator + 1,
          deals[iterator],
          errorMessage.roadblocksInfo,
        );
        isFormValid = false;
        break;
      }
    }
    if (
      [disclosureCstFailedCoAtmostphericSystem, disclosureCstFailedCoSealedSystem].includes('Yes')
    ) {
      if (!disclosureCstHighCoDetail) {
        showError(
          'Roadblocks Info',
          'Disclosure Cst High CO Detail',
          iterator + 1,
          deals[iterator],
          errorMessage.roadblocksInfo,
        );
        isFormValid = false;
        break;
      }

      if (!systemsFailingCoMultiselect.length) {
        showError(
          'Roadblocks Info',
          'Systems Failing CO Multiselect',
          iterator + 1,
          deals[iterator],
          errorMessage.roadblocksInfo,
        );
        isFormValid = false;
      }

      if (systemsFailingCoMultiselect.includes('Heating System') && !heatingSystemCO) {
        showError(
          'Roadblocks Info',
          'Heating System CO',
          iterator + 1,
          deals[iterator],
          errorMessage.roadblocksInfo,
        );
        isFormValid = false;
        break;
      }
      if (systemsFailingCoMultiselect.includes('DHW') && !domesticHotWaterCarbonMonoxide) {
        showError(
          'Roadblocks Info',
          'Domestic Hot Water CO',
          iterator + 1,
          deals[iterator],
          errorMessage.roadblocksInfo,
        );
        isFormValid = false;
        break;
      }
      if (
        systemsFailingCoMultiselect.includes('Other') &&
        (!otherCombustionAppliance || !failedCstOther)
      ) {
        showError(
          'Roadblocks Info',
          'Other Combustion Appliance or Failed CST Other',
          iterator + 1,
          deals[iterator],
          errorMessage.roadblocksInfo,
        );
        isFormValid = false;
        break;
      }
    }

    if (interestedInHvac !== 'No - Out of Territory') {
      if (!hvacQuality || !heaHvacNotes || !hvacInterest || !gasAvailable) {
        showError(
          'HVAC Info',
          'One or more fields',
          iterator + 1,
          deals[iterator],
          errorMessage.hvacInfo,
        );
        isFormValid = false;
        break;
      }
    }

    if (
      iterator === 0 &&
      (!yearHouseBuilt ||
        !buildingType ||
        !finishedSqFt ||
        !numberOfUnitsPerformedToday ||
        !conditionedStories ||
        !sidingMaterial ||
        !basementType)
    ) {
      showError(
        'Income Eligible Project Info',
        'One or more fields',
        iterator + 1,
        deals[iterator],
        errorMessage.incomeEligibleProjInfo,
      );
      isFormValid = false;
      break;
    }

    if (!ampEligible || !ampPerformed) {
      showError(
        'AMP Info',
        'AMP Eligible or AMP Performed',
        iterator + 1,
        deals[iterator],
        errorMessage.ampInfo,
      );
      isFormValid = false;
      break;
    }

    if (!ownerOccupancyType && iterator === 0) {
      showError(
        'Property Owner',
        'Occupany Type',
        iterator + 1,
        deals[iterator],
        errorMessage.propertyOwner,
      );
      isFormValid = false;
      break;
    }

    if (heaVisitResult === 'HEA Performed' && !preferredLanguage) {
      showError(
        'POST HEA',
        'Preferred Language',
        iterator + 1,
        deals[iterator],
        errorMessage.postHea,
      );
      isFormValid = false;
      break;
    }
  }

  return isFormValid;
};
