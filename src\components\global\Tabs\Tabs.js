import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState } from 'recoil';

import { activeTabIndexAtom } from '@recoil/app';
import TabButton from '@components/global/Tabs/TabButton';
import { CheckCircle } from '@styled-icons/boxicons-solid/CheckCircle';
import { uniqueId } from 'lodash';

const TabsContainer = styled.div`
  display: flex;
  flex-shrink: 0;
  background: ${({ theme }) => theme.secondary[200]};
  padding-left: 5px;
  overflow-x: auto;
  &::-webkit-scrollbar {
    height: 7px !important;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 5px;
  }
`;

const TabsButtonContainer = styled.div`
  margin: 0 5px;
  display: flex;
  flex-direction: row;
`;

const CheckIconWrapper = styled.div`
  align-self: center;
  margin-left: 3px;
`;

const CheckIcon = styled(CheckCircle)`
  color: ${({ theme }) => theme.colors.green};
  /* With Styled Component React does not recognize prop as boolean on DOM Element */
  visibility: ${({ show }) => (show === 'true' ? 'visible' : 'hidden')};
  width: 20px;
`;

const Tabs = ({ tabs, onTabChange = false, stepValidation = false, id = 'tabs' }) => {
  const [activeTabIndex, setActiveTabIndex] = useRecoilState(activeTabIndexAtom([id]));

  const handleTabIndexChange = (newTabIndex, name) => {
    if (tabs[activeTabIndex]?.onTabExit) tabs[activeTabIndex].onTabExit();
    if (tabs[newTabIndex]?.onTabEnter) tabs[newTabIndex].onTabEnter();

    const isNextTabEnabled =
      (stepValidation && stepValidation[newTabIndex - 1]) ||
      (newTabIndex < activeTabIndex && stepValidation[newTabIndex]);
    // StepValidation Not, means Tab can be used without any validation.
    // Example can be seen on EditAgentForm Component
    // If StepsValidation is passed then onChange tab it will check Above Variable
    // If current Tab validation is passed then proceed to next
    if (!stepValidation) {
      setActiveTabIndex(newTabIndex);
      handleTabChangeName(name);
    }
    if (isNextTabEnabled) {
      setActiveTabIndex(newTabIndex);
      handleTabChangeName(name);
    }
  };

  const handleTabChangeName = (name) => {
    if (onTabChange) onTabChange(name);
  };
  return (
    <TabsContainer>
      {tabs.map(({ title, name }, index) => (
        <TabsButtonContainer key={uniqueId()}>
          <TabButton
            title={title}
            active={activeTabIndex === index}
            onClick={() => handleTabIndexChange(index, name)}
          />
          {stepValidation && (
            <CheckIconWrapper>
              <CheckIcon show={stepValidation?.[index]?.toString()} />
            </CheckIconWrapper>
          )}
        </TabsButtonContainer>
      ))}
    </TabsContainer>
  );
};

Tabs.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      name: PropTypes.string,
      onTabEnter: PropTypes.func,
      onTabExit: PropTypes.func,
    }),
  ).isRequired,
  onTabChange: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),
  stepValidation: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.bool), PropTypes.bool]),
  id: PropTypes.string,
};

export default Tabs;
