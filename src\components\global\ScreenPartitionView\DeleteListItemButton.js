import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { X as xIcon } from '@styled-icons/boxicons-regular/X';

import { Clickable } from '@components/global';

const StyledDeleteIcon = styled(xIcon)`
  align-self: center;
  color: ${({ theme }) => theme.secondary[500]};
  height: 45px;
`;

// TODO: can delete role function be brought into this component?
const DeleteListItemButton = ({ deleteItem }) => {
  return (
    <Clickable>
      <StyledDeleteIcon onClick={deleteItem} />
    </Clickable>
  );
};

DeleteListItemButton.propTypes = {
  deleteItem: PropTypes.func.isRequired,
};

export default DeleteListItemButton;
