import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import styled, { useTheme } from 'styled-components';
import { Plus } from '@styled-icons/boxicons-regular/Plus';
import { PrimaryButton } from '@components/global/Buttons';
import {
  Row,
  Col,
  FormInput,
  FormTextBox,
  FormInputPhoneNumber,
  GoogleAddressInput,
  ButtonContainer,
  FormMultiselect,
  FormFieldLabel,
  FormFieldContainer,
} from '@components/global/Form';
import { Checkbox, Header as Title, AgentImage } from '@components/global';
import StyledAgentSlot from '@components/global/StyledAgentSlot';
import { CustomerManager, UsersManager } from '@utils/APIManager';
import { formatPhoneNumber, parseGoogleAutocomplete } from '@utils/functions';
import validateReferralDetails from '@utils/referralValidation';

const PlusIcon = styled(Plus)`
  height: 22px;
`;

const FormContainer = styled(Row)`
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 16px;
`;

const FieldsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
`;

const AgentName = styled.div`
  margin-top: 10px;
`;

const ReferralForm = ({ customerId, referralList, employeeList, setReferralList }) => {
  // State to track all form instances
  const [forms, setForms] = useState([]);
  const [crewLeadOptions, setCrewLeadOptions] = useState([]);
  const [auditorEmployeeReferral, setAuditorEmployeeReferral] = useState(null);

  const theme = useTheme();

  const isCustomeReferred = customerId.length > 0;

  useEffect(() => {
    const getUsers = async () => {
      const users = await UsersManager.getUsersInfo();
      const crewLeadOptions = users.map(({ displayName, oid }) => {
        return { key: displayName, value: oid };
      });
      setCrewLeadOptions(crewLeadOptions);
    };
    getUsers();
  }, []);

  useEffect(() => {
    if (employeeList?.length === 1) setAuditorEmployeeReferral(employeeList[0].sfId);
  }, [referralList]);

  // Used when you click an address in the google auto complete
  const handleGoogleLocationInput = (autocomplete, index) => {
    const { city } = parseGoogleAutocomplete(autocomplete);
    if (city) handleInputChange(index, 'town', city);
  };

  // Function to add a new form
  const addForm = () => {
    setForms([
      ...forms,
      {
        firstName: null,
        lastName: null,
        phone: null,
        email: null,
        town: null,
        crewLead: null,
        customerReferral: null || isCustomeReferred,
        notes: '',
        customerId,
      },
    ]);
  };

  // Function to handle input changes
  const handleInputChange = (index, fieldName, value) => {
    const cloneForm = [...forms];
    cloneForm[index][fieldName] = value;
    setForms(cloneForm);
  };

  const handleSubmit = async () => {
    const insertReferral = validateReferralDetails(forms, auditorEmployeeReferral);
    if (!insertReferral) return;
    const referral = await CustomerManager.insertReferral(insertReferral);
    setForms([]);
    setAuditorEmployeeReferral(null);
    setReferralList([...referral, ...referralList]);
  };

  // Function to check if a form is valid (for submit button disabling)
  const isFormValid = () => {
    if (!auditorEmployeeReferral || forms.length === 0) return true;
    const requiredFields = ['firstName', 'lastName', 'phone', 'town'];
    const filteredForms = forms.filter((form) => {
      const filteredFields = requiredFields.filter((fieldName) => {
        return !form[fieldName];
      });
      return filteredFields.length;
    });
    return filteredForms.length > 0;
  };

  // Function to remove a form
  const removeForm = (formIndex) => {
    setForms(forms.filter((form, index) => index !== formIndex));
  };

  const renderAuditorEmployeeList = () => {
    return (
      <FormFieldContainer>
        <FormFieldLabel>Referred by Auditor Employee: </FormFieldLabel>
        <Row>
          {employeeList?.length === 0 ? (
            <Title h4>No Homeworks Energy Employee found.</Title>
          ) : (
            employeeList.map(({ firstname, lastname, sfId }) => {
              const selected = sfId === auditorEmployeeReferral;
              return (
                <StyledAgentSlot
                  onClick={() => setAuditorEmployeeReferral(sfId)}
                  selected={selected}
                  key={sfId}
                  theme={theme}
                >
                  <AgentImage imageUrl={null} />
                  <AgentName>
                    {firstname} {lastname}
                  </AgentName>
                </StyledAgentSlot>
              );
            })
          )}
        </Row>
      </FormFieldContainer>
    );
  };

  return (
    <FormContainer>
      {forms.map((form, index) => {
        return (
          <FieldsContainer key={`customer-${form.firstname}`}>
            <Row>
              <Col>
                <FormInput
                  required
                  value={form.firstName}
                  title="First Name:"
                  placeholder="Enter First Name"
                  onChange={(e) => handleInputChange(index, 'firstName', e.target.value)}
                />
              </Col>
              <Col>
                <FormInput
                  required
                  value={form.lastName}
                  title="Last Name:"
                  placeholder="Enter Last Name"
                  onChange={(e) => handleInputChange(index, 'lastName', e.target.value)}
                />
              </Col>
            </Row>
            <Row>
              <Col>
                <FormInputPhoneNumber
                  required
                  title="Phone:"
                  placeholder="************"
                  value={formatPhoneNumber(form.phone)}
                  onChange={(e) => handleInputChange(index, 'phone', e.target.value)}
                  type="phone"
                />
              </Col>
              <Col>
                <FormInput
                  value={form.email}
                  title="Email Address:"
                  placeholder="Enter Email"
                  onChange={(e) => handleInputChange(index, 'email', e.target.value)}
                />
              </Col>
            </Row>
            <Row>
              <Col>
                <GoogleAddressInput
                  required
                  title="Town:"
                  name="town"
                  value={form.town}
                  onPlaceChange={(autocomplete) => handleGoogleLocationInput(autocomplete, index)}
                  onChange={(e) => handleInputChange(index, 'town', e.target.value)}
                  uniqueId={`town${index}`}
                  type="(cities)"
                />
              </Col>
              <Col>
                <FormMultiselect
                  title="Crew Lead"
                  name="crewLead"
                  value={form.crewLead}
                  options={crewLeadOptions}
                  onChange={(e) => handleInputChange(index, 'crewLead', e.target.value)}
                />
              </Col>
            </Row>
            {isCustomeReferred && (
              <Row>
                <Col>
                  <Checkbox
                    label="Referred By Customer"
                    name="customerReferral"
                    value={form.customerReferral}
                    onChange={(e) => handleInputChange(index, 'customerReferral', e.target.value)}
                  />
                </Col>
              </Row>
            )}
            <Row>
              <Col>
                <FormTextBox
                  value={form.notes}
                  title="Internal Notes"
                  onChange={(e) => handleInputChange(index, 'notes', e.target.value)}
                />
              </Col>
            </Row>
            <Row>
              <Col>
                <PrimaryButton width="-webkit-fill-available" onClick={() => removeForm(index)}>
                  Remove Referral
                </PrimaryButton>
              </Col>
            </Row>
          </FieldsContainer>
        );
      })}
      <Row>
        <ButtonContainer>
          <PrimaryButton role="button" onClick={addForm}>
            <PlusIcon />
            Add New Referral
          </PrimaryButton>
        </ButtonContainer>
      </Row>
      <Row>
        <Col>{renderAuditorEmployeeList()}</Col>
      </Row>
      <Row>
        <Col>
          <PrimaryButton
            width="-webkit-fill-available"
            onClick={() => handleSubmit()}
            disabled={isFormValid()}
          >
            Submit Referral
          </PrimaryButton>
        </Col>
      </Row>
    </FormContainer>
  );
};

ReferralForm.propTypes = {
  customerId: PropTypes.string,
  referralList: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  setReferralList: PropTypes.func.isRequired,
  employeeList: PropTypes.arrayOf(PropTypes.shape({ sfId: PropTypes.string })).isRequired,
};

ReferralForm.defaultProps = {
  customerId: '',
};

export default ReferralForm;
