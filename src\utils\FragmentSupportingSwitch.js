import React from 'react';
import PropTypes from 'prop-types';
import { isFragment } from 'react-is';
import { Switch } from 'react-router-dom';

const FragmentSwitch = (props) => {
  const flatten = (flattendChildren, children) => {
    children.forEach((child) => {
      if (React.isValidElement(child)) {
        if (isFragment(child)) {
          flatten(flattendChildren, child.props.children);
        } else {
          flattendChildren.push(child);
        }
      }
    });
  };
  const { children } = props;
  const flattenedChildren = [];
  flatten(flattenedChildren, children);
  return <Switch>{flattenedChildren}</Switch>;
};

FragmentSwitch.propTypes = {
  children: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.node), PropTypes.shape({})])
    .isRequired,
};

export default FragmentSwitch;
