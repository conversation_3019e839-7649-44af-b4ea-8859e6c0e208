import { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import moment from 'moment';
import _ from 'lodash';
import utilityService from '@homeworksenergy/utility-service';

import { useHasChanged } from '@hooks';
import { selectedEventState } from '@recoil/eventSidebar';

const useStartEndTimes = () => {
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);

  const {
    id,
    associatedEventIds,
    date,
    jobLength,
    startTime,
    startEndTimes = [], // [{start: '', end: ''}, {start: '', end: ''}]
  } = selectedEvent;

  // The event won't have an ID yet if we're just creating it.
  const isCreateEvent = !id;

  const dateChanged = useHasChanged(date);
  const jobLengthChanged = useHasChanged(jobLength);
  const associatedEventIdsChanged = useHasChanged(associatedEventIds);

  useEffect(() => {
    if (!date) return;
    if (!dateChanged && !jobLengthChanged && !associatedEventIdsChanged) return;

    const updateStartEndTimes = () => {
      const defaultTimes = utilityService.getDefaultStartEndTimes(
        jobLength,
        date,
        undefined,
        parseInt(startTime, 10),
      );

      return defaultTimes.map((defaultStartEnd, index) => {
        const isFinalDay = index === Math.ceil(jobLength) - 1;

        const existingStartEndTime = startEndTimes[index];

        // If there is an existing time
        if (existingStartEndTime) {
          // If it's not the final day (for partial days)
          if (!isFinalDay) {
            // Return existing times
            return existingStartEndTime;
          }
        }
        // Else, return default 9 - 5
        return defaultStartEnd;
      });
    };

    let newEvent = selectedEvent;

    // When selecting a different empty date
    if (dateChanged && isCreateEvent) {
      // Set default startendtimes
      const startEndTimes = utilityService.getDefaultStartEndTimes(
        jobLength,
        date,
        undefined,
        parseInt(startTime, 10),
      );

      newEvent = { ...newEvent, startEndTimes };
    }

    // When adding or removing dates
    if (jobLengthChanged && !associatedEventIdsChanged) {
      const startEndTimes = updateStartEndTimes();
      newEvent = { ...newEvent, startEndTimes };
    }

    setSelectedEvent({ ...newEvent });
  }, [
    isCreateEvent,
    date,
    jobLength,
    startTime,
    selectedEvent,
    dateChanged,
    jobLengthChanged,
    associatedEventIdsChanged,
    associatedEventIds,
    setSelectedEvent,
    startEndTimes,
  ]);

  const handleDateChange = (newDateTime) => {
    const startEndTimes = utilityService.getDefaultStartEndTimes(
      jobLength,
      newDateTime,
      undefined,
      parseInt(startTime, 10),
    );

    const date = moment(newDateTime).format('MM/DD/YYYY');

    return setSelectedEvent({ ...selectedEvent, date, startEndTimes });
  };

  const handleTimeChange = (newTime, startOrEnd, index = 0) => {
    if (
      index === 0 &&
      startOrEnd === 'start' &&
      !moment(newTime).isSame(moment(startEndTimes[0]?.start), 'date')
    )
      return handleDateChange(newTime);

    // Deep clone necessary so we don't directly mutate state
    const updatedTimes = _.cloneDeep(startEndTimes);
    updatedTimes[index][startOrEnd] = moment(newTime);
    return setSelectedEvent({ ...selectedEvent, startEndTimes: updatedTimes });
  };

  return handleTimeChange;
};

export default useStartEndTimes;
