import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';

const generateLink = (params) => {
  const { paymentType, amount, customAmount, location } = params;
  const requiredFields = {
    paymentType,
    amount: amount === 'customValue' ? customAmount : amount,
    location,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return true;
};

export default generateLink;
