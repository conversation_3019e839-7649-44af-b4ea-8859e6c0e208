import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useRecoilValue, useResetRecoilState } from 'recoil';

import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import { SecondaryButton } from '@components/global/Buttons';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  BackButton,
  FormTextBox,
} from '@components/global/Form';
import { LoadingIndicator, Checkbox } from '@components/global';

import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import SfIdInputs from '@components/global/Form/SfIdInputs';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import { eventTypeOptionsSelectorFamily, calendarTypeAtom } from '@recoil/app';
import { selectedEventState, availableSlotsAtom } from '@recoil/eventSidebar';

const FindSubSlotsFormCT = (props) => {
  const { handleFindSlotsClick, handleSaveClick } = props;

  const [slotInfo, setSlotInfo] = useRecoilState(selectedEventState);
  const { type, address, lock = false, notes, customerName } = slotInfo;

  const calendarType = useRecoilValue(calendarTypeAtom);
  const resetAvailableSlots = useResetRecoilState(availableSlotsAtom);
  const eventTypes = useRecoilValue(
    eventTypeOptionsSelectorFamily({
      departmentName: 'Subcontractor',
      showGroups: false,
      stateCode: calendarType?.slice(0, 2),
    }),
  );
  const [loading, setLoading] = useState(false);
  const [displayForm, setDisplayForm] = useState(true);

  useEffect(() => {
    if (!type) setSlotInfo({ ...slotInfo, type: calendarType });
  }, [type, calendarType, setSlotInfo, slotInfo]);

  const onViewSlotsButtonClick = async () => {
    const isValid = await handleFindSlotsClick();
    if (isValid) setDisplayForm(false);
  };

  const onClickBackButton = () => {
    resetAvailableSlots();
    setDisplayForm(true);
  };

  const handleFieldChange = useCallback(
    (e, updatedEvent = slotInfo) => {
      handleFormFieldChange(e, updatedEvent, setSlotInfo);
    },
    [slotInfo, setSlotInfo],
  );

  const handleCheckbox = (e) => {
    const { name } = e.target;
    handleFieldChange({ target: { name, value: !slotInfo[name] } });
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col size={2}>
            <HeaderTitle>
              {!displayForm && <BackButton onClick={onClickBackButton} />}
              Book Appointment
            </HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            {displayForm ? (
              <>
                <SfIdInputs sfObjectType="workOrder" setLoading={setLoading} />
                <FormSelect
                  title="job type"
                  name="type"
                  value={type}
                  options={eventTypes}
                  onChange={handleFieldChange}
                  placeholder="Event Type"
                />
                {customerName && (
                  <FormInput
                    readOnly
                    title="Customer Name"
                    name="customerName"
                    value={customerName}
                  />
                )}
                {address?.displayAddress && (
                  <FormInput
                    readOnly
                    title="Address"
                    name="address"
                    value={address.displayAddress}
                  />
                )}
              </>
            ) : (
              <>
                <AvailableSlots singleAgent allowAgentSelect={false} />
                <Row>
                  <Col>
                    <Checkbox
                      label="Lock Event"
                      name="lock"
                      value={lock}
                      onChange={handleCheckbox}
                    />
                    <FormTextBox
                      required={lock}
                      name="notes.officeNotes"
                      value={notes.officeNotes}
                      title="Notes"
                      placeholder=""
                      onChange={handleFieldChange}
                    />
                  </Col>
                </Row>
                <BookSlotsButton handleBookSlots={() => handleSaveClick()} />
              </>
            )}
          </Col>
        </Row>
      </EventSidebarBody>
      <EventSidebarFooter>
        {displayForm && (
          <SecondaryButton left onClick={onViewSlotsButtonClick}>
            View Available Slots
          </SecondaryButton>
        )}
      </EventSidebarFooter>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
    </SidebarForm>
  );
};

FindSubSlotsFormCT.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default FindSubSlotsFormCT;
