/* eslint-disable no-unused-vars */
import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';

const availability = (params) => {
  const { days, department, oids, overrideHoliday, region, startEndDateTime, action } = params;

  const requiredFields = {
    Department: department,
    Crews: oids,
    'Open/Close/Add Block': action,
    'Day of Week': days,
  };
  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const editCrew = {
    days,
    department,
    oids,
    overrideHoliday,
    region,
    startEndDateTime,
    action,
  };

  return editCrew;
};

const editInfoGroup = { availability };
export default editInfoGroup;
