import { atom, selector, DefaultValue } from 'recoil';
import { offlineWorkReceiptFields } from '@pages/WorkReceipt/FormSchema/fieldsMap';
import { getDataIntakeFormValues } from '@components/DataIntakeForm/dataIntakeFormHelpers';

const defaultValues = getDataIntakeFormValues(offlineWorkReceiptFields);
const numberOfIndices = 1;
const offlineWorkReceiptAtom = [];

Array.from({ length: numberOfIndices }, (_, index) => {
  const atoms = {};
  Object.entries(defaultValues).forEach(([fieldName, defaultValue]) => {
    atoms[fieldName] = atom({
      key: `offlineWorkReceiptAtom-${index}-${fieldName}`,
      default: defaultValue,
    });
  });
  return offlineWorkReceiptAtom.push(atoms);
});

const offlineWorkReceiptValuesState = selector({
  key: 'offlineWorkReceiptValuesState',
  get: ({ get }) => {
    const response = offlineWorkReceiptAtom.map((item) => {
      const fields = {};
      const propertyNames = Object.keys(item);

      propertyNames.forEach((propertyName) => {
        fields[propertyName] = get(item[propertyName]);
      });

      return fields;
    });

    return response;
  },
  set: ({ set, reset }, newValues) => {
    const { index, ...rest } = newValues;

    if (newValues instanceof DefaultValue) {
      offlineWorkReceiptAtom.forEach((item) => {
        const propertyNames = Object.keys(item);

        propertyNames.forEach((propertyName) => {
          reset(item[propertyName]);
        });
      });

      return;
    }

    if (Array.isArray(newValues)) {
      newValues.forEach((item, index) => {
        Object.entries(item).forEach(([fieldName, newValue]) => {
          if (offlineWorkReceiptAtom[index]?.[fieldName]) {
            set(offlineWorkReceiptAtom[index][fieldName], newValue);
          }
        });
      });
    } else {
      const currentAtoms = offlineWorkReceiptAtom[index];
      Object.entries(rest).forEach(([fieldName, newValue]) => {
        if (currentAtoms?.[fieldName]) {
          set(currentAtoms[fieldName], newValue);
        }
      });
    }
  },
});

export default offlineWorkReceiptValuesState;
