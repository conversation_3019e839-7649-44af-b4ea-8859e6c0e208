import React from 'react';
import { cloneDeep } from 'lodash';
import DataIntakeFormPage from '@components/DataIntakeForm/components/DataIntakeFormPage';
import { capWorkReceiptMap, capWorkReceiptFields } from './FormSchema/fieldsMap';
import { capWorkReceiptTabs as intakeTabs } from '../consts';
import { VentingCalculations } from '../VentingCalculations/VentingCalculations';
import { HvacProducts } from './HvacProducts/HvacProducts';

const areasToCheck = ['Attic Floor', 'Attic Slope', 'Attic Wall'];

// Tabs that should be removed when the form is reset or during initial mount.
// These tabs are only relevant after deals are fully loaded with some diff conditions and should not be visible by default.
const tabsToRemoveOnReset = [
  'propertyOwner',
  'incomeEligibleProjectInfo',
  'ventingCalculation',
  'basInformation',
  'aif',
  'resultingHvac',
  'products',
  'resultingAmp',
  'postHea',
];

const containsAnyArea = (areas, areasToCheck) => {
  return areasToCheck.some((area) => areas?.includes(area));
};

const tabsPlacementMap = {
  customerAuditorInfo: 0,
  propertyOwner: 1,
  incomeEligibleProjectInfo: 2,
  hvacInfo: 3,
  roadBlocksInfo: 4,
  ventingCalculation: 5,
  basInformation: 6,
  aif: 7,
  ampIsm: 8,
  postHea: 9,
  resultingAmp: 10,
  resultingHvac: 11,
  products: 13,
  readOnlyFields: 12,
};

export const useTabsAndFieldsForCapWR = (
  handleFieldChange,
  {
    activeTab,
    isDealLoaded,
    docRepoStatus,
    interestedInHvac,
    areasOnWorkscope,
    activeForm,
    hvacInstallOpportunityId,
  },
) => {
  let tabs = intakeTabs;

  const rebuildTabs = (tabs) => {
    const orderedTabs = Object.keys(tabsPlacementMap)
      .map((key) => {
        const tab = tabs?.find((tab) => tab?.name === key);
        return tab || null;
      })
      .filter((tab) => tab !== null);
    return orderedTabs;
  };

  const addTab = (tabName, tabTitle) => {
    const tabNotExist = !tabs.some((tab) => tab.name === tabName);
    if (tabNotExist) {
      tabs.push({
        name: tabName,
        title: tabTitle,
      });
    }
    return tabs;
  };

  const removeTab = (tabName) => {
    tabs = tabs.filter((tab) => tab.name !== tabName);
    return tabs;
  };

  const handleAppendTabs = (tabs) => {
    if (docRepoStatus > 0) {
      addTab('postHea', 'POST HEA');
      addTab('resultingAmp', 'POST HEA: AMP');
    }

    if (['Interested', 'Very Interested', 'Quote Developed In Home'].includes(interestedInHvac)) {
      addTab('resultingHvac', 'POST HEA: HVAC');
      addTab('products', 'Products');
    }

    if (containsAnyArea(areasOnWorkscope, areasToCheck) && activeForm === 0) {
      addTab('aif', 'AIF ');
      addTab('ventingCalculation', 'Venting Calculation');
    }

    if (activeForm === 0) {
      addTab('incomeEligibleProjectInfo', 'Income Eligible Project Info');
      addTab('basInformation', 'BAS Information');
    }
    return tabs;
  };

  const handleRemoveTabs = () => {
    if (!containsAnyArea(areasOnWorkscope, areasToCheck) && activeForm === 0) {
      removeTab('aif');
      removeTab('ventingCalculation');
    }

    return tabs;
  };

  const notAllowedPerUnitTabs = (tabs) => {
    let updatedTabs = tabs;
    if (activeForm > 0) {
      updatedTabs = tabs.filter(
        (tab) =>
          ![
            'ventingCalculation',
            'propertyOwner',
            'basInformation',
            'incomeEligibleProjectInfo',
            'aif',
          ].includes(tab.name),
      );
    }
    return updatedTabs;
  };

  if (isDealLoaded) {
    tabs = handleAppendTabs(tabs);
    tabs = handleRemoveTabs(tabs);
    tabs = notAllowedPerUnitTabs(tabs);
    tabs = rebuildTabs(tabs);
  }

  if (!isDealLoaded) {
    tabs = tabs.filter((tab) => !tabsToRemoveOnReset.includes(tab.name));
  }

  const capWorkReceiptTabs =
    tabs?.map(({ name, title }) => {
      const map = cloneDeep(capWorkReceiptMap[name]);

      if (name === 'ventingCalculation') {
        return {
          name,
          title,
          component: <VentingCalculations handleFieldChange={handleFieldChange} />,
        };
      }
      if (
        name === 'products' &&
        ['Interested', 'Very Interested', 'Quote Developed In Home'].includes(interestedInHvac)
      ) {
        return {
          name,
          title,
          component: <HvacProducts hvacInstallOpportunityId={hvacInstallOpportunityId} />,
        };
      }
      return {
        name,
        title,
        component: (
          <DataIntakeFormPage map={map} readOnlyForReview={activeTab === 'readOnlyFields'} />
        ),
      };
    }) ?? [];

  return {
    capWorkReceiptTabs,
    capWorkReceiptMap,
    capWorkReceiptFields,
  };
};
