import { selector } from 'recoil';

import {
  showSidebar<PERSON>tom,
  isSlotsSearch<PERSON>tom,
  selectedEventState,
  availableSlotsAtom,
  isLegendViewAtom,
  isSchedulingViewAtom,
  persistSelectedEventState,
  selectedPartnerEventState,
} from '@recoil/eventSidebar';
import { capHeaResultingQuestionsState, documentsSelector } from '@recoil/docRepo';
import { activeTabIndexAtom } from '@recoil/app';

import { paymentLinkSelector, customerInfoSelector } from '@recoil/payment';

import { isEventsSearchState } from '@recoil/eventsSearch';
import { selectedPatternState } from '@recoil/eventPatterns';

const showSidebarState = selector({
  key: 'showSidebarState',
  get: ({ get }) => {
    return get(showSidebarAtom);
  },
  set: ({ get, set, reset }, newValue) => {
    const persistSelectedEvent = get(persistSelectedEventState);

    // On open, new value is 'true'
    if (newValue) set(showSidebar<PERSON>tom, true);
    // On close, new value is 'false'
    else {
      reset(showSidebar<PERSON>tom);
      reset(isSlotsSearchAtom);
      reset(isLegendViewAtom);
      reset(isSchedulingViewAtom);
      reset(isEventsSearchState);
      reset(availableSlotsAtom);
      reset(selectedPatternState);
      reset(selectedPartnerEventState);
      reset(paymentLinkSelector);
      reset(customerInfoSelector);
      reset(capHeaResultingQuestionsState);
      reset(documentsSelector);
      // TODO: Reset Dynamically all instance of atomFamily
      reset(activeTabIndexAtom(['tabs']));
      reset(activeTabIndexAtom(['department']));
      if (!persistSelectedEvent) reset(selectedEventState);
    }
  },
});

export default showSidebarState;
