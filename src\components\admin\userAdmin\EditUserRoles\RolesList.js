// External Dependencies
import React from 'react';
import PropTypes from 'prop-types';
import { useSetRecoilState } from 'recoil';

// Internal Dependencies
import { selectedUserRoleState } from '@recoil/admin/users';

// Reusable Components
import { ScrollableList } from '@components/global/ScreenPartitionView';

// Internal Components
import RolesListCard from './RolesListCard';

// Main Component
const RolesList = ({ userRoles }) => {
  // Recoil State
  const setSelectedRole = useSetRecoilState(selectedUserRoleState);

  const handleAddRoleClick = () => {
    setSelectedRole({ department: '', role: '' });
  };

  return (
    <ScrollableList handleAddItemClick={handleAddRoleClick} listItemName="Role" allowSearch={false}>
      {userRoles.map((role) => {
        return <RolesListCard key={`${role.departmentId}-${role.state}`} roleObject={role} />;
      })}
    </ScrollableList>
  );
};

// Prop Types
RolesList.propTypes = {
  userRoles: PropTypes.arrayOf(PropTypes.shape({ roleId: PropTypes.number })).isRequired,
};

export default RolesList;
