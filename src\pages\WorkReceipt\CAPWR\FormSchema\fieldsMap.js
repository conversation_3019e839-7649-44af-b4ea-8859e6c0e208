import { addKeyNameToObject } from '@components/DataIntakeForm/dataIntakeFormHelpers';
import { sectionFields } from '@pages/WorkReceipt/FormSchema/sections';
import { flatCapWRFormSchema, capWRFormSchema } from './formSchema';

const {
  customerAuditorInfoForCapWr,
  incomeEligibleProjectInfo,
  capRoadBlocksInfo,
  hvacInfoCapWr,
  aif,
  ampIsm,
  resultingAmp,
  resultingHvac,
  capPostHea,
  propertyOwner,
  basInformationForCapWr,
  readOnlyFields,
} = capWRFormSchema;

const getFields = (schema) => Object.values(schema);

const capWorkReceiptMap = {
  customerAuditorInfo: {
    fields: getFields(customerAuditorInfoForCapWr),
  },
  incomeEligibleProjectInfo: {
    fields: getFields(incomeEligibleProjectInfo),
  },
  hvacInfo: {
    fields: getFields(hvacInfoCapWr),
  },
  roadBlocksInfo: {
    fields: [
      ...getFields(capRoadBlocksInfo),
      sectionFields.asbestosInfo,
      sectionFields.cstFailure,
      sectionFields.electrical,
      sectionFields.moisture,
      sectionFields.crawlSpace,
      sectionFields.mold,
      sectionFields.other,
      sectionFields.bathFanInstallReplace,
      sectionFields.structural,
      sectionFields.pestInfestationRoofLeak,
      sectionFields.wallsWorkDisclosures,
    ],
  },
  basInformation: {
    fields: getFields(basInformationForCapWr),
  },
  ampIsm: {
    fields: getFields(ampIsm),
  },
  aif: {
    fields: getFields(aif),
  },
  postHea: {
    fields: [...getFields(capPostHea), sectionFields.icw],
  },
  resultingAmp: {
    fields: [...getFields(resultingAmp), sectionFields.ampFail],
  },
  resultingHvac: {
    fields: getFields(resultingHvac),
  },
  propertyOwner: {
    fields: getFields(propertyOwner),
  },
  readOnlyFields: {
    fields: [...getFields(readOnlyFields), sectionFields.heaSpecReadOnly],
  },
};

const capWorkReceiptFields = addKeyNameToObject(flatCapWRFormSchema);

export { capWorkReceiptFields, capWorkReceiptMap };
