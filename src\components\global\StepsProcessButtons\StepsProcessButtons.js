import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Clickable } from '@components/global';
import styled from 'styled-components';

const StepProcessButtons = styled.div`
  margin: 30px 30px 30px 10px;
  button {
    font-size: 14px;
    border-radius: 35px;
  }
  .disabled {
    background-color: ${({ theme }) => theme.secondary[400]};
    color: ${({ theme }) => theme.secondary[100]};
    padding: 5px 40px;
    font-size: 16px;
    border: none;
  }
`;

const StepsProcessButtons = (props) => {
  const { activeTabIndex, setModalDisplay, handleSubmit, tabsData, enableSubmit = false } = props;
  const totalTabs = tabsData.length;
  const [isPrevButtonEnable, setIsPrevButtonEnable] = useState(false);
  const [isNextButtonEnable, setIsNextButtonEnable] = useState(true);

  // For displaying the current component for the tab clicked
  useEffect(() => {
    if (activeTabIndex > 0 && activeTabIndex <= totalTabs) {
      setIsPrevButtonEnable(true);
    } else setIsPrevButtonEnable(false);
    if (activeTabIndex >= 0 && activeTabIndex < totalTabs - 1) setIsNextButtonEnable(true);
    else setIsNextButtonEnable(false);
  }, [activeTabIndex, totalTabs]);

  return (
    <StepProcessButtons className="step-process-buttons">
      {isPrevButtonEnable && (
        <button
          type="button"
          className="cancel-button  mr-5"
          onClick={() => setModalDisplay(activeTabIndex - 1)}
        >
          <Clickable>Previous</Clickable>
        </button>
      )}
      {isNextButtonEnable && (
        <button
          type="button"
          className="save-button button-display"
          onClick={() => setModalDisplay(activeTabIndex + 1)}
        >
          <Clickable>Next</Clickable>
        </button>
      )}
      {!isNextButtonEnable && (
        <button
          type="button"
          className={!enableSubmit ? 'disabled mr-5' : 'save-button mr-5'}
          disabled={!enableSubmit}
          onClick={async () => {
            await handleSubmit(tabsData);
          }}
        >
          Submit
        </button>
      )}
    </StepProcessButtons>
  );
};

StepsProcessButtons.propTypes = {
  activeTabIndex: PropTypes.number.isRequired,
  tabsData: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  setModalDisplay: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  enableSubmit: PropTypes.bool,
};

export default StepsProcessButtons;
