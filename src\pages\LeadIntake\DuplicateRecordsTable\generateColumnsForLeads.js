const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

export const generateRowAndColumn = (records = [], columnsFor, sfType) => {
  const leadKeyMappings = [
    { columnName: '', oldKey: '' },
    { columnName: 'First Name', oldKey: 'FirstName' },
    { columnName: 'Last Name', oldKey: 'LastName' },
    { columnName: 'Street', oldKey: 'Street' },
    { columnName: 'City', oldKey: 'City' },
    { columnName: 'Lead Source', oldKey: 'LeadSource' },
    { columnName: 'Email', old<PERSON>ey: 'Email__c' },
    { columnName: 'Cell Phone', oldKey: 'Cell_Phone__c' },
    { columnName: 'Created Date', oldKey: 'CreatedDate', isDate: true },
  ];

  const accountsKeyMappings = [
    { columnName: 'Name', old<PERSON><PERSON>: 'Name' },
    { columnName: 'Email', old<PERSON>ey: 'Email__c' },
    { columnName: 'Deal ID', old<PERSON>ey: 'Deal__c' },
    { columnName: 'Address', oldKey: 'Address__c' },
    { columnName: 'Cell Phone', oldKey: 'Day_Phone__c' },
  ];

  const leadsTwoPoint0KeyMappings = [
    { columnName: '', oldKey: '' },
    { columnName: 'Email', oldKey: 'Email' },
    { columnName: 'FirstName', oldKey: 'FirstName' },
    { columnName: 'LastName', oldKey: 'LastName' },
    { columnName: 'Street', oldKey: 'street' },
    { columnName: 'City', oldKey: 'city' },
    { columnName: 'Phone', oldKey: 'Phone' },
  ];

  const accountsTwoPoint0KeyMappings = [
    { columnName: 'Name', oldKey: 'Name' },
    { columnName: 'Street', oldKey: 'street' },
    { columnName: 'City', oldKey: 'city' },
    { columnName: 'Phone', oldKey: 'Phone' },
    { columnName: 'Email', oldKey: 'Email__c' },
  ];

  const objectMappings = {
    'DUPLICATE_LEADS1.0': leadKeyMappings,
    'DUPLICATE_ACCOUNTS1.0': accountsKeyMappings,
    'DUPLICATE_LEADS2.0': leadsTwoPoint0KeyMappings,
    'DUPLICATE_ACCOUNTS2.0': accountsTwoPoint0KeyMappings,
  };

  const keyMappings = objectMappings[`${columnsFor}${sfType}`];
  const transformedRecords = records?.map((record) => {
    const transformedRecord = {};
    transformedRecord.id = record.Id;

    const { matchedOn } = record;

    keyMappings.forEach((mapping) => {
      const { columnName, oldKey } = mapping;
      const newKey = columnName;
      if (mapping.isDate) {
        transformedRecord[newKey] = formatDate(record[oldKey]);
      } else {
        transformedRecord[newKey] = record[oldKey];
      }
    });

    transformedRecord.matchedOn = matchedOn;
    return transformedRecord;
  });
  return { rows: transformedRecords, columns: keyMappings };
};
