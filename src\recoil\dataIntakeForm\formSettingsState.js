import { atom, selector } from 'recoil';
import { formValuesState, formFieldsState } from '@recoil/dataIntakeForm';
import { getDataIntakeFormValues } from '@components/DataIntakeForm/dataIntakeFormHelpers';

const formSettingsAtom = atom({
  key: 'formSettingsStateAtom',
  default: { numUnit: 1 },
});

const formSettingsState = selector({
  key: 'formSettingsStateSelector',
  get: ({ get }) => {
    return get(formSettingsAtom);
  },
  set: ({ get, set }, newValue) => {
    const { numUnit } = newValue;

    if (numUnit) {
      const formValues = get(formValuesState);
      const formFields = get(formFieldsState);
      const newValues = getDataIntakeFormValues(formFields, formValues, numUnit);

      set(formValuesState, newValues);
    }

    set(formSettingsAtom, newValue);
  },
});

export default formSettingsState;
