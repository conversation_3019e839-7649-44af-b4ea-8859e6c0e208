import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const TableContainer = styled.div`
  overflow: auto;
`;

const StyledTable = styled.table`
  width: 100%;
`;

const TableHeaderContainer = styled.th`
  color: ${({ theme }) => theme.secondary[500]};
  padding: 8px;
`;

const StyledTableData = styled.td`
  padding: 8px;
`;

const StyledTableBody = styled.tbody`
  flex: 1 1 auto;
  overflow: auto;
`;

const StyledTableRow = styled.tr`
  :nth-child(even) {
    background-color: #dddddd;
  }
  ${({ $halted, theme }) => ($halted ? `background-color: ${theme.colors.red} !important;` : '')}
`;

const Table = ({ header, rows, editable = false, onRowClick = () => {} }) => {
  const renderTableHeader = header.map((columnName) => {
    return <TableHeaderContainer key={columnName}>{columnName.toUpperCase()}</TableHeaderContainer>;
  });

  const renderTableData = rows.map(({ id, halted, tableData }) => {
    return (
      <StyledTableRow key={id} $halted={halted} onClick={editable ? () => onRowClick(id) : null}>
        {tableData.map((dataValue, i) => {
          return (
            <StyledTableData key={`${id}${dataValue}${header[i]}`}>{dataValue}</StyledTableData>
          );
        })}
      </StyledTableRow>
    );
  });

  return (
    <TableContainer>
      <StyledTable>
        <StyledTableRow>{renderTableHeader}</StyledTableRow>
        <StyledTableBody>{renderTableData}</StyledTableBody>
      </StyledTable>
    </TableContainer>
  );
};

Table.propTypes = {
  header: PropTypes.arrayOf(PropTypes.string).isRequired,
  rows: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.shape({})])).isRequired,
  editable: PropTypes.bool,
  onRowClick: PropTypes.func,
};

export default Table;
