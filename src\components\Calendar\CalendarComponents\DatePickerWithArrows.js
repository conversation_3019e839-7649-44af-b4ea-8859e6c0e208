import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';
import DatePicker from 'react-datepicker';

const StyledDatePicker = styled.div`
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
  background-color: white;
`;

const CalendarIcon = styled.button``;
const Arrow = styled.button``;

const DatePickerWithArrows = (props) => {
  const { currentDate, handleDateChange } = props;
  const currentDateMoment = moment(currentDate);

  return (
    <StyledDatePicker key={-1}>
      <Arrow onClick={() => handleDateChange(currentDateMoment.subtract(7, 'days'))}>
        <span className="glyphicon glyphicon-fast-backward" />
      </Arrow>
      <DatePicker
        customInput={
          // eslint-disable-next-line
          <div>
            <CalendarIcon>
              <span className="glyphicon glyphicon-calendar" />
            </CalendarIcon>
          </div>
        }
        selected={currentDateMoment.toDate()}
        onChange={handleDateChange}
        dateFormat="M/dd/yy"
        placeholderText="Select a start date."
      />
      <Arrow onClick={() => handleDateChange(currentDateMoment.add(7, 'days'))}>
        <span className="glyphicon glyphicon-fast-forward" />
      </Arrow>
    </StyledDatePicker>
  );
};

DatePickerWithArrows.propTypes = {
  currentDate: PropTypes.instanceOf(moment).isRequired,
  handleDateChange: PropTypes.func.isRequired,
};

export default DatePickerWithArrows;
