import React, { useEffect } from 'react';
import styled from 'styled-components';
import { selectedEventState } from '@recoil/eventSidebar';
import { activeTabState, formValuesState, resetFormRecoilState } from '@recoil/dataIntakeForm';
import { activeTabIndexAtom } from '@recoil/app';
import { useRecoilState, useResetRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import moment from 'moment';
import { getFormattedArrivalWindow } from '@utils/functions';
import { PrimaryButton } from '@components/global/Buttons';
import Cookies from 'js-cookie';
import { useFieldsAndTabsForIntake } from './utils/getters/useTabsAndFieldsForIntake';
import { isAuthorized } from '../../utils/AuthUtils';

const WrapUpContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: calc(100% - 48px);
  padding: 24px;
  border-radius: 8px;
  width: 742px;
`;
const Title = styled.div`
  color: #333333;
  font-size: 22px;
  margin-bottom: 16px;
`;
const PrimaryButtonContainer = styled.div`
  width: 40px;
`;
const DescriptionContainer = styled.ul``;
const Description = styled.li`
  list-style-type: circle;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  text-transform: none;
  color: #201d1d;
  margin-bottom: 16px;
  font-style: ${({ isItalic }) => (isItalic ? 'italic' : 'normal')};
`;
const QRCodeContainer = styled.img`
  height: 200px;
  width: 200px;
`;

export const WrapUp = () => {
  const event = useRecoilValue(selectedEventState);
  const setActiveTab = useSetRecoilState(activeTabState);
  const setActiveTabIndex = useSetRecoilState(activeTabIndexAtom(['tabs']));
  const [formValues, setFormValues] = useRecoilState(formValuesState);
  const resetFormState = useResetRecoilState(resetFormRecoilState);
  const { heaOrHvac } = formValues;
  const start = event?.startEndTimes[0]?.start;
  const end = event?.startEndTimes[0]?.end;
  const timeAndDate = moment(start).format('dddd, MMMM D, YYYY ') || '';
  const durationStart = new Date(start);
  const durationEnd = new Date(end);
  const duration =
    Math.abs(new Date(durationEnd.getTime()) - new Date(durationStart.getTime())) / 3600000;
  const { leadIntakeTabs } = useFieldsAndTabsForIntake();
  const isCT = event.type.slice(0, 4) === '0100';
  const isHEA = heaOrHvac === 'HEA';
  const isHVACSales = heaOrHvac === 'HVAC';
  const isBA = isAuthorized('Agent', 'Marketing', true);
  const isCIA = isAuthorized('Agent', 'CIA');

  const getCampaignFromCookies = () => {
    const isBrandAmbassador =
      Cookies.get('campaign-id') &&
      Cookies.get('campaign') &&
      Cookies.get('campaign') &&
      isAuthorized('Agent', 'Marketing', true);
    if (isBrandAmbassador) {
      setFormValues({
        isCampaignIdValid: true,
        campaignId: Cookies.get('campaign-id'),
        campaign: Cookies.get('campaign'),
        atAnEvent: 'Yes',
        leadSource: 'Field Marketing',
      });
    }
  };

  const resetLeadIntake = () => {
    setActiveTab(leadIntakeTabs[0]?.name);
    setActiveTabIndex(0);
    resetFormState();
    getCampaignFromCookies();
  };

  useEffect(() => {
    return () => {
      resetLeadIntake();
    };
  }, []);

  const wrapUpMessage = () => {
    switch (true) {
      case isCT:
        return (
          <>
            <Description>Please note the following:</Description>
            <DescriptionContainer>
              {event.type === '010006' && (
                <Description>
                  The appointment will take between 3 and 4 hours depending on the age and size of
                  the home.
                </Description>
              )}
              {event.type === '010000' && (
                <Description>The appointment will take between 2 and 3 hours.</Description>
              )}
              <Description>Someone over the age of 18 needs to be present </Description>
              <Description>
                There can be no open construction or remodeling going on in the home Please refrain
                from using any fireplace/wood stove/pellet stove and sweep away any ash prior to our
                arrival
              </Description>
              <Description>
                Our
                {['010000', '010001'].includes(event.type) ? ' Home Energy Specialist ' : ' crew '}
                will need access to the attic and basement while in your home
              </Description>
              <Description>
                Also, please note that if any safety barriers are found during the assessment, such
                as mold or asbestos, we will not be able to perform the{' '}
                {event.type === '010006'
                  ? 'blower door test and associated air sealing'
                  : 'diagnostic blower door test'}
                .
              </Description>
            </DescriptionContainer>
          </>
        );
      case isBA && isHEA:
        return (
          <>
            <Description>Pull out your appointment card</Description>
            <DescriptionContainer>
              <Description>Quiz the customer to be sure they are locked in</Description>
              <Description isItalic>
                “Perfect, so I’ve got you down for....what day again? Right, and what time?”
              </Description>
              <Description isItalic>
                “So here is an appointment card to remind you of the day and time of your
                appointment. Do you need to add that to your calendar right now?”
              </Description>
              <Description isItalic>
                “Great! We also have a QR code for you to scan to add our contact info to your
                phone. Since this is a state program someone may need to reach out to you if there
                is any question about them approving you and I wouldn&apos;t want you to think it is
                a spam call.”
              </Description>
              <Description isItalic>
                “OK you are all set! Our home energy specialist will be at your house on{' '}
                {timeAndDate}, with an arrival window of{' '}
                {getFormattedArrivalWindow({
                  dateTimeMoment: moment(start),
                  isHVACSales,
                })}
                . The audit will last roughly for {duration} hours. It was great talking to you!”
              </Description>
            </DescriptionContainer>
            <QRCodeContainer
              src="https://hwe-static-assets.s3.amazonaws.com/images/qrCodeHwe.png"
              alt="QRCODE"
            />
          </>
        );
      case isCIA && isHEA:
        return (
          <>
            <Description>
              Thank you for scheduling your home energy audit with HomeWorks Energy!
            </Description>
            <Description>
              Our home energy specialist will be at your house on {timeAndDate}, with an arrival
              window of{' '}
              {getFormattedArrivalWindow({
                dateTimeMoment: moment(start),
                isHVACSales,
              })}
              , and the audit will last roughly for {duration} hours.
            </Description>
            <Description>
              Ensure you have a bill available for your utilities for your Auditor to review. This
              will help them understand your home&apos;s energy usage.
            </Description>
            <Description>
              Our assessor will need access to your attic, crawl spaces, and basement. This does not
              need to be totally clear but they do need some access.
            </Description>
            <Description>
              If you have any questions, please feel free to reach out to us at (781) 305-3319.
            </Description>
          </>
        );
      case isHVACSales:
        return (
          <>
            <Description>
              Thank you for scheduling your Heating and Cooling visit with HomeWorks Energy!
            </Description>
            <Description>
              Our home cooling specialist will be at your house on {timeAndDate}, with an arrival
              window of 1 - 1.5 hours, and the audit will last roughly for 4 hours.
            </Description>
            <Description>
              Our assessor will need access to your attic, crawl spaces, and basement. This does not
              need to be totally clear but they do need some access.
            </Description>
            <Description>
              If you have any questions, please feel free to reach out to us at (781) 305-3319.
            </Description>
          </>
        );
      default:
        return (
          <Description>
            None of the roles matched the criteria for displaying the script. Please contact the
            software team for assistance in resolving this issue.
          </Description>
        );
    }
  };

  return (
    <WrapUpContainer>
      <Title>Wrap-up</Title>
      {wrapUpMessage()}
      <PrimaryButtonContainer>
        <PrimaryButton onClick={resetLeadIntake}>Return to Start</PrimaryButton>
      </PrimaryButtonContainer>
    </WrapUpContainer>
  );
};
export default WrapUp;
