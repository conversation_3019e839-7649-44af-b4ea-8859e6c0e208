import { isHWEUser } from '@homeworksenergy/utility-service';
import validateRequiredParams from '@utils/validateRequiredParams';
import { verifySingleWeek } from '@utils/functions';
import { throwError } from '@utils/EventEmitter';

const create = (params) => {
  const {
    sfIds,
    sfIds: { operationsId },
    address,
    oids,
    phoneNumber,
    type,
    date,
    leadVendor,
    siteId,
    jobLength,
    jobStatus,
    confirmationStatus,
    amount,
    attributes,
    notes,
    startEndTimes,
    eventName,
    finalContractAmountAtApproval,
    revisedContractAmount,
  } = params;

  const isTruckService = type === '000503';
  const requiredFields = isTruckService
    ? {
        Truck: oids,
        Location: address,
        'Job Type': type,
        Date: date,
        'Service Summary': eventName,
      }
    : {
        'Operations ID': operationsId,
        'Truck(s)': oids,
        'Lead Vendor': leadVendor,
        Amount: amount,
        Location: address,
        'Job Type': type,
        'Phone Number': phoneNumber,
        Date: date,
        'Requirement(s)': attributes,
      };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const createData = isTruckService
    ? { date, oids, type, eventName, notes, startEndTimes, address }
    : {
        date,
        oids,
        type,
        jobLength,
        attributes,
        sfIds,
        notes,
        siteId,
        amount,
        confirmationStatus,
        leadVendor,
        jobStatus,
        phoneNumber,
        startEndTimes,
        finalContractAmountAtApproval,
        revisedContractAmount,
      };

  // Create slots using the start and end times
  return createData;
};

const update = (params) => {
  const {
    address,
    date,
    associatedEventIds,
    associatedEventsId,
    oids,
    sfIds,
    type,
    leadVendor,
    siteId,
    jobLength,
    jobStatus,
    confirmationStatus,
    amount,
    attributes,
    notes,
    startEndTimes,
    wxVisitResult,
    eventName,
    finalContractAmountAtApproval,
    revisedContractAmount,
  } = params;

  const isTruckService = type === '000503';
  const requiredFields = isTruckService
    ? {
        'Truck(s)': oids,
      }
    : {
        'Truck(s)': oids,
        'Requirement(s)': attributes,
      };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const updateData = {
    date,
    oids,
    type,
    sfIds,
    confirmationStatus,
    attributes,
    notes,
    leadVendor,
    siteId,
    jobLength,
    jobStatus,
    amount,
    associatedEventIds,
    associatedEventsId,
    startEndTimes,
    wxVisitResult,
    eventName,
    address,
    finalContractAmountAtApproval,
    revisedContractAmount,
  };

  return updateData;
};

// TODO: change to return only one slot
const getSlots = (params) => {
  const {
    sfIds: { operationsId },
    type,
    jobLength,
    attributes,
  } = params;

  const requiredFields = {
    'Operations ID': operationsId,
    'Job Type': type,
    'Contract Amount': () => jobLength,
    'Requirement(s)': attributes,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  // TODO: multiple day events
  return [params];
};

const cancel = () => true;

const reassign = () => true;

const reschedule = (params) => {
  const { date, jobLength } = params;
  if (!verifySingleWeek(date, jobLength))
    return throwError('Events must start and end on the same week');

  return params;
};

const createAgent = (params) => {
  const { firstname, lastname, displayName, department, company, region, state } = params;
  const hweUser = isHWEUser({ company });
  const isPartner = !hweUser;
  const requiredFields = {
    'First name': firstname,
    'Last name': lastname,
    'Display Name': displayName,
    Department: department,
    Company: company,
    State: state,
  };
  if (!isPartner) requiredFields.Region = region;
  if (department === 3) {
    if (params?.sendNotification) requiredFields['Notification Channel'] = params?.sendNotification;
  }
  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  return params;
};

const insulationInstall = { create, update, getSlots, cancel, reassign, reschedule, createAgent };
export default insulationInstall;
