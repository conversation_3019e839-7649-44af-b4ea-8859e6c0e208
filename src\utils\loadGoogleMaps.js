let bootstrapLoader = null;
let isLoading = false;

// wait for Google Maps to load
const waitForGoogleMaps = () => {
  return new Promise((resolve) => {
    const checkLoaded = () => {
      if (window.google && window.google.maps && window.google.maps.importLibrary) {
        resolve();
      } else {
        setTimeout(checkLoaded, 100);
      }
    };
    checkLoaded();
  });
};

// load the Google Maps script
const loadGoogleMapsScript = (apiKey) => {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    const params = new URLSearchParams();
    params.set('key', apiKey);
    params.set('libraries', 'places,marker');
    script.src = `https://maps.googleapis.com/maps/api/js?${params.toString()}`;
    script.async = true;
    script.onerror = () => {
      isLoading = false;
      reject(new Error('Failed to load Google Maps API'));
    };
    script.onload = () => {
      isLoading = false;
      resolve();
    };
    document.head.appendChild(script);
  });
};

const initializeBootstrapLoader = async (apiKey) => {
  if (bootstrapLoader) return bootstrapLoader;

  bootstrapLoader = (async () => {
    // Check if already loaded
    if (window.google && window.google.maps && window.google.maps.importLibrary) {
      return;
    }

    // If loading is in progress, wait for it
    if (isLoading) {
      await waitForGoogleMaps();
      return;
    }

    isLoading = true;

    try {
      await loadGoogleMapsScript(apiKey);
    } catch (error) {
      isLoading = false;
      bootstrapLoader = null;
      throw error;
    }
  })();

  return bootstrapLoader;
};

const loadGoogleMaps = async (apiKey) => {
  if (!apiKey) {
    throw new Error('Google Maps API key not found. Please check your backend configuration.');
  }
  await initializeBootstrapLoader(apiKey);
  return window.google.maps;
};

export const importMapLibrary = async (library) => {
  if (!window.google || !window.google.maps || !window.google.maps.importLibrary) {
    throw new Error('Google Maps not initialized. Call loadGoogleMaps() first.');
  }
  return window.google.maps.importLibrary(library);
};

export default loadGoogleMaps;
