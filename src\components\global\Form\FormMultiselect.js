import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { Multiselect } from 'react-widgets';

import FormFieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';
import RecoilFieldOptions from './RecoilFieldOptions';

import 'react-widgets/styles.css';

// TODO: Build in disabled/readOnly styles to match the rest of the form components

const FormMultiselectContent = styled(Multiselect)`
  .rw-widget-input {
    box-shadow: unset;
  }
  .rw-widget-input.rw-widget-picker.rw-widget-container {
    min-height: 32px;
    height: auto;
  }
  .rw-multiselect-taglist {
    min-height: 32px;
    height: auto;
  }
  .rw-btn.rw-picker-caret {
    min-height: 32px;
    height: auto;
  }
  .rw-multiselect.rw-widget {
    min-height: 32px;
    height: auto;
  }
  .rw-widget-picker {
    min-height: 32px;
    height: auto;
  }
  .rw-multiselect-input {
    height: unset;
  }
`;

const FormMultiselect = (props) => {
  const {
    title,
    required = false,
    options,
    name = '',
    value = [],
    onChange = () => {},
    recoilOptions,
    readOnly = false,
    compact = false,
    testid = '',
    weight = 500,
  } = props;

  if (recoilOptions) return <RecoilFieldOptions Component={FormMultiselect} {...props} />;
  return (
    <FormFieldContainer testid={testid} required={required} fieldName={name} compact={compact}>
      <FormFieldLabel weight={weight}>{title}</FormFieldLabel>
      <FormMultiselectContent
        disabled={readOnly}
        data={options}
        filter="contains"
        textField={(field) => {
          return field?.key || field;
        }}
        defaultValue={value}
        value={
          value
            ? value?.map(
                (valueId) =>
                  options[options.findIndex((opt) => opt.value === valueId || opt === valueId)],
              )
            : []
        }
        onChange={(value) => onChange({ target: { name, value } })}
      />
    </FormFieldContainer>
  );
};

FormMultiselect.propTypes = {
  title: PropTypes.string.isRequired,
  required: PropTypes.bool,
  options: PropTypes.arrayOf(PropTypes.shape({})),
  name: PropTypes.string,
  onChange: PropTypes.func,
  value: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.shape({})),
    PropTypes.arrayOf(PropTypes.string),
    PropTypes.arrayOf(PropTypes.number),
  ]),
  recoilOptions: PropTypes.shape({}),
  compact: PropTypes.bool,
  testid: PropTypes.string,
  readOnly: PropTypes.bool,
  weight: PropTypes.number,
};

export default FormMultiselect;
