import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput, FormTextBox } from '@components/global/Form';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const HVACIcwReport = ({ record = {} }) => {
  const {
    dealId,
    opportunityId,
    createdDate,
    amount,
    capIcwReason,
    notesForOffice,
    siteid,
    isCap,
  } = record;

  return (
    <>
      {dealId && (
        <InteractiveRow>
          <InteractiveButtons dealId={dealId} title="Doc Repo" isCap={isCap} />
          <InteractiveButtons dealId={dealId} title="Work Receipt" />
        </InteractiveRow>
      )}
      <Row>
        <Col>
          {dealId && (
            <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          )}
          {opportunityId && (
            <FormInput
              readOnly
              name="opportunityId"
              value={opportunityId}
              title="Opportunity Id"
              placeholder=""
            />
          )}
          <FormInput readOnly name="siteid" value={siteid} title="Site ID" placeholder="" />
          <FormInput
            readOnly
            name="createdDate"
            value={createdDate}
            title="Created Date"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput readOnly name="amount" value={amount} title="Amount" placeholder="" />
          <FormInput
            readOnly
            name="capIcwReason"
            value={capIcwReason}
            title="CAP ICW Reason"
            placeholder=""
          />
        </Col>
      </Row>
      <FormTextBox
        readOnly
        name="notesForOffice"
        value={notesForOffice}
        title="Notes for Office"
        placeholder=""
      />
    </>
  );
};

HVACIcwReport.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    opportunityId: PropTypes.string,
    createdDate: PropTypes.string,
    amount: PropTypes.string,
    capIcwReason: PropTypes.string,
    notesForOffice: PropTypes.string,
    siteid: PropTypes.string,
    isCap: PropTypes.string,
  }),
};

export default HVACIcwReport;
