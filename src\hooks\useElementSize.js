/**
 * Not currently used, this should work if we need it.
 * Was going to be used for styling to calculate correct heights on the calendar page,
 * but we found a way to make it work with flexbox instead.
 *
 * Converted to JS from here:
 *
 * https://usehooks-ts.com/react-hook/use-element-size
 */

import { useCallback, useState, useLayoutEffect } from 'react';

const useElementSize = () => {
  // Mutable values like 'ref.current' aren't valid dependencies
  // because mutating them doesn't re-render the component.
  // Instead, we use a state as a ref to be reactive.
  const [ref, setRef] = useState(null);
  const [size, setSize] = useState({
    width: 0,
    height: 0,
  });

  const handleSize = useCallback(async (ref) => {
    setSize({
      width: ref?.offsetWidth || 0,
      height: ref?.offsetHeight || 0,
    });
  }, []);

  useLayoutEffect(() => {
    handleSize(ref);
  }, [handleSize, ref]);

  useLayoutEffect(() => {
    if (ref) resizeObserver.observe(ref);
    return resizeObserver.disconnect();
  }, [ref, resizeObserver]);

  const resizeObserver = new ResizeObserver((entries) => {
    const [element] = entries;
    if (!element) return null;

    if (element.borderBoxSize) {
      const { blockSize: height, inlineSize: width } = element.borderBoxSize[0];
      return setSize({ width, height });
    }

    const { width, height } = element.contentRect;
    return setSize({ width, height });
  });

  return [setRef, size];
};

export default useElementSize;
