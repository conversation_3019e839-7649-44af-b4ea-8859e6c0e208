import React, { useEffect, useState, useCallback } from 'react';
import { useSetRecoilState, useResetRecoilState, useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';

import { calendarTypeAtom } from '@recoil/app';
import { departmentZipcodesAtom } from '@recoil/utility';
import { EventsManager, UtilityManager } from '@utils/APIManager';
import { monthStartAtom } from '@recoil/calendar';
import {
  addEventChangedListener,
  reloadCalendar,
  addReloadCalendarListener,
} from '@utils/EventEmitter';
import { selectedEventState } from '@recoil/eventSidebar';

import CalendarTopContainer from '../CalendarComponents/CalendarTopContainer';
import CalendarControlHeader from '../CalendarComponents/CalendarControlHeader';
import CalendarHeader from '../CalendarComponents/CalendarHeader';
import GroupHeader from '../CalendarComponents/GroupHeader';
import CalendarRow from '../CalendarComponents/CalendarRow';

const StyledMonthlyCalendar = styled.div`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.secondary[100]};
  border-radius: 8px;
  overflow: auto;
`;

const MonthlyCalendar = (props) => {
  const {
    userInfo: { oid, regionName },
  } = props;

  const [dates, setDates] = useState({});
  const [monthStart, setMonthStart] = useState(moment().startOf('month'));
  const calendarType = useRecoilValue(calendarTypeAtom);
  const setDepartmentZipcodesAtom = useSetRecoilState(departmentZipcodesAtom);
  const resetSelectedEvent = useResetRecoilState(selectedEventState);
  const [calendarStart, setCalendarStart] = useState(
    moment()
      .startOf('month')
      .startOf('week'),
  );
  const [calendarEnd, setCalendarEnd] = useState(
    moment()
      .endOf('month')
      .endOf('week'),
  );
  const setMonthStartAtom = useSetRecoilState(monthStartAtom);

  const changeMonth = (newMonth = moment(), date = null) => {
    if (date) {
      handleDateChange(date);
    } else {
      if (moment.isMoment(newMonth)) return setMonthStart(moment(newMonth).startOf('month'));
      if (newMonth === '+') return setMonthStart(moment(monthStart).add(1, 'month'));
      if (newMonth === '-') return setMonthStart(moment(monthStart).subtract(1, 'month'));
    }
    return null;
  };

  useEffect(() => {
    setCalendarStart(moment(monthStart).startOf('week'));
    setCalendarEnd(
      moment(monthStart)
        .endOf('month')
        .endOf('week'),
    );
    setMonthStartAtom(moment(monthStart).startOf('month'));
  }, [monthStart, setMonthStartAtom]);

  useEffect(() => {
    const fetchHvacSalesZipCodes = async () => {
      const zipcodes = await UtilityManager.getZipCodesByDepartment(2);
      setDepartmentZipcodesAtom(zipcodes);
    };
    if (calendarType && calendarType === '000000') fetchHvacSalesZipCodes();
  }, [calendarType, setDepartmentZipcodesAtom]);

  const getEvents = useCallback(async () => {
    const rows = await EventsManager.loadEvents(calendarStart, calendarEnd, null, [oid]);
    if (!rows) return false;

    const [{ dates }] = rows;

    return setDates(dates);
  }, [calendarStart, calendarEnd, oid]);

  useEffect(() => {
    if (oid) getEvents();
  }, [getEvents, oid]);

  useEffect(() => {
    resetSelectedEvent();
    const removeReloadCalendarListener = addReloadCalendarListener(getEvents);
    return () => removeReloadCalendarListener();
  }, [getEvents, resetSelectedEvent]);

  useEffect(() => {
    const removeEventChangedListener = addEventChangedListener(() => reloadCalendar());
    return () => {
      removeEventChangedListener();
    };
  }, []);

  const renderMonth = () => {
    const rows = [];
    let weekStart = calendarStart;
    while (weekStart.isBefore(calendarEnd)) {
      rows.push(
        <CalendarRow
          key={weekStart.format('YYYY-MM-DD')}
          oid={oid}
          startDate={weekStart}
          dates={dates}
          isMonthly
        />,
      );
      weekStart = moment(weekStart).add(1, 'week');
    }
    return rows;
  };

  const handleDateChange = (date) => {
    setMonthStart(moment(date).startOf('month'));
  };

  if (!oid) return null;

  return (
    <StyledMonthlyCalendar>
      <CalendarTopContainer>
        <CalendarControlHeader
          scrollCalendar={changeMonth}
          startDate={monthStart}
          isMonthly
          showMonthYearPicker
        />
        <CalendarHeader startDate={calendarStart} isMonthly />
        <GroupHeader title={regionName} defaultTitle="No Region" />
      </CalendarTopContainer>
      {renderMonth()}
    </StyledMonthlyCalendar>
  );
};

MonthlyCalendar.propTypes = {
  userInfo: PropTypes.shape({
    oid: PropTypes.string,
    regionName: PropTypes.string,
  }).isRequired,
};

export default MonthlyCalendar;
