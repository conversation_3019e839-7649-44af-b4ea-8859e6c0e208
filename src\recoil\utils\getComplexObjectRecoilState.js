import { atom, selector, DefaultValue } from 'recoil';

const getComplexObjectRecoilState = (keyName, defaultValues) => {
  // Allow custom atoms/selectors to be applied directly
  // All others will be set to atoms with their default value from above
  const fieldStates = {};

  // Create a separate atom for each field on the selectedEvent.
  // This way, changing the customerName doesn't need to rerender the siteId
  // We can just update the customerName atom without effecting the siteId atom
  Object.keys(defaultValues).forEach((fieldName) => {
    fieldStates[fieldName] = atom({
      key: `${keyName}-${fieldName}Atom`,
      default: defaultValues[fieldName],
    });
  });

  /**
   * Handles converting startEndTimes from date to moment.
   * If there is any other calculated values that we need we can do this here.
   */
  const objectSelector = selector({
    key: keyName,
    get: ({ get }) => {
      const object = {};
      const propertyNames = Object.keys(fieldStates);

      // Get value of each atom, then return together in an object
      propertyNames.forEach((propertyName) => {
        object[propertyName] = get(fieldStates[propertyName]);
      });

      return object;
    },
    set: ({ set, reset }, newValue) => {
      if (typeof newValue === 'undefined') {
        console.error('Undefined value passed to getComplexObjectRecoilState');
        return;
      }

      // Handle Resetting selected event
      if (newValue instanceof DefaultValue) {
        const propertyNames = Object.keys(fieldStates);
        propertyNames.forEach((propertyName) => reset(fieldStates[propertyName]));
        return;
      }

      // New values we are trying to update.
      // Don't need to pass the { ...selectedEvent, [updateField]: changedValue }
      // Since we only update the properties that are present in the newValue object
      const propertyNames = Object.keys(newValue);
      propertyNames.forEach((propertyName) => {
        // We can only update atoms that we have created above. Each property needs a default value if it should be updated
        // Might also need one just to access the property.
        // Previously, when selecting an event on the calendar, it put the whole object from the backend into state.
        // This means that all properties on that object were automatically accounted for, and set to state even if they were undefined to start.
        // Now, we have no way of setting an unknown property, since there are individual atoms for each.
        // If necessary, we could potentially create a new atom and add it to the fieldStates here
        // But I think that it might be better just to enforce that all properties are created to start with
        if (!fieldStates[propertyName]) return;
        set(fieldStates[propertyName], newValue[propertyName]);
      });
    },
  });

  return [fieldStates, objectSelector];
};

export default getComplexObjectRecoilState;
