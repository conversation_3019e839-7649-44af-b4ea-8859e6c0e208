import React from 'react';
import styled, { useTheme } from 'styled-components';
import PropTypes from 'prop-types';

import { CalendarCheck } from '@styled-icons/bootstrap/CalendarCheck';
import { CalendarX } from '@styled-icons/bootstrap/CalendarX';
import { Calendar2Minus } from '@styled-icons/bootstrap/Calendar2Minus';
import { CalendarWeek } from '@styled-icons/bootstrap/CalendarWeek';
import { Calendar2Week } from '@styled-icons/bootstrap/Calendar2Week';
import { DollarCircle } from '@styled-icons/boxicons-regular/DollarCircle';

import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';

const LegendContainer = styled.div``;
const LegendHeader = styled.div`
  padding: 25px 25px 5px 15px;
  font-size: ${({ theme }) => theme.fontSizes.h2};
`;
const LegendUnitsContainer = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
`;
const LegendUnit = styled.div`
  width: 50%;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: left;
`;
const Dot = styled.span`
  height: 16px;
  width: 16px;
  background-color: ${({ color }) => color};
  border-radius: 50%;
  display: inline-block;
`;
const DisplayName = styled.div`
  padding-left: 6px;
`;

const CalendarWeekIcon = styled(CalendarWeek)`
  height: 32px;
  width: 32px;
  color: ${({ theme }) => theme.colors.eventC};
`;
const CalendarXIcon = styled(CalendarX)`
  height: 35px;
  width: 50px;
  margin-left: -7px;
  color: ${({ theme }) => theme.colors.eventJ};
`;
const CalendarCheckIcon = styled(CalendarCheck)`
  height: 32px;
  width: 32px;
  color: ${({ theme }) => theme.colors.actionButton};
`;
const CapIncSchIcon = styled(DollarCircle)`
  height: 32px;
  width: 32px;
  color: ${({ theme }) => theme.colors.customBlock};
`;
const CapIncNotCompIcon = styled(DollarCircle)`
  height: 35px;
  width: 50px;
  margin-left: -7px;
  color: ${({ theme }) => theme.colors.eventJ};
`;
const CapIncCompIcon = styled(DollarCircle)`
  height: 32px;
  width: 32px;
  color: ${({ theme }) => theme.colors.eventM};
`;
const Calendar2WeekIcon = styled(Calendar2Week)`
  height: 128px;
  width: 128px;
  color: ${({ theme }) => theme.colors.eventJ};
`;
const CalendarMinusIcon = styled(Calendar2Minus)`
  height: 64px;
  width: 64px;
  color: ${({ theme }) => theme.colors.actionButton};
`;

const Legend = (props) => {
  const theme = useTheme();
  const { state = 'MA', business: department } = props;

  // TODO: Move to database or utility-service repo
  // TODO: Add in IIC -- colors.lightYellow
  const legendEventsAndColors = {
    MA: {
      Insulation: [
        { name: 'Insulation Install', color: theme.colors.eventA },
        { name: 'Insulation Finish', color: theme.colors.eventF },
        { name: 'Callback, Internal Callback', color: theme.colors.eventC },
        { name: 'Repair & Service', color: theme.colors.eventB },
        { name: 'Manager Visit', color: theme.colors.eventB },
        { name: 'Truck Service', color: theme.colors.eventB },
        { name: 'Manager QC Visit', color: theme.colors.eventB },
        { name: 'Manager Permit Inspection', color: theme.colors.eventM },
        { name: 'Custom Block', color: theme.colors.customBlock },
        { name: 'Shadow Block', color: theme.colors.shadowBlock },
        { name: 'Job Complete, Reschedule', color: theme.colors.eventD },
        { name: 'Job Walk', color: theme.colors.red },
        { name: 'Green Energy Pros', color: theme.colors.eventL },
        { name: 'Home Depot', color: theme.colors.eventL },
        { name: 'Income Eligible', color: theme.colors.eventE },
      ],
      HEA: [
        { name: 'HEA', color: theme.colors.eventA },
        { name: 'Multi Family HEA', color: theme.colors.eventC },
        { name: 'Virtual HEA', color: theme.colors.eventB },
        { name: 'Virtual QC', color: theme.colors.eventF },
        { name: 'Return Visit', color: theme.colors.eventJ },
        { name: 'CAP Visit', color: theme.colors.eventN },
        { name: 'HVAC Spec Visit', color: theme.colors.eventK },
        { name: 'Custom Block', color: theme.colors.customBlock },
      ],
      'HVAC Sales': [
        { name: 'Sales Visit', color: theme.colors.eventA },
        { name: 'Lead Vetted', color: theme.colors.eventN },
        { name: 'Return Visit', color: theme.colors.eventG },
        { name: 'Lead Waiting', color: theme.colors.eventM },
        { name: 'Custom Block', color: theme.colors.customBlock },
        { name: 'Lead Not Vetted', color: theme.colors.eventJ },
        { name: 'Direct to HVAC', color: theme.colors.orange },
      ],
      'HVAC Install': [
        { name: 'Not Ordered', color: theme.colors.eventG },
        { name: 'Backordered', color: theme.colors.eventG },
        { name: 'Partially Delivered', color: theme.colors.eventG },
        { name: 'Order Confirmed', color: theme.colors.eventH },
        { name: 'Delivered', color: theme.colors.eventH },
        { name: 'Site Eval', color: theme.colors.eventA },
        { name: 'Final Site Eval', color: theme.colors.shadowBlock },
        { name: 'Custom Block', color: theme.colors.customBlock },
      ],
      Partners: [
        { name: 'Inspection Scheduled', icon: <CalendarWeekIcon /> },
        { name: 'CAP - Inspection Scheduled', icon: <CapIncSchIcon /> },
        { name: 'Inspection Completed', icon: <CalendarCheckIcon /> },
        { name: 'CAP - Inspection Completed', icon: <CapIncCompIcon /> },
        {
          name: 'Inspection Not Completed, Customer Not Responsive, Closed Lost',
          icon: <CalendarXIcon />,
        },
        {
          name: 'CAP - Inspection Not Completed, Customer Not Responsive, Closed Lost',
          icon: <CapIncNotCompIcon />,
        },
      ],
      'WX Partners': [
        {
          name:
            'Requires Action by Installer OR the scheduled end date is more than 5 days ago but status still not Installation and Documentation Complete',
          icon: <Calendar2WeekIcon />,
        },
        {
          name: 'It has been more than 7 days since last customer contact attempt',
          icon: <CalendarMinusIcon />,
        },
      ],
    },
    CT: {
      HEA: [
        { name: 'HEA', color: theme.colors.eventA },
        { name: 'Sealing Service Revisit', color: theme.colors.eventL },
        { name: 'Sealing Service Only', color: theme.colors.eventC },
        { name: 'HEA + Sealing Service', color: theme.colors.eventG },
        { name: 'Insulation Quote', color: theme.colors.eventB },
        { name: 'Custom Block', color: theme.colors.customBlock },
      ],
      Subcontractor: [
        { name: 'Window Visit', color: theme.colors.eventL },
        { name: 'HVAC Visit', color: theme.colors.eventJ },
      ],
    },
  };

  return (
    <LegendContainer>
      <LegendHeader> Legend Description </LegendHeader>
      <LegendUnitsContainer>
        {legendEventsAndColors[state][department] &&
          legendEventsAndColors[state][department].map((el) => {
            const { name, color, icon } = el;
            return (
              <LegendUnit key={name}>
                {color && <Dot color={color} />}
                {icon}
                <DisplayName>{name}</DisplayName>
              </LegendUnit>
            );
          })}
      </LegendUnitsContainer>
      <EventSidebarFooter />
    </LegendContainer>
  );
};

Legend.propTypes = {
  business: PropTypes.string.isRequired,
  state: PropTypes.string.isRequired,
};

export default Legend;
