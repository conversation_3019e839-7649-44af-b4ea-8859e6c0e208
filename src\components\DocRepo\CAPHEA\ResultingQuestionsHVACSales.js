import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import {
  handleFormFieldChange,
  Row,
  Col,
  FormRadioButtons,
  FormSelect,
} from '@components/global/Form';

import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';

import { PrimaryButton } from '@components/global/Buttons';

import { capHeaResultingQuestionsState } from '@recoil/docRepo';
import { DocRepoManager } from '@utils/APIManager';
import eventValidation from '@utils/eventValidation';
import {
  existingHVACOptions,
  existingDHWOptions,
  recommendedHVACOptions,
  recommendedDHWOptions,
} from '@utils/businessLogic/capHvacSalesDocRepoBusinessLogic';

const HorizontalDivider = styled.hr`
  margin-top: 1px;
  margin-bottom: 3px;
`;

const ResultingQuestionsHVACSales = ({ uniqueId, department, setShowResulting, getDocs }) => {
  const [resulting, setResulting] = useRecoilState(capHeaResultingQuestionsState);
  const {
    displaceGasHeatWithHeatPump,
    cstFailForGasSystem,
    replaceDelivFuelWithHeatPump,
    recommendedHvac,
    recommendedDhw,
    existingHvac,
    existingDhw,
  } = resulting;

  const yesNoOptions = [
    { key: 'Yes', value: 'yes' },
    { key: 'No', value: 'no' },
  ];

  const handleFieldChange = (e, updatedResulting = resulting) => {
    return handleFormFieldChange(e, updatedResulting, setResulting);
  };

  const handleSaveClick = async () => {
    const updateResulting = eventValidation.MA.HEA.capDocRepoResulting(resulting, department);
    if (updateResulting) {
      const response = await DocRepoManager.updateDocRepoResultingQuestions({
        state: 'MA',
        department,
        uniqueId,
        updateResulting,
      });
      if (response) {
        setResulting({ ...response.data, uniqueId });
        setShowResulting(false);
        getDocs();
      }
    }
  };

  return (
    <>
      <EventSidebarBody>
        <Row key="displaceGasHeatWithHeatPump">
          <FormRadioButtons
            name="displaceGasHeatWithHeatPump"
            required
            title="Are you Displacing Gas Heat with Heat Pumps ?"
            column={false}
            maxGap
            horizontal
            options={yesNoOptions}
            value={displaceGasHeatWithHeatPump}
            onChange={handleFieldChange}
          />
        </Row>
        <Row key="cstFailForGasSystem">
          <FormRadioButtons
            name="cstFailForGasSystem"
            required
            title="Are you addressing a CST fail for a gas system?"
            column={false}
            maxGap
            horizontal
            options={yesNoOptions}
            value={cstFailForGasSystem}
            onChange={handleFieldChange}
          />
        </Row>
        <Row key="replaceDelivFuelWithHeatPump">
          <FormRadioButtons
            name="replaceDelivFuelWithHeatPump"
            required
            title="Are you replacing deliverable fuel with Heat Pump?"
            column={false}
            maxGap
            horizontal
            options={yesNoOptions}
            value={replaceDelivFuelWithHeatPump}
            onChange={handleFieldChange}
          />
        </Row>
        <Row key="existingHvac">
          <Col>
            <FormSelect
              required
              title="Existing HVAC"
              placeholder="Select Existing HVAC"
              name="existingHvac"
              value={existingHvac}
              onChange={handleFieldChange}
              options={existingHVACOptions}
            />
          </Col>
        </Row>
        <Row key="recommendedHvac">
          <Col>
            <FormSelect
              required
              title="Recommended HVAC"
              placeholder="Select Recommended HVAC"
              name="recommendedHvac"
              value={recommendedHvac}
              onChange={handleFieldChange}
              options={recommendedHVACOptions}
            />
          </Col>
        </Row>
        <Row key="existingDhw">
          <Col>
            <FormSelect
              required
              title="Existing DHW"
              placeholder="Select Recommended DHW"
              name="existingDhw"
              value={existingDhw}
              onChange={handleFieldChange}
              options={existingDHWOptions}
            />
          </Col>
        </Row>
        <Row key="recommendedDhw">
          <Col>
            <FormSelect
              required
              title="Recommended DHW"
              placeholder="Select Recommended DHW"
              name="recommendedDhw"
              value={recommendedDhw}
              onChange={handleFieldChange}
              options={recommendedDHWOptions}
            />
          </Col>
        </Row>
        <HorizontalDivider />
      </EventSidebarBody>
      {displaceGasHeatWithHeatPump && (
        <EventSidebarFooter
          leftButtons={
            <PrimaryButton onClick={() => handleSaveClick()}>Save Resulting</PrimaryButton>
          }
        />
      )}
    </>
  );
};

ResultingQuestionsHVACSales.propTypes = {
  uniqueId: PropTypes.string.isRequired,
  department: PropTypes.string.isRequired,
  setShowResulting: PropTypes.func.isRequired,
  getDocs: PropTypes.func.isRequired,
};

export default ResultingQuestionsHVACSales;
