import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const HeaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: 20px;
  height: 100px;
`;

const Name = styled.div`
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  color: ${({ theme }) => theme.primary[500]};
`;

const BasicInfo = styled.div`
  display: flex;
  flex-direction: row;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  color: ${({ theme }) => theme.secondary[500]};
`;

const Header = ({ headerText = 'Create New', detailText = '' }) => {
  return (
    <HeaderContainer>
      <Name>{headerText}</Name>
      <BasicInfo>{detailText}</BasicInfo>
    </HeaderContainer>
  );
};

Header.propTypes = {
  headerText: PropTypes.string,
  detailText: PropTypes.string,
};

export default Header;
