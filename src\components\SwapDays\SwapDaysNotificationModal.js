import React from 'react';

import PropTypes from 'prop-types';
import Modal from 'react-bootstrap/Modal';
import { PrimaryButton, SecondaryButton } from '@components/global/Buttons';

import { ModalButtonWrapper, ModalTitle } from './SwapDaysNotificationModal.styles';

const SwapDaysNotificationModal = ({ isOpen, handleCloseModal, handleOpenSidebar, date }) => {
  return (
    <Modal style={{ marginTop: '200px' }} className="modal" show={isOpen} onHide={() => {}}>
      <ModalTitle>{`It is time to select your swap days for ${date}!`}</ModalTitle>
      <ModalButtonWrapper className="modal-button-wrapper">
        <PrimaryButton left onClick={handleOpenSidebar}>
          Select Swap Days
        </PrimaryButton>
        <SecondaryButton right onClick={handleCloseModal}>
          Not Now
        </SecondaryButton>
      </ModalButtonWrapper>
    </Modal>
  );
};

SwapDaysNotificationModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  handleCloseModal: PropTypes.func.isRequired,
  handleOpenSidebar: PropTypes.func.isRequired,
  date: PropTypes.string.isRequired,
};

export default SwapDaysNotificationModal;
