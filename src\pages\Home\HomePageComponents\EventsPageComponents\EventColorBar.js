import React from 'react';
import PropTypes from 'prop-types';
import styled, { useTheme } from 'styled-components';

import getEventColorForEventType from '@utils/colorDictWithEventType';

const StyledColorBar = styled.div`
  background-color: ${({ barColor, theme }) => barColor || theme.colors.eventGreen};
  height: auto;
  width: 12px;
  border-radius: 15px 0px 0px 15px;
  @media (max-width: 450px) {
    padding-right: 12px;
  }
`;

const EventColorBar = ({ eventType }) => {
  const theme = useTheme();
  return <StyledColorBar barColor={getEventColorForEventType(eventType, theme)} />;
};

EventColorBar.propTypes = {
  eventType: PropTypes.string.isRequired,
};

export default EventColorBar;
