import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled, { ThemeProvider } from 'styled-components';

import { selectedEventState } from '@recoil/eventSidebar';

import { handleFormFieldChange, FormSelect, FormTextBox } from '@components/global/Form';
import { rescheduleReasons } from '@utils/businessLogic/heaBusinessLogic';

const StyledConfirmationBody = styled.div``;

const RescheduleEventConfirmation = ({ theme }) => {
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);

  const { rescheduleReason = '', notes = {} } = selectedEvent;

  const handleFieldChange = (e, updatedEvent = selectedEvent) => {
    return handleFormFieldChange(e, updatedEvent, setSelectedEvent);
  };

  return (
    <ThemeProvider theme={theme}>
      <StyledConfirmationBody>
        <FormSelect
          required
          name="rescheduleReason"
          value={rescheduleReason}
          title="Reason for Reschedule"
          options={rescheduleReasons}
          onChange={handleFieldChange}
        />
        <FormTextBox
          required
          name="notes.rescheduleNotes"
          value={notes?.rescheduleNotes}
          title="Reschedule Notes"
          placeholder=""
          onChange={handleFieldChange}
        />
      </StyledConfirmationBody>
    </ThemeProvider>
  );
};

const fireRescheduleConfirmation = async (RecoilBridge, theme) => {
  // Need to lazy load swal config so that the .app-content node exists in dom
  const { createSwalWithTheme } = await import('@config/swalConfig');
  const swal = createSwalWithTheme(theme);

  return swal.fire({
    titleText: 'Are you sure you want to reschedule this event later?',
    html: (
      <RecoilBridge>
        <RescheduleEventConfirmation theme={theme} />
      </RecoilBridge>
    ),
    icon: 'warning',
    confirmButtonText: 'Yes',
    showCancelButton: true,
    cancelButtonText: 'No',
  });
};

RescheduleEventConfirmation.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  theme: PropTypes.object.isRequired,
};

export { fireRescheduleConfirmation };

export default RescheduleEventConfirmation;
