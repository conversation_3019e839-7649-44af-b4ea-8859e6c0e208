import styled from 'styled-components';
import { Clickable } from '@components/global';

const StyledAgentSlot = styled(Clickable)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 20%;
  padding: 10px 0;
  border-radius: 4px;
  margin-right: 10px;
  overflow: hidden;
  color: ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.primary[500])};
  border: 1px solid
    ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.secondary[500])};
  font-weight: ${({ selected }) => (selected ? 'bold' : 'normal')};
`;

export default StyledAgentSlot;
