import styled from 'styled-components';
import React from 'react';
import PropTypes from 'prop-types';

const HeaderContainer = styled.nav`
  top: 0;
  width: 100%;
  background: ${({ theme }) => theme.secondary[200]};
  padding: 1.5em;
`;

const HeaderTitle = styled.div`
  font-style: normal;
  font-weight: 500;
  font-size: 22px;
  line-height: 26px;
  color: ${({ theme }) => theme.secondary[600]};
`;

const HeaderLabel = styled.div`
  font-style: normal;
  font-weight: ${({ $bold }) => ($bold ? '500' : 'normal')};
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.213333px;
  color: ${({ theme }) => theme.secondary[400]};
`;

const EventSidebarHeader = ({ children }) => {
  return <HeaderContainer>{children}</HeaderContainer>;
};

EventSidebarHeader.propTypes = {
  children: PropTypes.node.isRequired,
};

export default EventSidebarHeader;
export { HeaderTitle, HeaderLabel };
