import { selectorFamily } from 'recoil';
import pinnedEventsAtom from './pinnedEventsAtom';
import allEventsAtom from './allEventsAtom';

const pinnedEventSelectorFamily = selectorFamily({
  key: 'pinnedEventSelectorFamily',
  get: (id) => ({ get }) => {
    const allPinnedEvents = get(pinnedEventsAtom);
    return allPinnedEvents[id];
  },
  set: () => ({ get, set }, newId) => {
    const allPinnedEvents = get(pinnedEventsAtom);
    const pinnedEvent = allPinnedEvents[newId];

    let newPinnedEvents = { ...allPinnedEvents };
    if (pinnedEvent) delete newPinnedEvents[newId];
    else {
      const allEventComponents = get(allEventsAtom);
      const pinnedEventComponentDetails = allEventComponents[newId];
      newPinnedEvents = {
        ...allPinnedEvents,
        [newId]: { ...pinnedEventComponentDetails },
      };
    }
    set(pinnedEventsAtom, newPinnedEvents);
  },
});

export default pinnedEventSelectorFamily;
