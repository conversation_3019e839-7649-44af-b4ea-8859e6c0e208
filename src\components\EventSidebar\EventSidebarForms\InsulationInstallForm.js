import React, { useState, Suspense, useEffect, useRef } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import moment from 'moment';
import _ from 'lodash';

import { SalesforceManager, UtilityManager } from '@utils/APIManager';
import useStartEndTimes from '@hooks/useStartEndTimes';
import { formatCurrency } from '@utils/functions';

import wxJobDetailsPrint from '@utils/wxJobDetailsPrint';
import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import { Payment } from '@components/Calendar/EventComponents';
import {
  Row,
  Col,
  AddRemoveButtonContainer,
  FormInput,
  FormTextBox,
  FormSelect,
  FormMultiselect,
  FormInfoField,
  FormInfo,
  FormStartEndDateTimePickers,
  FormAddRemoveDayButtons,
  handleFormFieldChange,
} from '@components/global/Form';
import { History } from '@styled-icons/boxicons-regular/History';

import { eventTypesByOidSelectorFamily, jobAttributesSelectorFamily } from '@recoil/app';
import { agentsFormOptionsSelector } from '@recoil/agents';
import { selectedEventState } from '@recoil/eventSidebar';
import { useValidateSelectedEvent } from '@recoil/hooks';

import Pin from '@components/Calendar/Pin';
import Lock from '@components/Calendar/Lock';
import EventSidebarHeader, {
  HeaderTitle,
  HeaderLabel,
} from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import DropDownMenu from '@components/global/DropDownMenu/DropDownMenu';
import ThreeVerticalDots from '@components/global/Icons/ThreeVerticalDots';
import PrintDocsButton from '@components/EventSidebar/PrintDocsButton';
import { LoadingIndicator } from '@components/global';
import { isAuthorized } from '@utils/AuthUtils';
import { EventHistoryModal } from '@components/EventHistoryModal/EventHistoryModal';
import TruckService from './TruckService';

const confirmationStatusOptions = [
  'Pending',
  '1 Attempt',
  '2 Attempts',
  '3 Attempts',
  '4 Attempts',
  'Not Confirmed',
  'Confirmed',
];

const wxVisitResultOptions = [
  'Walk',
  'Complete',
  'Reschedule',
  'Building Inspector No Show',
  'Customer No Show',
];

// TODO: this should be refactored as it's used in any form that needs lock/pin
const IconContainer = styled.div`
  margin-right: 10px;
  margin-top: auto;
  height: 24px;
  & :hover {
    cursor: pointer;
  }
`;

const HistoryIcon = styled(History)`
  height: 24px;
  cursor: pointer;
`;

const InsulationInstallForm = ({ handleSaveClick, handleCancelClick }) => {
  useValidateSelectedEvent();
  const [event, setEvent] = useRecoilState(selectedEventState);
  const paymentRef = useRef();

  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const handleHistoryModal = () => setIsHistoryModalOpen(!isHistoryModalOpen);
  const {
    id,
    customerName,
    leadVendor,
    amount,
    date,
    oids,
    sfIds: { operationsId, dealId },
    siteId,
    address,
    notes: { officeNotes, fieldNotes, summaryNotes },
    phoneNumber,
    secondaryPhoneNumber,
    jobStatus,
    confirmationStatus,
    jobLength,
    estimatedJobLength,
    startEndTimes = [], // [{start: '', end: ''}, {start: '', end: ''}]
    type,
    scheduledBy,
    scheduledDate,
    attributes = [],
    lock,
    wxVisitResult,
    isMultifamily,
    finalContractAmountAtApproval = 0,
    revisedContractAmount = 0,
  } = event;

  // The event won't have an ID yet if we're just creating it.
  const isCreateEvent = !id;

  const [loading, setLoading] = useState(false);

  // Used for getting the job types to be displayed.
  // TODO: This is somewhat of a temporary work around for manager QC visits
  // Need to determine if we would like to filter down the event types to only the selected crew's,
  // Or if we should follow the 'attributes' pattern and remove any trucks that don't fit the event type.
  // Might be better to follow the attributes pattern, but we first need to have better manager QC support
  // To only show those event types for managers on the calendar
  const [oid] = oids;

  const eventTypesForUser = useRecoilValue(eventTypesByOidSelectorFamily(oid));

  const defaultType = eventTypesForUser[0]?.value;

  const isUserScheduler = isAuthorized('Scheduler', 'Insulation');
  const canPerformActions = isUserScheduler && !lock;

  const isTruckService = type === '000503';

  useEffect(() => {
    if (!date || !isCreateEvent) return;

    // If no type for create event, set defaultType
    if (!type) setEvent({ ...event, type: defaultType });
  }, [date, defaultType, type, isCreateEvent, setEvent, event]);

  // For some reason, this needs to be below the above useEffect, or else its value gets overwritten with the previous event value
  const handleTimeChange = useStartEndTimes();

  const syncFromSalesforce = async (operationsId) => {
    setLoading(true);
    const salesforceInfo = await SalesforceManager.getInsulationEventInfoWithOperationsId(
      operationsId,
    );
    setLoading(false);
    if (!salesforceInfo) return;

    const {
      siteId,
      jobStatus,
      leadVendor,
      amount,
      notes,
      sfIds,
      phoneNumber,
      secondaryPhoneNumber,
      customerName,
      address,
      // insulation specific
      estimatedJobLength,
      attributes,
      finalContractAmountAtApproval,
      revisedContractAmount,
    } = salesforceInfo;

    const eventUpdateObject = {
      sfIds,
      customerName,
      siteId,
      jobStatus,
      leadVendor,
      address,
      amount,
      phoneNumber,
      secondaryPhoneNumber,
      estimatedJobLength,
      notes,
      attributes,
      finalContractAmountAtApproval,
      revisedContractAmount,
    };
    const updatedEvent = _.merge({}, event, eventUpdateObject);

    setEvent(updatedEvent);
  };

  const handleOperationsIdChange = async (e) => {
    handleFieldChange(e);
    const { value: operationsId } = e.target;
    if (![15, 18].includes(operationsId.length)) return false;
    return syncFromSalesforce(operationsId);
  };

  const handleFieldChange = (e, updatedEvent = event) => {
    return handleFormFieldChange(e, updatedEvent, setEvent);
  };

  const getDocRepoLink = () => {
    const baseUrl = UtilityManager.getDocRepoBaseUrl(dealId);
    window.open(
      `${baseUrl}/${dealId}?customer_name=${encodeURIComponent(
        customerName,
      )}&leadVendor=${encodeURIComponent(leadVendor)}&singleFamily=${!isMultifamily}`,
      '_blank',
    );
  };

  const dropDownValues = [
    { text: 'Doc Repo link', onClick: () => getDocRepoLink() },
    {
      text: 'SF Ops Page',
      onClick: () => UtilityManager.openSfPage(operationsId, 'Operations__c'),
    },
    { text: 'Job Details', onClick: () => wxJobDetailsPrint(event) },
    {
      text: 'Sync from SF',
      onClick: async () => syncFromSalesforce(operationsId),
    },
  ];

  // Show Job State only for Insulation Install, Manager Visit and Manager QC Visit
  // 000590 is Rhode Island. Will revisit if we start getting more visits
  const showWxVisitResult = ['000500', '000505', '000506', '000590'].includes(type);

  return (
    <SidebarForm>
      {/* TODO: better suspense support */}
      <Suspense fallback={<LoadingIndicator loading message="Loading..." fullscreen={false} />}>
        {isTruckService ? (
          <TruckService eventTypesForUser={eventTypesForUser} />
        ) : (
          <>
            <EventSidebarHeader>
              <Row>
                <Col size={2}>
                  <HeaderLabel>Customer Name:</HeaderLabel>
                  <HeaderTitle>{customerName}</HeaderTitle>
                  <HeaderLabel>Lead Vendor:</HeaderLabel>
                  <HeaderLabel>{leadVendor}</HeaderLabel>
                  <HeaderLabel>Job Status:</HeaderLabel>
                  <HeaderLabel>{jobStatus}</HeaderLabel>
                </Col>
                <Col size={1} right>
                  <HeaderLabel>Amount:</HeaderLabel>
                  <HeaderTitle>{formatCurrency(amount)}</HeaderTitle>
                  <HeaderLabel>Amount at Approval:</HeaderLabel>
                  <HeaderTitle>{formatCurrency(finalContractAmountAtApproval || 0)}</HeaderTitle>
                  <HeaderLabel>Revised Amount:</HeaderLabel>
                  <HeaderTitle>{formatCurrency(revisedContractAmount || 0)}</HeaderTitle>
                </Col>
                {!isCreateEvent && (
                  <Col size={0} left>
                    <Row>
                      <IconContainer>
                        <HistoryIcon onClick={handleHistoryModal} />
                      </IconContainer>
                      <IconContainer>
                        {canPerformActions && <Pin event={event} onSidebar />}
                      </IconContainer>
                      <IconContainer>{isUserScheduler && <Lock event={event} />}</IconContainer>
                      <PrintDocsButton department={`${type.slice(0, 4)}00`} dealId={dealId} />
                      <DropDownMenu
                        DropDownIcon={<ThreeVerticalDots />}
                        listItems={dropDownValues}
                      />
                    </Row>
                    <Row>
                      <Payment
                        ref={paymentRef}
                        showButton
                        onClick={() => paymentRef.current.handlePaymentButtonClick()}
                      />
                    </Row>
                  </Col>
                )}
              </Row>
            </EventSidebarHeader>
            <EventSidebarBody>
              <Row>
                <Col>
                  <FormInput
                    readOnly={!isCreateEvent}
                    required
                    title="operations id"
                    placeholder="Enter Operations ID"
                    name="sfIds.operationsId"
                    value={operationsId}
                    onChange={handleOperationsIdChange}
                    testid="insulation-operations-id-input"
                  />
                  <FormSelect
                    required
                    title="Job Type"
                    placeholder="Select Job Type"
                    name="type"
                    value={type}
                    onChange={handleFieldChange}
                    options={eventTypesForUser}
                    testid="insulation-job-type-input"
                  />
                  {showWxVisitResult && (
                    <FormSelect
                      required
                      title="WX Visit Result"
                      placeholder=""
                      name="wxVisitResult"
                      value={wxVisitResult || ''}
                      onChange={handleFieldChange}
                      options={wxVisitResultOptions}
                      testid="insulation-job-state-input"
                      disablePlaceholder={false}
                    />
                  )}

                  <FormMultiselect
                    required
                    title="Requirement(s)"
                    name="attributes"
                    recoilOptions={jobAttributesSelectorFamily({ type })}
                    onChange={handleFieldChange}
                    value={attributes}
                    testid="insulation-requirements-multi-select"
                  />
                  <FormMultiselect
                    required
                    title="Truck(s)"
                    name="oids"
                    recoilOptions={agentsFormOptionsSelector}
                    onChange={handleFieldChange}
                    value={oids}
                  />
                  <FormTextBox
                    title="HEA Notes"
                    name="notes.fieldNotes"
                    value={fieldNotes}
                    readOnly
                  />
                  <FormTextBox
                    title="Insulation Notes"
                    name="notes.officeNotes"
                    value={officeNotes}
                    onChange={handleFieldChange}
                  />
                  <FormTextBox
                    title="Summary Notes"
                    name="notes.summaryNotes"
                    value={summaryNotes}
                    onChange={handleFieldChange}
                  />
                </Col>
                <Col>
                  <FormTextBox
                    readOnly
                    title="Location"
                    placeholder="Enter Location"
                    name="address"
                    value={address?.displayAddress || ''}
                  />
                  <FormInput
                    title="site id"
                    placeholder="Enter Site ID"
                    name="siteId"
                    value={siteId}
                    onChange={handleFieldChange}
                  />
                  <FormInput
                    title="Primary Phone number"
                    placeholder="Enter Number"
                    name="phoneNumber"
                    value={phoneNumber}
                    onChange={handleFieldChange}
                  />
                  <FormInput
                    title="Secondary Phone number"
                    placeholder="Enter Number"
                    name="secondaryPhoneNumber"
                    value={secondaryPhoneNumber}
                    onChange={handleFieldChange}
                  />
                  <FormSelect
                    title="Confirmation Status"
                    placeholder="Select Confirmation Status"
                    name="confirmationStatus"
                    value={confirmationStatus}
                    onChange={handleFieldChange}
                    options={confirmationStatusOptions}
                  />
                  {estimatedJobLength > 0 && (
                    <FormInput
                      readOnly
                      title="Estimated Service Duration"
                      name="estimatedJobLength"
                      value={estimatedJobLength}
                    />
                  )}
                  <>
                    <FormStartEndDateTimePickers
                      allowDateSelect={false}
                      startEndTimes={startEndTimes}
                      onChange={handleTimeChange}
                    />
                    <AddRemoveButtonContainer>
                      <FormAddRemoveDayButtons
                        name="jobLength"
                        value={jobLength}
                        onChange={handleFieldChange}
                        amount={1}
                        testid="1"
                      >
                        1 Day
                      </FormAddRemoveDayButtons>
                      <FormAddRemoveDayButtons
                        name="jobLength"
                        value={jobLength}
                        onChange={handleFieldChange}
                        amount={0.5}
                        testid="0-5"
                      >
                        1/2 Day
                      </FormAddRemoveDayButtons>
                      <FormAddRemoveDayButtons
                        amount={0.25}
                        value={jobLength}
                        name="jobLength"
                        onChange={handleFieldChange}
                        testid="0-25"
                      >
                        1/4 Day
                      </FormAddRemoveDayButtons>
                    </AddRemoveButtonContainer>
                  </>
                  {!isCreateEvent && (
                    <FormInfo>
                      <FormInfoField title="Scheduled By :" body={scheduledBy} />
                      <FormInfoField
                        title="Scheduled On :"
                        body={moment.utc(new Date(scheduledDate)).format('MMMM Do YYYY, h:mm a')}
                      />
                    </FormInfo>
                  )}
                </Col>
              </Row>
            </EventSidebarBody>

            {!isCreateEvent && event?.id && (
              <EventHistoryModal
                isOpen={isHistoryModalOpen}
                id={event.id}
                handleClose={handleHistoryModal}
              />
            )}
          </>
        )}
        <EventSidebarFooter>
          {!isCreateEvent && canPerformActions && (
            <CancelButton left onClick={() => handleCancelClick()}>
              Cancel Event
            </CancelButton>
          )}
          <PrimaryButton right onClick={() => handleSaveClick()}>
            Save
          </PrimaryButton>
        </EventSidebarFooter>
        <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
      </Suspense>
    </SidebarForm>
  );
};

InsulationInstallForm.propTypes = {
  handleCancelClick: PropTypes.func.isRequired,
  handleSaveClick: PropTypes.func.isRequired,
};

export default InsulationInstallForm;
