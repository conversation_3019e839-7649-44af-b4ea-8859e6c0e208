import React from 'react';

import styled from 'styled-components';
import PropTypes from 'prop-types';
import moment from 'moment';
import DatePicker from 'react-datepicker';

const TitleHeading = styled.h4`
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 5px;
  text-transform: uppercase;
  color: ${({ theme }) => theme.secondary[500]};
`;

const DatePickerBodyWrapper = styled.div`
  padding-bottom: 10px;
  & .react-datepicker-wrapper {
    width: 100%;
    & .react-datepicker__input-container {
      display: block;
      & input {
        min-height: 32px;
        background: ${({ theme }) => theme.secondary[100]};
        border: 1px solid ${({ theme }) => theme.secondary[300]};
        box-sizing: border-box;
        border-radius: 4px;
      }
    }
  }
  & .react-datepicker--time-only {
    font-size: 14px;
  }
`;

const FormDatePicker = ({
  dateFormat = 'MMMM d, yyyy',
  name,
  onChange,
  title,
  value = null,
  minDate = null,
  maxDate = null,
  useSyntheticEvent = false,
  ...otherProps
}) => {
  // TODO: a lot of this functionality is taken from the formDateTimePicker. they should be merged together eventually but i don't want to cause any breaking changes
  const handleDateChange = (newDate) => {
    if (!useSyntheticEvent) {
      const date = moment(newDate).format('YYYY-MM-DD');
      return onChange(date, name);
    }

    const value = moment(newDate).startOf('date');
    const syntheticEvent = {
      target: {
        name,
        value,
      },
    };
    return onChange(syntheticEvent);
  };

  return (
    <DatePickerBodyWrapper>
      <TitleHeading> {title} </TitleHeading>
      <DatePicker
        placeholderText="Click to select a date"
        name={name}
        selected={value?.toDate()}
        onChange={handleDateChange}
        dateFormat={dateFormat}
        minDate={minDate?.toDate()}
        maxDate={maxDate?.toDate()}
        {...otherProps}
      />
    </DatePickerBodyWrapper>
  );
};

FormDatePicker.propTypes = {
  title: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  value: PropTypes.instanceOf(moment),
  onChange: PropTypes.func.isRequired,
  dateFormat: PropTypes.string,
  minDate: PropTypes.instanceOf(moment),
  maxDate: PropTypes.instanceOf(moment),
  useSyntheticEvent: PropTypes.bool,
};

export default FormDatePicker;
