import moment from 'moment';

/**
 * Calculates and returns array of all days in a given month
 * @param {string | moment} today
 * @returns {Array}
 */
export const getDaysInCurrentMonth = (today = moment()) => {
  const date = moment(today).local();

  const numberOfDaysInMonth = date.daysInMonth();

  const dateArray = [];
  let dateNumber = 1;
  while (dateNumber <= numberOfDaysInMonth) {
    dateArray.push(
      moment(date)
        .set('date', dateNumber)
        .toDate(),
    );
    dateNumber++;
  }

  return dateArray;
};

/**
 * Takes in a date object and calculates if the date falls on a weekend or not
 * @param {moment} day
 * @returns {boolean}
 */
export const isDayWeekend = (day) => day.day() % 6 === 0;

/**
 * Takes in the lastUpdated date and returns whether or not the swap days sidebar/notification should be shown
 * Calculation - checks if lastUpdated is 1 month and 7 days or less
 * @param {string} lastUpdated required
 * @param {string} today optional
 * @returns {boolean}
 */
export const checkLastUpdatedSwapMonth = (lastUpdated, today = moment()) => {
  const last = moment(lastUpdated).local();
  const now = moment(today).local();

  // Last is stored as the first of the month after this was updated.
  // Example: If I select swap days in July for September, last will be stored as Aug 1st
  // So every middle of the month around the 15th/16th, this will make this true and trigger the pop-up
  const dateToStartShowingSidebar = moment(last).add(15, 'days');

  return !lastUpdated || now >= dateToStartShowingSidebar;
};
