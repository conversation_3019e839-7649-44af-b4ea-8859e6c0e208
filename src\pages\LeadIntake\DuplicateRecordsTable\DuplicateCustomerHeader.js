import React from 'react';
import styled from 'styled-components';

export const TableTitle = styled.div`
  font-weight: 700;
  font-size: 15px;
  margin-bottom: 8px;
  margin-top: 10px;
`;

export const TableDescription = styled.p`
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 5px;
  text-transform: none;
  color: #201d1d;
`;

export const TableActionsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  width: 20%;
  margin-top: 10px;
  gap: 12px;
`;

const DuplicateCustomerHeader = () => {
  return (
    <>
      <TableTitle>Duplicate Customer(s) Found</TableTitle>
      <TableDescription>
        These existing customers have similar contact information to what was entered. If the
        customer already exists, copy the deal id(s) from the table below and schedule the audit
        from this page. Otherwise, click Forward.
      </TableDescription>
    </>
  );
};

export default DuplicateCustomerHeader;
