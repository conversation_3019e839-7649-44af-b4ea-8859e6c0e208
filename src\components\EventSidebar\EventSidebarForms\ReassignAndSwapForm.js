import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useRecoilValue } from 'recoil';

import { PrimaryButton } from '@components/global/Buttons';
import RadioSlots from '@components/getSlots/RadioSlots';
import { Header } from '@components/global';
import { Row, Col } from '@components/global/Form';
import EventSidebarHeader, { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import { selectedEventState, availableSlotsAtom } from '@recoil/eventSidebar';
import { decodeEventType } from '@homeworksenergy/utility-service';
import SidebarForm from './SidebarForm';
import EventSidebarBody from '../EventSidebarBody';

const ReassignAndSwapForm = ({
  handleReassignClick,
  handleSwapClick,
  handleFindReassignSwapSlots,
}) => {
  const availableSlots = useRecoilValue(availableSlotsAtom);
  const selectedEvent = useRecoilValue(selectedEventState);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [reassignSlots, setReassignSlots] = useState([]);
  const [swapSlots, setSwapSlots] = useState([]);
  const [action, setAction] = useState('');

  const { business: department } = decodeEventType(selectedEvent?.type);

  // Loading Reassign and Swap Slots
  useEffect(() => {
    const findReassignSlots = async () => {
      const slots = await handleFindReassignSwapSlots('reassign');
      setReassignSlots(slots);
    };
    const findSwapSlots = async () => {
      const slots = await handleFindReassignSwapSlots('swap');
      setSwapSlots(slots);
    };
    findReassignSlots();
    findSwapSlots();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!availableSlots) return null;

  const handleChange = (event) => {
    const { value: newSelectedSlot, name: action } = event.target;
    setAction(action);

    setSelectedSlot(newSelectedSlot);
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col>
            <HeaderLabel>Reassign {department} Visit:</HeaderLabel>
            <Header h3>{selectedSlot?.address ? selectedSlot?.address?.displayAddress : ''}</Header>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <RadioSlots
          title="Reassign Slots"
          name="reassign"
          slotsToDisplay={reassignSlots}
          handleChange={handleChange}
          selectedSlot={selectedSlot}
        />
        <RadioSlots
          title="1 - 1 Swap"
          name="swap"
          slotsToDisplay={swapSlots}
          handleChange={handleChange}
          selectedSlot={selectedSlot}
        />
      </EventSidebarBody>
      <EventSidebarFooter>
        {action && (
          <PrimaryButton
            left
            onClick={
              action && action === 'reassign'
                ? () => handleReassignClick(selectedSlot)
                : () => handleSwapClick(selectedSlot)
            }
          >
            {action === 'reassign' ? 'Reassign' : 'Swap'}
          </PrimaryButton>
        )}
      </EventSidebarFooter>
    </SidebarForm>
  );
};

ReassignAndSwapForm.propTypes = {
  handleReassignClick: PropTypes.func.isRequired,
  handleFindReassignSwapSlots: PropTypes.func.isRequired,
  handleSwapClick: PropTypes.func.isRequired,
};

export default ReassignAndSwapForm;
