import { useState, useCallback, useRef, useEffect } from 'react';
import { useRecoilValue } from 'recoil';
import loadGoogleMaps from '@utils/loadGoogleMaps';
import { throwError } from '@utils/EventEmitter';
import { googleMapsApiKeyAtom } from '@recoil/app';

const useGoogleMapInitialization = () => {
  const mapRef = useRef(null);
  const googleMapRef = useRef(null);
  const [mapInitialized, setMapInitialized] = useState(false);
  const googleMapsApiKey = useRecoilValue(googleMapsApiKeyAtom);

  const initializeMap = useCallback(() => {
    if (!window.google || !mapRef.current || mapInitialized) return;

    const map = new window.google.maps.Map(mapRef.current, {
      zoom: 10,
      center: { lat: 41.6032, lng: -73.0877 }, // Connecticut center
      mapTypeId: window.google.maps.MapTypeId.ROADMAP,
      styles: [
        {
          featureType: 'poi',
          elementType: 'labels',
          stylers: [{ visibility: 'off' }],
        },
      ],
    });

    googleMapRef.current = map;
    setMapInitialized(true);
  }, [mapInitialized]);

  // Initialize map on mount
  useEffect(() => {
    const initGoogleMaps = async () => {
      try {
        if (window.google && window.google.maps) {
          initializeMap();
        } else {
          await loadGoogleMaps(googleMapsApiKey);
          initializeMap();
        }
      } catch (error) {
        throwError('Failed to load Google Maps. Please check your API key.');
      }
    };

    if (googleMapsApiKey) {
      initGoogleMaps();
    }
  }, [initializeMap, googleMapsApiKey]);

  return { googleMapRef, mapRef, mapInitialized };
};

export default useGoogleMapInitialization;
