import React, { useState } from 'react';
import styled, { css } from 'styled-components';
import PropTypes from 'prop-types';

import { FilePdf } from '@styled-icons/bootstrap/FilePdf';
import { FileEarmarkCheck } from '@styled-icons/bootstrap/FileEarmarkCheck';
import { FileEarmarkExcel } from '@styled-icons/bootstrap/FileEarmarkExcel';
import { FileImage } from '@styled-icons/bootstrap/FileImage';
import Tooltip from '../Tooltip/Tooltip';

const DocRepoIconContainer = styled.div`
  position: relative;
  height: 100%;
`;

const iconStyle = css`
  cursor: pointer;
  &:hover {
    background-color: initial;
    background-position: 0 0;
    color: black;
  }
  height: 30px;
`;

const FilePdfIconStyled = styled(FilePdf)`
  ${iconStyle};
  color: ${({ theme }) => theme.colors.isRequired};
`;
const FileEarmarkCheckIconStyled = styled(FileEarmarkCheck)`
  ${iconStyle};
  margin-left: 10px;
  color: ${({ theme }) => theme.colors.eventGreen};
`;
const FileEarmarkExcelIconStyled = styled(FileEarmarkExcel)`
  ${iconStyle};
  margin-left: 10px;
  color: ${({ theme }) => theme.colors.red};
`;
const FileImageIconStyled = styled(FileImage)`
  ${iconStyle};
  color: ${({ theme }) => theme.colors.eventA};
`;

const DocRepoIcon = ({ doc, openFile }) => {
  const [hoveringDoc, setHoveringDoc] = useState(null);
  const getIcon = ({ uploaded, sensitive, type }) => {
    if (sensitive) return uploaded > 0 ? FileEarmarkCheckIconStyled : FileEarmarkExcelIconStyled;
    switch (type) {
      case 'jpeg':
        return FileImageIconStyled;
      case 'pdf':
      default:
        return FilePdfIconStyled;
    }
  };

  const DocIconComponent = getIcon(doc);

  return (
    <DocRepoIconContainer>
      <DocIconComponent
        onClick={() => {
          if (doc.uploaded) {
            openFile(doc);
          }
        }}
        onMouseEnter={() => setHoveringDoc(doc.name)}
        onMouseLeave={() => setHoveringDoc(null)}
      />
      {hoveringDoc === doc.name && (
        <Tooltip text={doc.uploaded ? 'View Document' : 'Document Not Uploaded'} />
      )}
    </DocRepoIconContainer>
  );
};

DocRepoIcon.propTypes = {
  doc: PropTypes.shape({
    uploaded: PropTypes.bool,
    name: PropTypes.string,
  }).isRequired,
  openFile: PropTypes.func.isRequired,
};

export default DocRepoIcon;
