import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import moment from 'moment';
import _ from 'lodash';

import utilityService from '@homeworksenergy/utility-service';

import { selectedEventState } from '@recoil/eventSidebar';

import FormStartEndDateTimePicker from './FormStartEndDateTimePicker';

const FormStartEndDateTimePickers = ({
  displayDay = true,
  allowDateSelect = true,
  direction = 'column',
  dateFormat = 'MMMM d, yyyy h:mm aa',
  timeFormat = 'h:mm aa',
}) => {
  const [selectedEvent, setSelectedEvent] = useRecoilState(selectedEventState);

  const { date, jobLength, startTime, startEndTimes } = selectedEvent;

  // TODO: same function in insulation form, move to util file with handleFormFieldChange
  const handleTimeChange = (newTime, index, startOrEnd) => {
    if (
      index === 0 &&
      startOrEnd === 'start' &&
      !moment(newTime).isSame(moment(startEndTimes[0]?.start), 'date')
    )
      return handleDateChange(newTime);

    // Deep clone necessary so we don't directly mutate state
    const updatedTimes = _.cloneDeep(startEndTimes);
    updatedTimes[index][startOrEnd] = moment(newTime);

    return setSelectedEvent({ ...selectedEvent, startEndTimes: updatedTimes });
  };

  const handleDateChange = (newDateTime) => {
    const startEndTimes = utilityService.getDefaultStartEndTimes(
      jobLength,
      newDateTime,
      undefined,
      parseInt(startTime, 10),
    );

    const date = moment(newDateTime).format('MM/DD/YYYY');

    return setSelectedEvent({ ...selectedEvent, date, startEndTimes });
  };

  return startEndTimes.map(({ start, end }, index) => {
    return (
      <FormStartEndDateTimePicker
        key={`${date} ${start} ${end}`}
        startTime={moment(start)}
        endTime={moment(end)}
        onChange={(newTime, startOrEnd) => {
          handleTimeChange(newTime, index, startOrEnd);
        }}
        day={index + 1}
        displayDay={displayDay}
        allowDateSelect={allowDateSelect}
        direction={direction}
        dateFormat={dateFormat}
        timeFormat={timeFormat}
      />
    );
  });
};

FormStartEndDateTimePickers.propTypes = {
  startEndTimes: PropTypes.arrayOf(
    PropTypes.shape({ start: PropTypes.shape({}), end: PropTypes.shape({}) }),
  ),
  onChange: PropTypes.func,
  displayDay: PropTypes.bool,
  allowDateSelect: PropTypes.bool,
  direction: PropTypes.oneOf(['row', 'column']),
  dateFormat: PropTypes.string,
  timeFormat: PropTypes.string,
};

export default FormStartEndDateTimePickers;
