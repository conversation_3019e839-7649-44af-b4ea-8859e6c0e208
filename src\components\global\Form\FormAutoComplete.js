import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { uniqueId } from 'lodash';
import Swal from 'sweetalert2/dist/sweetalert2';

import FormFieldContainer from './FormFieldContainer';
import FormFieldLabel from './FormFieldLabel';
import RecoilFieldOptions from './RecoilFieldOptions';

const StyledText = styled.div`
  padding-top: 5px;
  color: red;
`;

const AutocompleteContainer = styled.div`
  position: relative;
`;

const AutocompleteInput = styled.input`
  width: 100%;
  min-height: 32px;
  background: ${({ readOnly, theme }) => (readOnly ? theme.secondary[200] : theme.secondary[100])};
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  box-sizing: border-box;
  border-radius: 4px;
  padding: 0.5rem;
`;

const AutocompleteDropdown = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: ${({ theme }) => theme.secondary[100]};
  border: 1px solid ${({ theme }) => theme.secondary[300]};
  border-top: none;
  border-radius: 0 0 4px 4px;
  z-index: 1;
`;

const AutocompleteOption = styled.div`
  padding: 0.5rem;
  cursor: pointer;

  &:hover {
    background-color: ${({ theme }) => theme.secondary[200]};
  }
`;
//  AutoComplete Component accept list of {name, value}[]
//  Recoil Options are also supported
//  When user start typing a value in field it will auto filter
//  if user select option from drop down it will be selected as value
//  it's not a free solo input so if user erase a value, then whole value from input field will be erased.
const FormAutoComplete = (props) => {
  const {
    title,
    placeholder = '',
    readOnly = false,
    required = false,
    options,
    name = '',
    onChange = () => {},
    testId = '',
    compact = false,
    customPaddingRight = false,
    customWidth = false,
    recoilOptions = null,
    value = '',
  } = props;

  const disableField = value && readOnly;
  const listOptions = recoilOptions || options;

  const inputValueRef = useRef('');
  const [inputValue, setInputValue] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(listOptions);

  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef(null);
  const showDropDownList = showDropdown && filteredOptions.length > 0;
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
    inputValueRef.current = e.target.value;
    const filtered = listOptions?.filter((option) =>
      option.name?.toLowerCase().includes(e.target.value.toLowerCase()),
    );
    setFilteredOptions(filtered);
    setShowDropdown(true);
  };

  const handleOptionClick = (option) => {
    if (!option?.value) {
      return Swal.fire({ title: 'Selected Auditor is Invalid', icon: 'error' });
    }
    setInputValue(option?.name);
    setShowDropdown(false);
    const changeEvent = { target: { name, value: option?.value } };
    inputValueRef.current = option.name;
    onChange(changeEvent);
    return changeEvent;
  };

  const handleClickOutside = (e) => {
    if (dropdownRef.current && !dropdownRef.current?.contains(e.target)) {
      setShowDropdown(false);
      const optionSelected = filteredOptions?.some(
        (option) => option.name === inputValueRef.current,
      );
      if (!optionSelected && filteredOptions.length) {
        setInputValue('');
        inputValueRef.current = '';
        onChange({ name, value: '' });
      }
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (value && listOptions.length) {
      const fieldValue = listOptions?.filter((agent) => agent.value === value)?.[0] || '';
      setInputValue(fieldValue?.name);
      inputValueRef.current = fieldValue?.name || '';
    }
  }, [value, listOptions]);
  if (recoilOptions) return <RecoilFieldOptions Component={FormAutoComplete} {...props} />;

  return (
    <FormFieldContainer
      required={required}
      fieldName={name}
      compact={compact}
      customPaddingRight={customPaddingRight}
      customWidth={customWidth}
    >
      <FormFieldLabel>{title}</FormFieldLabel>
      <AutocompleteContainer>
        <AutocompleteInput
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          readOnly={disableField}
          placeholder={placeholder}
          name={name}
          data-testid={testId}
          ref={dropdownRef}
        />
        {showDropDownList && (
          <AutocompleteDropdown>
            {filteredOptions?.map((option) => (
              <AutocompleteOption key={uniqueId('id_')} onClick={() => handleOptionClick(option)}>
                {option?.name}
              </AutocompleteOption>
            ))}
          </AutocompleteDropdown>
        )}
        {!filteredOptions.length && inputValueRef.current && (
          <StyledText> The filter return no results</StyledText>
        )}
      </AutocompleteContainer>
    </FormFieldContainer>
  );
};

FormAutoComplete.propTypes = {
  title: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  value: PropTypes.string,
  onChange: PropTypes.func,
  name: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({ key: PropTypes.string, value: PropTypes.string }),
    ]),
  ).isRequired,
  recoilOptions: PropTypes.shape({}),
  testId: PropTypes.string,
  compact: PropTypes.bool,
  customPaddingRight: PropTypes.bool,
  customWidth: PropTypes.bool,
};

export default FormAutoComplete;
