import React, { useEffect, useState } from 'react';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import _ from 'lodash';

import FormCheckbox from '@components/global/Form/FormCheckboxes';
import { Container, Row, Col, handleFormFieldChange } from '@components/global/Form';

import { eventTypeOptionsSelectorFamily } from '@recoil/app';
import { selectedAgentAtom, attributesOptionsSelector } from '@recoil/admin/agents';
import { adminInfoHasChangedState, adminInfoChangesState } from '@recoil/admin';

const EditAgentEventTypes = () => {
  const [agent, setAgent] = useRecoilState(selectedAgentAtom);
  const { department: departmentId, departmentName, eventTypes, attributes, state } = agent;
  const options = useRecoilValue(eventTypeOptionsSelectorFamily({ departmentId, state }));
  const agentInfoHasChanged = useRecoilValue(adminInfoHasChangedState);
  const attributesOptions = useRecoilValue(attributesOptionsSelector);
  const setAgentInfoChanges = useSetRecoilState(adminInfoChangesState);
  // Options returns with an 'ungrouped' option for default
  const [selectedGroups, setSelectedGroups] = useState(['ungrouped']);
  const [availableEventTypes, setAvailableEventTypes] = useState([]);

  useEffect(() => {
    if (selectedGroups.length > 0) handleAvailableTypes(selectedGroups, eventTypes);
    handleGroups(eventTypes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!agentInfoHasChanged) handleGroups(eventTypes);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [agentInfoHasChanged]);

  const handleAvailableTypes = (selectedGroups, eventTypes) => {
    let newSelectedEventTypes = [];
    // Every time selected groups changes, loop through each selected group
    const newAvailableTypes = selectedGroups.reduce((acc, group) => {
      // Flatten out all event types for that selected group
      acc.push(
        ...options.reduce((acc, option) => {
          // Push all event types associated with that selected group to the accumulator
          if (option.value === group) {
            acc.push(...option.eventTypes);
            // Filter Agent event type on groups selecte/deselect
            const filterSelectedGroupsEventTypes = eventTypes.filter((eventType) => {
              return option.eventTypes
                .map(({ value }) => {
                  return value;
                })
                .includes(eventType);
            });
            newSelectedEventTypes = [...newSelectedEventTypes, ...filterSelectedGroupsEventTypes];
          }
          return acc;
        }, []),
      );
      return acc;
    }, []);

    handleFormFieldChange(
      { target: { name: 'eventTypes', value: newSelectedEventTypes } },
      agent,
      setAgent,
    );
    setAvailableEventTypes(newAvailableTypes);
  };

  const handleGroups = (eventTypes) => {
    // Every time event types selected/deselected , loop through each options
    if (options.length === 1) return;
    const newGroups = options.reduce(
      (acc, option) => {
        // Checks selected event type is present in current option's event type
        if (option.eventTypes.some(({ value }) => eventTypes.includes(value))) {
          acc.push(option.value);
        }
        return acc;
      },
      ['ungrouped'],
    );
    const uniqueGroups = _.uniq(newGroups);
    setSelectedGroups(uniqueGroups);
    handleAvailableTypes(uniqueGroups, eventTypes);
  };

  const showGroups = options.length > 1;

  const handleFieldChange = (e) => {
    handleFormFieldChange(e, agent, setAgent);
    const { name, value } = e.target;
    // stores only those values in an atom whose value is changed
    setAgentInfoChanges({ name, value });
  };

  return (
    <Container>
      <Row>
        {showGroups && (
          <Col size={1}>
            <FormCheckbox
              title="Group"
              name="group"
              options={options.filter((option) => option.value !== 'ungrouped')}
              onChange={(e) => {
                setSelectedGroups(e.target.value);
                handleAvailableTypes(e.target.value, eventTypes);
                handleFieldChange(e);
              }}
              value={selectedGroups}
            />
          </Col>
        )}
        <Col size={2}>
          <FormCheckbox
            title="Job Types"
            name="eventTypes"
            options={availableEventTypes}
            onChange={handleFieldChange}
            value={eventTypes}
          />
        </Col>
        {/* TODO: clean this up when we update the utilityManager package? */}
        {attributesOptions?.[departmentName?.toLowerCase()]?.length >= 1 && (
          <Col size={2}>
            <FormCheckbox
              name="attributes"
              title="ATTRIBUTES"
              required
              options={attributesOptions?.[departmentName?.toLowerCase()]}
              value={attributes}
              border={false}
              onChange={handleFieldChange}
            />
          </Col>
        )}
      </Row>
    </Container>
  );
};

export default EditAgentEventTypes;
