import styled from 'styled-components';
import { InfoCircle } from '@styled-icons/boxicons-solid/InfoCircle';
import { Phone } from '@styled-icons/boxicons-solid/Phone';
import { Envelope } from '@styled-icons/boxicons-solid/Envelope';
import { Home } from '@styled-icons/boxicons-solid/Home';

export const Subtitle = styled.div`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 300px;
  text-align: left;
  font-size: 14px;
  color: grey;
  max-width: ${(props) => (props.fullWidth ? '100%' : '300px')};
`;

export const CardContainer = styled.div`
  border-radius: 2px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
  padding: 24px;
  background-color: ${({ theme }) => theme.secondary[100]};
  min-height: 200px;
  height: 100%;
  overflow: auto;
`;

export const InformationIcon = styled(InfoCircle)`
  color: ${({ theme }) => theme.primary[400]};
  height: 20px;
  margin: 0;
`;

export const FlexColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${(props) => props.gap || '8px'};
`;

export const FlexRow = styled.div`
  display: flex;
  justify-content: ${(props) => props.justifyContent || 'flex-start'};
  align-items: center;
  gap: ${(props) => props.gap || '8px'};
  cursor: ${(props) => props.cursor || 'unset'};
`;

export const HomeIcon = styled(Home)`
  color: ${({ color, theme }) => color || theme.primary[400]};
  height: 20px;
  margin-left: 0;
  margin-right: 8px;
`;

export const PhoneIcon = styled(Phone)`
  color: ${({ color, theme }) => color || theme.primary[400]};
  height: 20px;
  margin-left: 0;
  margin-right: 8px;
`;

export const MailIcon = styled(Envelope)`
  color: ${({ color, theme }) => color || theme.primary[400]};
  height: 20px;
  margin-left: 0;
  margin-right: 8px;
`;
