import styled from 'styled-components';

export const MapContainer = styled.div`
  width: 100%;
  height: 600px;
  border: 1px solid ${({ theme }) => theme.colors.calendarBorder};
  border-radius: 8px;
  position: relative;
  background-color: ${({ theme }) => theme.secondary[100]};
`;

export const MapControlsContainer = styled.div`
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  max-height: 580px;
  display: flex;
  flex-direction: column;
`;

export const FilterContainer = styled.div`
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  min-height: 0;
  padding-right: 4px;
`;

export const ScrollableAgentList = styled.div`
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding-right: 4px;

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.secondary[300]};
    border-radius: 3px;

    &:hover {
      background: ${({ theme }) => theme.secondary[400]};
    }
  }
`;

export const SearchBarWrapper = styled.div`
  width: 100%;
  margin-bottom: 8px;

  /* Override SearchBar styling */
  & > div {
    width: 100% !important;
    margin-left: 0 !important;
    border: none !important;
  }

  & input {
    width: 100% !important;
    border: none !important;
    outline: none !important;
    background: ${({ theme }) => theme.secondary[50]} !important;
    padding: 6px 8px !important;

    &:focus {
      outline: none !important;
      border: none !important;
      background: ${({ theme }) => theme.secondary[100]} !important;
    }
  }
`;

export const FilterTitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.formText};
`;

export const FilterActions = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
`;

export const FilterButton = styled.button`
  padding: 4px 8px;
  font-size: 11px;
  border: 1px solid ${({ theme }) => theme.colors.calendarBorder};
  background: ${({ theme }) => theme.secondary[100]};
  color: ${({ theme }) => theme.colors.formText};
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.secondary[200]};
  }
`;

export const HESFilterItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background-color: ${({ theme }) => theme.secondary[200]};
  }
`;

export const ColorIndicator = styled.div`
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  background-color: ${({ color }) => color};
  border: 2px solid ${({ active, theme }) => (active ? theme.colors.primary : 'transparent')};
`;

export const HESName = styled.span`
  font-size: 12px;
  color: ${({ theme }) => theme.colors.formText};
  opacity: ${({ active }) => (active ? 1 : 0.6)};
`;

export const MapStats = styled.div`
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid ${({ theme }) => theme.colors.calendarBorder};
  font-size: 11px;
  color: ${({ theme }) => theme.colors.formText};
  flex-shrink: 0;
`;

export const MapControls = styled.div`
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid ${({ theme }) => theme.colors.calendarBorder};
  flex-shrink: 0;
`;

export const ControlButton = styled.button`
  width: 100%;
  padding: 6px 8px;
  margin-bottom: 4px;
  font-size: 11px;
  border: 1px solid ${({ theme }) => theme.colors.calendarBorder};
  background: ${({ theme }) => theme.secondary[100]};
  color: ${({ theme }) => theme.colors.formText};
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: ${({ theme }) => theme.secondary[200]};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

export const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  border-radius: 8px;
`;

export const ScheduleCardContainer = styled.div`
  margin-top: 6px;
  margin-bottom: 6px;
  background: ${({ theme }) => theme.secondary[50]};
  border-radius: 4px;
  padding: 6px;
  border: 1px solid ${({ theme }) => theme.secondary[200]};
  max-height: 100px;
  overflow-y: auto;

  /* Custom scrollbar for route stops */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.secondary[300]};
    border-radius: 2px;
  }
`;

export const ScheduleCardTitle = styled.h3`
  margin: 0 0 4px 0;
  font-size: 11px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.formText};
`;

export const RouteStopsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const RouteStop = styled.div`
  display: flex;
  gap: 6px;
  padding: 3px;
  border-radius: 3px;
  background: white;
`;

export const StopNumber = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  min-width: 20px;
  border-radius: 50%;
  background: ${({ color }) => color};
  color: white;
  font-weight: 600;
  font-size: 10px;
`;

export const StopDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1px;
  flex: 1;
`;

export const StopCustomerName = styled.div`
  font-size: 11px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.formText};
`;

export const StopAddress = styled.div`
  font-size: 10px;
  color: ${({ theme }) => theme.colors.formText};
`;

export const StopTime = styled.div`
  font-size: 10px;
  color: ${({ theme }) => theme.colors.formText};
`;

export const StopEventType = styled.div`
  font-size: 9px;
  color: ${({ theme }) => theme.secondary[600]};
  font-weight: 500;
`;
