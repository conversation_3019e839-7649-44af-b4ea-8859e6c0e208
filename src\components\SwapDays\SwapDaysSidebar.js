import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import DatePicker from 'react-datepicker';
import moment from 'moment';

import { getDaysInCurrentMonth, isDayWeekend } from '@utils/dateUtils';
import EventSidebarBase from '@components/EventSidebar/EventSidebarBase';
import { EventSidebarFooter, EventSidebarHeader } from '@components/EventSidebar';
import { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import { Col, Row } from '@components/global/Form';
import { Header } from '@components/global';
import { PrimaryButton, SecondaryButton } from '@components/global/Buttons';
import { UsersManager } from '@utils/APIManager';
import {
  Wrapper,
  CalendarWrapper,
  TextWrapper,
  DateCircle,
  InfoWrapper,
} from './SwapDaysSidebar.styles';

const SwapDaysSidebar = ({ sidebarOpen, closeSidebar, oid, lastUpdated }) => {
  const [weekdaysInMonth, setWeekdaysInMonth] = useState([]);
  const [weekendsInMonth, setWeekendsInMonth] = useState([]);
  const [weekendDaysOff, setWeekendDaysOff] = useState([]);
  const [weekdaysOff, setWeekdaysOff] = useState([]);
  const [daysInMonth, setDaysInMonth] = useState([]);
  const [selectedMonth, setSelectedMonth] = useState(new Date());

  const numberOfWeekendDaysOn = weekendsInMonth.length - weekendDaysOff.length;
  const numberOfWeekdaysOff = weekdaysOff.length;

  const swapsSelected = numberOfWeekendDaysOn === numberOfWeekdaysOff && numberOfWeekendDaysOn >= 2;

  useEffect(() => {
    const monthToUpdate = moment(lastUpdated || new Date()).add(2, 'month');
    const daysInMonth = getDaysInCurrentMonth(monthToUpdate);

    setSelectedMonth(monthToUpdate.toDate());

    setDaysInMonth(daysInMonth);

    const weekendDays = [];
    const weekdays = [];
    daysInMonth.forEach((day) => {
      if (isDayWeekend(moment(day))) weekendDays.push(day);
      else weekdays.push(day);
    });

    setWeekdaysOff([]);
    setWeekdaysInMonth(weekdays);
    setWeekendsInMonth(weekendDays);
    setWeekendDaysOff(weekendDays);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sidebarOpen]);

  const addRemoveDate = (date) => {
    const isWeekend = isDayWeekend(moment(date));
    const daysOff = isWeekend ? weekendDaysOff : weekdaysOff;
    const setDaysOff = isWeekend ? setWeekendDaysOff : setWeekdaysOff;

    const index = daysOff.findIndex((day) => day.toDateString() === date.toDateString());

    if (index === -1) {
      setDaysOff([...daysOff, date]);
    } else {
      const daysOffArray = [...daysOff];
      daysOffArray.splice(index, 1);
      setDaysOff(daysOffArray);
    }
  };

  const handleSave = async () => {
    const dates = daysInMonth
      .filter(
        (day) =>
          ![...weekendDaysOff, ...weekdaysOff]
            .map((date) => date.toDateString())
            .includes(day.toDateString()),
      )
      .map((day) => moment(day).format('MM/DD/YYYY'));
    await UsersManager.updateUserOverview({ oid, dates });
    closeSidebar();
  };

  // Don't allow choosing weekdays off if they haven't chosen their weekends on yet
  const excludeDates = weekendDaysOff?.length > weekendsInMonth.length - 2 ? weekdaysInMonth : [];

  const includeDates =
    // if they have already chosen their swaps
    swapsSelected
      ? // require selecting another day on before allowing more days off
        [...weekdaysOff, ...weekendDaysOff]
      : // otherwise allow selecting more days off
        [...daysInMonth];

  return (
    <EventSidebarBase show={sidebarOpen} closeSidebar={closeSidebar}>
      <EventSidebarHeader>
        <Row>
          <Col>
            <HeaderTitle>Select Swap Days</HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <Wrapper>
        <CalendarWrapper>
          <DatePicker
            onChange={(date) => addRemoveDate(date)}
            highlightDates={[...weekdaysOff, ...weekendDaysOff]}
            excludeDates={excludeDates}
            includeDates={includeDates}
            inline
            showDisabledMonthNavigation
            openToDate={selectedMonth}
          />
        </CalendarWrapper>
        <InfoWrapper>
          <Header h3>
            Please select at least two weekend days on before selecting weekdays off
          </Header>
          <TextWrapper>
            <DateCircle /> <span> = Days off</span>
          </TextWrapper>
        </InfoWrapper>
      </Wrapper>
      <EventSidebarFooter>
        {swapsSelected && (
          <PrimaryButton left onClick={handleSave}>
            Save
          </PrimaryButton>
        )}
        <SecondaryButton left onClick={() => closeSidebar()}>
          Close
        </SecondaryButton>
      </EventSidebarFooter>
    </EventSidebarBase>
  );
};

export default SwapDaysSidebar;

SwapDaysSidebar.propTypes = {
  sidebarOpen: PropTypes.bool.isRequired,
  closeSidebar: PropTypes.func.isRequired,
  oid: PropTypes.string.isRequired,
  lastUpdated: PropTypes.string.isRequired,
};
