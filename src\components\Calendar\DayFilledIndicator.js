import React from 'react';
import PropTypes from 'prop-types';
import CellCornerIndicator from './CellCornerIndicator';

const DayFilledIndicator = ({ dayFilled = null }) => {
  const status = dayFilled > 0.75 ? 'pass' : 'fail';

  return <CellCornerIndicator value={dayFilled} status={status} />;
};

DayFilledIndicator.propTypes = {
  dayFilled: PropTypes.oneOfType([PropTypes.bool, PropTypes.number]),
};

export default DayFilledIndicator;
