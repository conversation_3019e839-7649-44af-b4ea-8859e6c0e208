import { throwError, startLoading, stopLoading, displaySuccessMessage } from '@utils/EventEmitter';
import eventValidation from '@utils/eventValidation';
import axios from './utils/AxiosConfig';

const createPattern = async (params) => {
  if (!eventValidation.eventPatterns.create(params)) return false;

  const url = '/api/eventPatterns/createPattern';
  startLoading('Creating Pattern ...');
  try {
    const response = await axios.post(url, params);
    stopLoading();
    const { error } = response.data;
    // TODO: is this actually how we handle errors?
    if (error) {
      throwError({
        message: `Failed to create new pattern. Error: ${error.message}`,
        params: `Params passed: ${params}`,
      });
      return false;
    }
    displaySuccessMessage('Successfully created new pattern');
    return true;
  } catch (error) {
    stopLoading();
    throwError(error);
  }
  return false;
};

const getPatterns = async () => {
  const url = '/api/eventPatterns/getPatterns';
  startLoading('Getting Patterns ...');
  try {
    const response = await axios.get(url);
    stopLoading();
    const { error } = response.data;
    // TODO: is this actually how we handle errors?
    if (error) {
      throwError({
        message: `Failed to get patterns. Error: ${error.message}`,
        params: 'Please send a <NAME_EMAIL>',
      });
      return false;
    }
    return response.data;
  } catch (error) {
    stopLoading();
    throwError(error);
  }
  return false;
};

const updatePattern = async (params) => {
  if (!eventValidation.eventPatterns.update(params)) return false;

  const url = '/api/eventPatterns/updatePattern';
  startLoading('Updating Pattern ...');
  try {
    const response = await axios.post(url, params);
    stopLoading();
    const { error } = response.data;
    // TODO: is this actually how we handle errors?
    if (error) {
      throwError({
        message: `Failed to update pattern. Error: ${error.message}`,
        params: `Params passed: ${params}`,
      });
      return false;
    }
    displaySuccessMessage('Successfully updated pattern');
    return true;
  } catch (error) {
    stopLoading();
    throwError(error);
  }
  return false;
};

const deletePattern = async (params) => {
  const url = '/api/eventPatterns/deletePattern';
  startLoading('Deleting Pattern ...');
  try {
    const response = await axios.post(url, params);
    stopLoading();
    const { error } = response.data;
    // TODO: is this actually how we handle errors?
    if (error) {
      throwError({
        message: `Failed to delete patterns. Error: ${error.message}`,
        params: `Params passed: ${params}`,
      });
      return false;
    }
    displaySuccessMessage('Successfully deleted pattern');
    return true;
  } catch (error) {
    stopLoading();
    throwError(error);
  }
  return false;
};

export default { createPattern, getPatterns, updatePattern, deletePattern };
