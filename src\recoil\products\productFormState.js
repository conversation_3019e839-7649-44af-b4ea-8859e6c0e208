import { atom, selector, DefaultValue } from 'recoil';

const defaultValues = {
  id: '',
  quantity: 0,
  unitPrice: 0,
  manufacturer: '',
  series: '',
  systemSize: '',
  indoorModelNumber: '',
  outdoorModelNumbers: '',
  numberOfZonesHeads: '',
  description: '',
  opportunityLineItemId: '',
};
const productFormState = {};

Object.keys(defaultValues).forEach((fieldName) => {
  productFormState[fieldName] = atom({
    key: `productFormState-${fieldName}Atom`,
    default: defaultValues[fieldName],
  });
});

const productSelector = selector({
  key: 'productFormState',
  get: ({ get }) => {
    const state = {};
    const propertyNames = Object.keys(productFormState);

    propertyNames.forEach((propertyName) => {
      state[propertyName] = get(productFormState[propertyName]);
    });

    return state;
  },
  set: ({ set, reset }, newValue) => {
    if (newValue instanceof DefaultValue) {
      Object.keys(productFormState).forEach((propertyName) => {
        reset(productFormState[propertyName]);
      });
    }
    const propertyNames = Object.keys(newValue);
    propertyNames.forEach((propertyName) => {
      if (!productFormState[propertyName]) return;
      set(productFormState[propertyName], newValue[propertyName]);
    });
  },
});

export { productSelector, productFormState };
