import { addKeyNameToObject } from '@components/DataIntakeForm/dataIntakeFormHelpers';
import {
  formSchema,
  flatFormSchema,
  offlineWRFormSchema,
  flatOfflineWRFormSchema,
} from './formSchema';
import { sectionFields } from './sections';

const {
  customerAuditorInfo,
  hvacInfo,
  roadBlocksInfo,
  proposalTerms,
  incentiveEligibilityAndAmounts,
  massSaveWorkAmounts,
  flooringRemovalIncentive,
  basInformation,
  inHouseChecks,
  returnVisitResulting,
  dhwQuoting,
  goodSpecBadSpec,
  icRatedLightLocation,
  postHea,
  weatherization,
} = formSchema;

const {
  customerAuditorInfoOfflineWr,
  offlineWRIncentiveEligibilityAndAmounts,
  offlineRoadBlocksInfo,
} = offlineWRFormSchema;

const getFields = (schema) => Object.values(schema);

const workReceiptMap = {
  customerAuditorInfo: {
    fields: getFields(customerAuditorInfo),
  },
  hvacInfo: {
    fields: getFields(hvacInfo),
  },
  roadBlocksInfo: {
    fields: [
      ...getFields(roadBlocksInfo),
      sectionFields.asbestosInfo,
      sectionFields.cstFailure,
      sectionFields.electrical,
      sectionFields.moisture,
      sectionFields.crawlSpace,
      sectionFields.mold,
      sectionFields.other,
      sectionFields.pestInfestationRoofLeak,
      sectionFields.structural,
      sectionFields.wallsWorkDisclosures,
    ],
  },
  proposalTerms: {
    fields: getFields(proposalTerms),
  },
  incentiveEligibilityAndAmounts: {
    fields: getFields(incentiveEligibilityAndAmounts),
  },
  massSaveWorkAmounts: {
    fields: getFields(massSaveWorkAmounts),
  },
  flooringRemovalIncentive: {
    fields: getFields(flooringRemovalIncentive),
  },
  icRatedLightLocation: {
    fields: getFields(icRatedLightLocation),
  },
  basInformation: {
    fields: getFields(basInformation),
  },
  inHouseChecks: {
    fields: getFields(inHouseChecks),
  },
  dhwQuoting: {
    fields: getFields(dhwQuoting),
  },
  postHea: {
    fields: getFields(postHea),
  },
  goodSpecBadSpec: {
    fields: getFields(goodSpecBadSpec),
  },
  returnVisitResulting: {
    fields: getFields(returnVisitResulting),
  },
  additionalProjectInformation: {
    fields: [sectionFields.weatherization, sectionFields.hvac],
  },
  weatherization: {
    fields: getFields(weatherization),
  },
};

const offlineWorkReceiptMap = {
  customerAuditorInfo: {
    fields: getFields(customerAuditorInfoOfflineWr),
  },
  hvacInfo: {
    fields: getFields(hvacInfo),
  },
  roadBlocksInfo: {
    fields: [
      ...getFields(offlineRoadBlocksInfo),
      sectionFields.asbestosInfo,
      sectionFields.cstFailure,
      sectionFields.electrical,
      sectionFields.moisture,
      sectionFields.crawlSpace,
      sectionFields.mold,
      sectionFields.pestInfestationRoofLeak,
      sectionFields.other,
      sectionFields.structural,
    ],
  },
  incentiveEligibilityAndAmounts: {
    fields: getFields(offlineWRIncentiveEligibilityAndAmounts),
  },
  basInformation: {
    fields: getFields(basInformation),
  },
  postHea: {
    fields: getFields(postHea),
  },
  goodSpecBadSpec: {
    fields: getFields(goodSpecBadSpec),
  },
  returnVisitResulting: {
    fields: getFields(returnVisitResulting),
  },
  readOnlyFields: {
    fields: [sectionFields.weatherization, sectionFields.hvac],
  },
};

const workReceiptFields = addKeyNameToObject(flatFormSchema);
const offlineWorkReceiptFields = addKeyNameToObject(flatOfflineWRFormSchema);

export {
  workReceiptMap,
  workReceiptFields,
  offlineWorkReceiptMap,
  offlineWorkReceiptFields,
  getFields,
};
