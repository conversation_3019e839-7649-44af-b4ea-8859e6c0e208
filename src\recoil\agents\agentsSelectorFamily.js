import { selectorFamily } from 'recoil';
import { AgentsManager } from '@utils/APIManager';
import { refreshAdminListState } from '@recoil/admin';

const agentsSelectorFamily = selectorFamily({
  key: 'agentsSelectorFamily',
  get: (params) => async ({ get }) => {
    // counter used to force async refresh selector
    get(refreshAdminListState);

    // TODO: update backend functions to use "agent" term instead of "crew"
    const { crews: agents } = await AgentsManager.getCrewsByStateAndDepartment(params);
    return agents;
  },
});

export default agentsSelectorFamily;
