import React, { useState, useRef, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState } from 'recoil';
import styled from 'styled-components';
import { Search } from '@styled-icons/boxicons-regular/Search';
import { ChevronDown } from '@styled-icons/boxicons-regular/ChevronDown';
import { searchAdminState } from '@recoil/admin';

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  margin-bottom: 8px;
`;

const SearchInputWrapper = styled.div`
  display: flex;
  align-items: center;
  border: solid 1px ${({ theme }) => theme.secondary[200]};
  border-radius: 5px;
  background: white;
  padding: 8px 12px;
  cursor: text;

  &:focus-within {
    border-color: ${({ theme }) => theme.primary[400]};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.primary[100]};
  }
`;

const SearchIcon = styled(Search)`
  color: ${({ theme }) => theme.secondary[500]};
  margin-right: 8px;
  flex-shrink: 0;
`;

const SearchInput = styled.input`
  border: none;
  outline: none;
  flex: 1;
  color: ${({ theme }) => theme.secondary[700]};
  font-size: 14px;
  background: transparent;

  &::placeholder {
    color: ${({ theme }) => theme.secondary[400]};
  }
`;

const DropdownToggle = styled.button`
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.secondary[500]};
  margin-left: 8px;

  &:hover {
    color: ${({ theme }) => theme.primary[500]};
  }
`;

const DropdownList = styled.div`
  position: absolute;
  top: ${({ dropUp }) => (dropUp ? 'auto' : '100%')};
  bottom: ${({ dropUp }) => (dropUp ? '100%' : 'auto')};
  left: 0;
  right: 0;
  background: white;
  border: solid 1px ${({ theme }) => theme.secondary[200]};
  border-radius: 5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: ${({ maxHeight }) => maxHeight || '180px'};
  overflow-y: auto;
  z-index: 1001;
  margin-top: ${({ dropUp }) => (dropUp ? '0' : '4px')};
  margin-bottom: ${({ dropUp }) => (dropUp ? '4px' : '0')};

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.secondary[100]};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.secondary[300]};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${({ theme }) => theme.secondary[400]};
  }
`;

const DropdownItem = styled.div`
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid ${({ theme }) => theme.secondary[100]};

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: ${({ theme }) => theme.primary[50]};
  }
`;

const CheckboxWrapper = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
`;

const AgentDetailsWrapper = styled.div`
  flex: 1;
  margin-left: 12px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const AgentCountWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const AgentColorIndicator = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${({ color }) => color};
  flex-shrink: 0;
`;

const AgentName = styled.div`
  font-weight: 500;
  color: ${({ theme }) => theme.secondary[700]};
  font-size: 14px;
`;

const AgentCount = styled.div`
  font-size: 12px;
  color: ${({ theme }) => theme.secondary[500]};
  margin-top: 2px;
`;

const NoResults = styled.div`
  padding: 16px;
  text-align: center;
  color: ${({ theme }) => theme.secondary[500]};
  font-style: italic;
`;

const Checkbox = styled.input`
  width: 26px;
  height: 26px;
  cursor: pointer;
  flex-shrink: 0;
  accent-color: ${({ theme }) => theme.primary[500]};
`;

const HESAgentSearchBox = ({
  hesAgents = [],
  placeholder = 'Search HES agents...',
  onAgentSelect = null,
  activeFilters = new Set(),
}) => {
  const [searchTerm, setSearchTerm] = useRecoilState(searchAdminState);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [dropdownPosition, setDropdownPosition] = useState({ dropUp: false, maxHeight: '180px' });
  const searchRef = useRef(null);
  const dropdownRef = useRef(null);

  // Filter agents and recalculate dropdown position when search term, hesAgents, or dropdown state changes
  useEffect(() => {
    let filtered;
    if (!searchTerm) {
      filtered = hesAgents.filter((agent) => agent.appointmentCount > 0);
    } else {
      filtered = hesAgents.filter((agent) => {
        if (agent.appointmentCount === 0) return false;
        const searchableText = [
          agent.name,
          agent.displayName,
          agent.firstname,
          agent.lastname,
          `${agent.firstname} ${agent.lastname}`.trim(),
        ]
          .filter(Boolean)
          .join(' ')
          .toLowerCase();
        return searchableText.includes(searchTerm.toLowerCase());
      });
    }
    setFilteredAgents(filtered);
    if (isDropdownOpen) {
      calculateDropdownPosition();
    }
  }, [searchTerm, hesAgents, isDropdownOpen, calculateDropdownPosition]);

  // Toggle dropdown
  const toggleDropdown = () => {
    if (!isDropdownOpen) {
      calculateDropdownPosition();
    }
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Calculate dropdown position based on available space
  const calculateDropdownPosition = useCallback(() => {
    if (!searchRef.current) return;

    const searchRect = searchRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - searchRect.bottom;
    const spaceAbove = searchRect.top;

    // Find the parent container (MapControlsContainer) to respect its boundaries
    const mapContainer = searchRef.current.closest('[class*="MapControlsContainer"]');
    let containerSpaceBelow = spaceBelow;
    let containerSpaceAbove = spaceAbove;

    if (mapContainer) {
      const containerRect = mapContainer.getBoundingClientRect();
      containerSpaceBelow = containerRect.bottom - searchRect.bottom;
      containerSpaceAbove = searchRect.top - containerRect.top;
    }

    const minDropdownHeight = 120;
    const maxDropdownHeight = 180;
    const itemHeight = 56; // Approximate height per item
    const neededHeight = Math.min(filteredAgents.length * itemHeight + 20, maxDropdownHeight);

    let dropUp = false;
    let maxHeight = maxDropdownHeight;

    // Determine if we should drop up or down
    if (containerSpaceBelow < neededHeight && containerSpaceAbove > containerSpaceBelow) {
      dropUp = true;
      maxHeight = Math.min(containerSpaceAbove - 10, maxDropdownHeight);
    } else {
      dropUp = false;
      maxHeight = Math.min(containerSpaceBelow - 10, maxDropdownHeight);
    }

    // Ensure minimum height
    maxHeight = Math.max(maxHeight, minDropdownHeight);

    setDropdownPosition({ dropUp, maxHeight: `${maxHeight}px` });
  }, [filteredAgents.length]);

  // Handle agent selection from dropdown
  const handleAgentSelect = (agent) => {
    if (onAgentSelect) {
      onAgentSelect(agent);
    }
  };

  // Handle input focus
  const handleInputFocus = () => {
    calculateDropdownPosition();
    setIsDropdownOpen(true);
  };

  // Handle input change
  const handleInputChange = (e) => {
    setSearchTerm(e.target.value);
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <SearchContainer ref={searchRef}>
      <SearchInputWrapper>
        <SearchIcon height={18} width={18} />
        <SearchInput
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
        />
        <DropdownToggle onClick={toggleDropdown} type="button">
          <ChevronDown
            height={16}
            width={16}
            style={{
              transform: isDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease',
            }}
          />
        </DropdownToggle>
      </SearchInputWrapper>

      {isDropdownOpen && (
        <DropdownList
          ref={dropdownRef}
          dropUp={dropdownPosition.dropUp}
          maxHeight={dropdownPosition.maxHeight}
        >
          {filteredAgents.length > 0 ? (
            filteredAgents.map((agent) => (
              <DropdownItem key={agent.oid}>
                <CheckboxWrapper onClick={() => handleAgentSelect(agent)}>
                  <Checkbox type="checkbox" checked={activeFilters.has(agent.oid)} readOnly />
                </CheckboxWrapper>
                <AgentDetailsWrapper onClick={() => handleAgentSelect(agent)}>
                  <AgentName>{agent.name}</AgentName>
                  <AgentCountWrapper>
                    <AgentCount>{agent.appointmentCount} appointments</AgentCount>
                    <AgentColorIndicator color={agent.color} />
                  </AgentCountWrapper>
                </AgentDetailsWrapper>
              </DropdownItem>
            ))
          ) : (
            <NoResults>{searchTerm ? 'No agents found' : 'No agents available'}</NoResults>
          )}
        </DropdownList>
      )}
    </SearchContainer>
  );
};

HESAgentSearchBox.propTypes = {
  hesAgents: PropTypes.arrayOf(
    PropTypes.shape({
      oid: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      color: PropTypes.string.isRequired,
      appointmentCount: PropTypes.number.isRequired,
    }),
  ),
  placeholder: PropTypes.string,
  onAgentSelect: PropTypes.func,
  activeFilters: PropTypes.instanceOf(Set),
};

export default HESAgentSearchBox;
