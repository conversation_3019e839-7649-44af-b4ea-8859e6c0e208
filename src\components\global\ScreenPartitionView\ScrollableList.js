import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import AddListItemButton from '@components/global/ScreenPartitionView/AddListItemButton';
import SearchBar from '@components/global/SearchBar';

const ListContainer = styled.div`
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const StyledScrollableList = styled.div`
  height: 100%;
  flex: 1 1 auto;
  overflow: hidden;
  overflow-y: auto;
`;

const ListHeaderContainer = styled.div`
  height: 50px;
  display: flex;
  flex: 0 1 auto;
  margin-top: 10px;
  margin-bottom: 10px;
`;

const ScrollableList = ({
  children = null,
  allowSearch = true,
  handleAddItemClick = null,
  listItemName = 'Item',
}) => {
  return (
    <ListContainer>
      {allowSearch && (
        <ListHeaderContainer>
          <SearchBar showBorder={false} placeholder="Search List" />
        </ListHeaderContainer>
      )}
      {handleAddItemClick && (
        <AddListItemButton text={`Add ${listItemName}`} onClick={handleAddItemClick} />
      )}
      <StyledScrollableList>{children}</StyledScrollableList>
    </ListContainer>
  );
};

ScrollableList.propTypes = {
  children: PropTypes.node,
  allowSearch: PropTypes.bool,
  handleAddItemClick: PropTypes.func,
  listItemName: PropTypes.string, // "User", "Crew", "Agent", "Role" etc.
};

export default ScrollableList;
