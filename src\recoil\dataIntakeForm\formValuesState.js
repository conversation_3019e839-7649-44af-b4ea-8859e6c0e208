import { selector } from 'recoil';

import formRecoilState from './formRecoilState';

const formValuesState = selector({
  key: 'formValuesSelector',
  get: ({ get }) => {
    const stateObject = get(formRecoilState);
    if (!stateObject) return {};
    return get(stateObject);
  },
  set: ({ set, get }, newValue) => {
    const stateObject = get(formRecoilState);
    set(stateObject, newValue);
  },
});

export default formValuesState;
