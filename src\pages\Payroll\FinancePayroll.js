import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2/dist/sweetalert2';
import styled from 'styled-components';
import { Header } from '@components/global';
import { ExclamationTriangleFill } from '@styled-icons/bootstrap/ExclamationTriangleFill';
import { XCircle } from '@styled-icons/boxicons-regular/XCircle';

import { PayrollManager } from '@utils/APIManager';
import PayrollModal from './PayrollModal';

// ***** NEED TO FIX MOBILE DISPLAY *****

const StyledDivContainer = styled.div``;
const StyledContainerTD = styled.td``;
const StyledContainerTR = styled.tr``;
const StyledContainerTH = styled.th``;
const StyledContainerTHEAD = styled.thead``;
const StyledContainerTBODY = styled.tbody``;
const StyledTableContainer = styled.table``;
const StyledContainerSpan = styled.span``;
const StyledContainerOption = styled.option``;
const WarningTriangle = styled(ExclamationTriangleFill)``;
const ErrorCircle = styled(XCircle)``;
const FileUploadLabel = styled.label`
  background: #008afc;
  text-align: center;
  padding-top: 8px;
  cursor: pointer;
  border: none;
  height: 32px;
  width: 117px;
  font-style: normal;
  font-weight: bold;
  font-size: 12px;
  line-height: 14px;
  border-radius: 4px;
  /* identical to box height */
  color: #ffffff;
`;
const StyledSpanUnderline = styled.span`
  text-decoration-line: underline;
  font-weight: bold;
`;
const StyledRemoveButton = styled.button`
  font-style: normal;
  font-size: 16px;
  line-height: 19px;
  color: #565656;
  background: transparent;
  border: none;
  &.extra-style {
    text-decoration-line: underline;
    font-weight: bold;
    color: #002e54;
  }
`;
const StyledTD = styled.td`
  text-align: end;
`;
const StyledPassQcCheck = styled.div`
  font-weight: bold;
  color: ${({ theme }) => theme.colors.green};
`;
const StyledTR = styled.tr`
  line-height: 35px;
`;
const StyledBoldTD = styled.td`
  font-weight: bold;
  &.color-red {
    color: ${({ theme }) => theme.colors.red};
  }
`;
const StyledSpan = styled.span`
  padding-right: 5px;
  &.color-red {
    color: ${({ theme }) => theme.colors.red};
  }
  &.color-yellow {
    color: #989020;
  }
`;
const StyledMainButton = styled.button`
  border: none;
  height: 32px;
  width: 117px;
  font-style: normal;
  font-weight: bold;
  font-size: 12px;
  line-height: 14px;
  border-radius: 4px;
  /* identical to box height */
  color: #ffffff;
  &.quality-control {
    background: #788f3f;
  }
  &.combine-files {
    background-color: #707172;
  }
`;
const StyledDateFilterAndButtons = styled.div`
  width: 75%;
  display: flex;
  justify-content: space-between;
`;
const StyledSelect = styled.select`
  width: 20%;
`;
const StyledUploadButton = styled.button`
  background: linear-gradient(to bottom, #1a0ef4 5%, #0f0f9b 100%);
  background-color: #1a0ef4;
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 7px;
  color: #ffffff;
  font-family: Arial;
  font-weight: bold;
  padding: 2px 19px;
  text-decoration: none;
  text-shadow: 0px 1px 0px #810e05;
  border: none;
  &:hover {
    background: linear-gradient(to bottom, #0f0f9b 5%, #1a0ef4 100%);
    background-color: #0f0f9b;
  }
  &:active {
    position: relative;
    top: 1px;
  }
`;
const StyledUploadLabel = styled.label`
  background: linear-gradient(to bottom, #1a0ef4 5%, #0f0f9b 100%);
  background-color: #1a0ef4;
  border: 1px solid #ccc;
  display: inline-block;
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 7px;
  color: #ffffff;
  font-family: Arial, sans-serif;
  font-weight: bold;
  padding: 2px 19px;
  text-decoration: none;
  text-shadow: 0px 1px 0px #810e05;
  border: none;
  &:hover {
    background: linear-gradient(to bottom, #0f0f9b 5%, #1a0ef4 100%);
    background-color: #0f0f9b;
  }
  &:active {
    position: relative;
    top: 1px;
  }
`;
const HiddenFileInput = styled.input.attrs({ type: 'file' })`
  display: none;
`;

const requiredDocumentsArr = [
  'Training',
  'Marketing',
  'HVAC_Install',
  'HVAC_Sales',
  'HAM',
  'CIA',
  'HES',
  'HCS',
  'HR',
  'Wx_Cape',
  'Wx_Metro_West',
  'Wx_North_Shore',
  'Wx_South_Shore_A',
  'Wx_South_Shore_B',
  'Wx_WeMA',
  'Wx_Framingham',
  'Wx_Support_Staff',
  'Wx_PWX',
];

const FinancePayroll = () => {
  const {
    getUploadedDatesFromS3,
    getUploadedDocumentsFromS3,
    runQcCheckOnUploadedFiles,
    deleteUploadedDocumentFromS3,
    uploadDocumentToS3,
    uploadMulitpleDocumentsToS3,
    getSignedUrl,
    runCombineFilesLambda,
  } = PayrollManager;
  const [viewingWeek, setViewingWeek] = useState(false);
  const [dateOptions, setDateOptions] = useState([]);
  const [uploadedDocs, setUploadedDocs] = useState([]);
  const [combinedDoc, setCombinedDoc] = useState([]);
  const [requiredDocs, setRequiredDocs] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [showModalKey, setShowModalKey] = useState(null);

  useEffect(() => {
    loadDateOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (viewingWeek) getUploadedDocuments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [viewingWeek]);

  // Get all folders from Payroll S3 bucket. The folder names are date we will use as options for date drop down
  const loadDateOptions = async () => {
    // Monday of current week
    const currentDate = createWeekStart();
    // Get folder names(which are dates) from Payroll S3 bucket
    const uploadedWeeks = await getUploadedDatesFromS3();
    if (!uploadedWeeks.includes(currentDate)) uploadedWeeks.unshift(currentDate);
    // Set values for date picker drop down
    setDateOptions(uploadedWeeks);
    // Set default value as Monday of this week
    setViewingWeek(currentDate);
  };

  // Remove file button. We don't acutally delte files. We rename when deleted_fileName
  const handleRemove = async (docName) => {
    const { value } = await Swal.fire({
      title: 'Do you want to delete this file?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Confirm',
    });
    if (value) {
      // Rename file deleted_fileName
      await deleteUploadedDocumentFromS3(docName, viewingWeek);
      // Reset state since file was deleted
      await getUploadedDocuments();
    }
  };

  // Upload while renaming file
  const handleUpload = async (event) => {
    const { name, files } = event.target;
    if (!files[0]?.name?.toLowerCase().includes(name?.toLowerCase())) {
      const { value: confirmed } = await Swal.fire({
        icon: 'warning',
        title: `The name of the file you are trying to upload does not match the name displayed for this file. Are you sure you want to upload this file? The file you are uploading will be renamed to ${name}`,
        confirmButtonText: 'Yes',
        showCancelButton: true,
        cancelButtonText: 'No',
      });
      if (!confirmed) return;
    }
    const data = new FormData();
    data.append('file', files[0]);
    await uploadDocumentToS3(data, name, viewingWeek);
    // Reset state since file was uploaded
    await getUploadedDocuments();
  };

  const handleMultiUpload = async (event) => {
    const { target } = event;
    const files = Array.from(event.target.files);
    await uploadMulitpleDocumentsToS3(files, viewingWeek);
    target.value = null;
    // Reset state since file was uploaded
    await getUploadedDocuments();
  };

  const handleDownload = async (fileName) => {
    const combinedFileUrl = await getSignedUrl(viewingWeek, fileName);
    return window.open(combinedFileUrl, '_blank');
  };

  // Get uploaded documents for viewingWeek state value from S3
  const getUploadedDocuments = async () => {
    // Get uploaded documents from S3
    const { uploadedDocs, combinedDoc } = await getUploadedDocumentsFromS3(viewingWeek);
    // Filter uploaded documents against required documents list
    const filteredRequiredDocs = filterRequiredDocuments(uploadedDocs);
    // Set documents that still need to be uploaded
    setRequiredDocs(filteredRequiredDocs);
    setCombinedDoc(combinedDoc);
    // Set documents already uploaded S3
    setUploadedDocs(uploadedDocs);
  };

  // Filter uploaded documents against required documents(requiredDocumentsArr)
  const filterRequiredDocuments = (uploadedDocs) => {
    const notUploadedRequiredDocs = [];
    // requiredDocuemntsArr will have a length of 15 always unless more documents are added to the array on line 9
    for (let k = 0; k < requiredDocumentsArr.length; k++) {
      const reqDoc = requiredDocumentsArr[k];
      let isUploaded = false;
      // uploadedDocs at most will have a length of 15 hopefully unless more documents are added to the array on line 9 or random documents are uploaded
      for (let i = 0; i < uploadedDocs.length; i++) {
        const { fileName } = uploadedDocs[i];
        if (fileName?.toLowerCase().startsWith(reqDoc?.toLowerCase())) {
          isUploaded = true;
          break;
        }
      }
      if (!isUploaded) notUploadedRequiredDocs.push(reqDoc);
    }
    return notUploadedRequiredDocs;
  };

  // Handle Date dropdown
  const handleDateFilter = (event) => {
    setViewingWeek(event.target.value);
  };

  // Invoke combine files lambda
  const runCombineFiles = async () => {
    if (requiredDocs.length > 0) {
      const { value: confirmed } = await Swal.fire({
        icon: 'warning',
        title: `All required files have not been uploaded. Are you sure you want to merge? Missing Files : ${requiredDocs.join(
          ', ',
        )}`,
        confirmButtonText: 'Yes',
        showCancelButton: true,
        cancelButtonText: 'No',
      });
      if (!confirmed) return;
    }
    const combineSuccess = await runCombineFilesLambda(viewingWeek, uploadedDocs);
    if (combineSuccess) await getUploadedDocuments();
  };

  // Set QC results on uploaded documents
  const parseResults = (qcResultsOnDocs) => {
    const uploadedDocsCopy = [...uploadedDocs];
    const newUploadedDocsState = [];
    uploadedDocsCopy.forEach((doc) => {
      const { fileName } = doc;
      // eslint-disable-next-line no-unused-expressions
      qcResultsOnDocs[fileName]
        ? newUploadedDocsState.push({ ...doc, ...qcResultsOnDocs[fileName] })
        : null;
    });
    setUploadedDocs(newUploadedDocsState);
  };

  // Invoke QC check lambda
  const runQcCheck = async () => {
    const qcResults = await runQcCheckOnUploadedFiles(viewingWeek);
    parseResults(qcResults);
  };

  // Create date that is the Monday of current week
  const createWeekStart = () => {
    // eslint-disable-next-line prefer-const
    let date = new Date();
    const currentWeekStart = date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1);
    // eslint-disable-next-line no-new
    new Date(date.setDate(currentWeekStart));
    const currentDateStr = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    return currentDateStr;
  };

  // Handle modal open and closing
  const handleModalOpenClose = (arrKey, bool) => {
    setShowModalKey(arrKey);
    setShowModal(bool);
  };

  const renderCombinedDoc = () => {
    const docArr = [];
    for (let k = 0; k < combinedDoc.length; k++) {
      const { fileName, lastModified } = combinedDoc[k];
      docArr.push(
        <StyledTR key={fileName}>
          <StyledBoldTD>{fileName}</StyledBoldTD>
          <StyledContainerTD> {lastModified} </StyledContainerTD>
          <StyledContainerTD>
            {' '}
            <StyledUploadButton type="button" onClick={() => handleDownload(fileName)}>
              Download
            </StyledUploadButton>{' '}
          </StyledContainerTD>
          <StyledTD>
            <StyledRemoveButton type="button" onClick={() => handleRemove(fileName)}>
              X <StyledSpanUnderline>Remove</StyledSpanUnderline>
            </StyledRemoveButton>
          </StyledTD>
        </StyledTR>,
      );
    }
    return docArr;
  };

  // Not yet uploaded documents render
  const renderRequiredDocs = () => {
    const reqDocDisplay = [];
    for (let k = 0; k < requiredDocs.length; k++) {
      const reqDoc = requiredDocs[k];
      reqDocDisplay.push(
        <StyledContainerTR key={reqDoc}>
          <StyledBoldTD>{reqDoc}</StyledBoldTD>
          <StyledContainerTD />
          <StyledBoldTD className="color-red"> Not Uploaded </StyledBoldTD>
          <StyledTD>
            {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
            <StyledUploadLabel htmlFor={`file-upload-${reqDoc}`}>
              <i className="fa fa-cloud-upload" /> Upload
            </StyledUploadLabel>
            <HiddenFileInput
              id={`file-upload-${reqDoc}`}
              type="file"
              name={reqDoc}
              onChange={handleUpload}
            />
          </StyledTD>
        </StyledContainerTR>,
      );
    }
    return reqDocDisplay;
  };

  // Uploaded documents render
  const renderUploadedDocs = () => {
    const docArr = [];
    for (let k = 0; k < uploadedDocs.length; k++) {
      const { fileName, lastModified, status } = uploadedDocs[k];
      const arrKey = k;
      docArr.push(
        <StyledTR key={fileName}>
          <StyledBoldTD>{fileName}</StyledBoldTD>
          <StyledContainerTD> {lastModified} </StyledContainerTD>
          <StyledContainerTD>
            {' '}
            {!status ? 'Not Yet Checked' : null}{' '}
            {status && status !== 'Pass' && (
              <>
                <StyledContainerSpan>
                  <StyledSpan className={`${status === 'Error' ? 'color-red' : 'color-yellow'}`}>
                    {status === 'Error' ? (
                      <ErrorCircle height={16} width={16} />
                    ) : (
                      <WarningTriangle height={16} width={16} />
                    )}
                  </StyledSpan>
                  {status === 'Error' ? 'Errors' : 'Warnings'} found:{' '}
                </StyledContainerSpan>
                <StyledRemoveButton
                  type="button"
                  onClick={() => handleModalOpenClose(arrKey, true)}
                  className="extra-style"
                >
                  View Report
                </StyledRemoveButton>
              </>
            )}
            {status && status === 'Pass' && <StyledPassQcCheck>Approved</StyledPassQcCheck>}
          </StyledContainerTD>
          <StyledTD>
            <StyledRemoveButton type="button" onClick={() => handleRemove(fileName)}>
              X <StyledSpanUnderline>Remove</StyledSpanUnderline>
            </StyledRemoveButton>
          </StyledTD>
        </StyledTR>,
      );
    }
    return docArr;
  };

  return (
    <StyledDivContainer>
      <Header h1>Finance Payroll</Header>
      <br />
      <StyledDateFilterAndButtons>
        <StyledSelect onChange={handleDateFilter}>
          {dateOptions.map((date) => {
            return (
              <StyledContainerOption value={date} key={date}>
                {date}
              </StyledContainerOption>
            );
          })}
        </StyledSelect>
        {/* eslint-disable-next-line jsx-a11y/label-has-associated-control */}
        <FileUploadLabel htmlFor="payroll-multi-file-upload" id="multi-file-upload">
          <i className="fa fa-cloud-upload" /> + Multi-Upload
          <HiddenFileInput
            id="payroll-multi-file-upload"
            type="file"
            name="payroll-multi"
            onChange={handleMultiUpload}
            multiple
          />
        </FileUploadLabel>
        <StyledMainButton type="button" className="quality-control" onClick={runQcCheck}>
          Quality Control
        </StyledMainButton>
        <StyledMainButton type="button" className="combine-files" onClick={runCombineFiles}>
          Combine Files
        </StyledMainButton>
      </StyledDateFilterAndButtons>
      <StyledDivContainer>
        <StyledTableContainer className="table mt-5">
          <StyledContainerTHEAD>
            <StyledContainerTR>
              <StyledContainerTH>Name</StyledContainerTH>
              <StyledContainerTH>Date Uploaded</StyledContainerTH>
              <StyledContainerTH>Status</StyledContainerTH>
              {/* eslint-disable-next-line jsx-a11y/control-has-associated-label */}
              <StyledContainerTH />
            </StyledContainerTR>
          </StyledContainerTHEAD>
          <StyledContainerTBODY>
            {combinedDoc.length > 0 && renderCombinedDoc()}
            {renderRequiredDocs()}
            {renderUploadedDocs()}
          </StyledContainerTBODY>
        </StyledTableContainer>
      </StyledDivContainer>
      {showModal ? (
        <PayrollModal
          show={showModal}
          header={uploadedDocs[showModalKey].fileName}
          tableHeaders={[
            'PAYCHEX ID',
            'PAYCHEX NAME',
            'SIMILARITY',
            'EMPLOYEE',
            'ERRORS',
            'WARNINGS',
          ]}
          headerInfo={uploadedDocs[showModalKey].headers}
          duplicateIdStatus={uploadedDocs[showModalKey].duplicatedIds}
          employeesWithErrors={uploadedDocs[showModalKey].qcEmployeeData}
          currentStatus={uploadedDocs[showModalKey].status}
          handleModalOpenClose={handleModalOpenClose}
        />
      ) : null}
    </StyledDivContainer>
  );
};

export default FinancePayroll;
