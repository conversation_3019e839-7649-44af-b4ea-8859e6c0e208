import { selector } from 'recoil';

import selected<PERSON>gent<PERSON>tom from './selectedAgentAtom';
import attributesAtom from './attributesAtom';

const attributesOptionsSelector = selector({
  key: 'attributesOptionsSelector',
  get: ({ get }) => {
    const { department } = get(selectedAgentAtom);
    const attributes = get(attributesAtom);
    const filteredAttributes = attributes.filter(({ departmentId }) => {
      return departmentId === department;
    });
    const insulation = filteredAttributes.filter((a) => !a.group);
    const hea = filteredAttributes.filter((a) => a.departmentId === 1);
    return { insulation, hea };
  },
});

export default attributesOptionsSelector;
