import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { Row, Col, FormInput } from '@components/global/Form';
import InteractiveButtons from './InteractiveButtons';

const InteractiveRow = styled(Row)`
  padding-bottom: 15px;
`;

const LTAWaiverMissing = ({ record = {} }) => {
  const {
    dealId,
    scheduledInsulationStartDate_Dt_: scheduledInsulationStartDate,
    revisedContractAmount,
    incorrectlyCwNotes,
    old___ReasonForIncorrectlyCwByIam: oldReasonForIncorrectlyCwByIam,
    timeStampClosedWon,
    old___TsIncorrectlyClosedWonByIam: oldTsIncorrectlyClosedWonByIam,
    old___IncorrectlyCwNotes: oldIncorrectlyCwNotes,
    isCap,
  } = record;

  return (
    <>
      <InteractiveRow>
        <InteractiveButtons dealId={dealId} title="Doc Repo" isCap={isCap} />
        <InteractiveButtons dealId={dealId} title="Work Receipt" />
      </InteractiveRow>
      <Row>
        <Col>
          <FormInput readOnly name="dealId" value={dealId} title="Deal Id" placeholder="" />
          <FormInput
            readOnly
            name="scheduledInsulationStartDate"
            value={scheduledInsulationStartDate}
            title="Scheduled Insulation Start Date (DT)"
            placeholder=""
          />
          <FormInput
            readOnly
            name="timeStampClosedWon"
            value={timeStampClosedWon}
            title="Time Stamp Closed Won"
            placeholder=""
          />
          <FormInput
            readOnly
            name="revisedContractAmount"
            value={revisedContractAmount}
            title="Revised Contract Amount"
            placeholder=""
          />
        </Col>
        <Col>
          <FormInput
            readOnly
            name="incorrectlyCwNotes"
            value={incorrectlyCwNotes}
            title="Incorrectly CW Notes"
            placeholder=""
          />
          <FormInput
            readOnly
            name="oldReasonForIncorrectlyCwByIam"
            value={oldReasonForIncorrectlyCwByIam}
            title="OLD - Reason for Incorrectly CW by IAM"
            placeholder=""
          />
          <FormInput
            readOnly
            name="oldTsIncorrectlyClosedWonByIam"
            value={oldTsIncorrectlyClosedWonByIam}
            title="OLD - TS Incorrectly Closed Won by IAM"
            placeholder=""
          />
          <FormInput
            readOnly
            name="oldIncorrectlyCwNotes"
            value={oldIncorrectlyCwNotes}
            title="OLD - Incorrectly CW Notes"
            placeholder=""
          />
        </Col>
      </Row>
    </>
  );
};

LTAWaiverMissing.propTypes = {
  record: PropTypes.shape({
    dealId: PropTypes.string,
    scheduledInsulationStartDate_Dt_: PropTypes.string,
    revisedContractAmount: PropTypes.string,
    incorrectlyCwNotes: PropTypes.string,
    timeStampClosedWon: PropTypes.string,
    old___ReasonForIncorrectlyCwByIam: PropTypes.string,
    old___TsIncorrectlyClosedWonByIam: PropTypes.string,
    old___IncorrectlyCwNotes: PropTypes.string,
    isCap: PropTypes.string,
  }),
};

export default LTAWaiverMissing;
