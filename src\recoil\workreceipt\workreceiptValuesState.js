import { atom, selector, DefaultValue } from 'recoil';
import { workReceiptFields } from '@pages/WorkReceipt/FormSchema/fieldsMap';
import { getDataIntakeFormValues } from '@components/DataIntakeForm/dataIntakeFormHelpers';

const defaultValues = getDataIntakeFormValues(workReceiptFields);
const numberOfIndices = 4; // Number of array indices
const workReceiptValuesAtom = [];

Array.from({ length: numberOfIndices }, (_, index) => {
  const atoms = {};
  Object.entries(defaultValues).forEach(([fieldName, defaultValue]) => {
    atoms[fieldName] = atom({
      key: `workReceiptValuesAtom-${index}-${fieldName}`, // Unique key for each atom
      default: defaultValue, // Each atom holds a different default value
    });
  });
  return workReceiptValuesAtom.push(atoms);
});

const workReceiptValuesState = selector({
  key: 'workReceiptValuesSelector',
  get: ({ get }) => {
    const response = workReceiptValuesAtom.map((item) => {
      const fields = {}; // Ensure a new object is created for each item
      const propertyNames = Object.keys(item);

      propertyNames.forEach((propertyName) => {
        fields[propertyName] = get(item[propertyName]);
      });

      return fields;
    });

    return response;
  },
  set: ({ set, reset }, newValues) => {
    const { index, ...rest } = newValues;

    if (newValues instanceof DefaultValue) {
      workReceiptValuesAtom.forEach((item) => {
        const propertyNames = Object.keys(item);

        propertyNames.forEach((propertyName) => {
          reset(item[propertyName]);
        });
      });

      return;
    }

    if (Array.isArray(newValues)) {
      if (workReceiptValuesAtom.length > newValues.length) {
        workReceiptValuesAtom.splice(newValues.length);
      }
      newValues.forEach((item, index) => {
        Object.entries(item).forEach(([fieldName, newValue]) => {
          if (workReceiptValuesAtom[index]?.[fieldName]) {
            set(workReceiptValuesAtom[index][fieldName], newValue);
          }
        });
      });
    } else {
      const currentAtoms = workReceiptValuesAtom[index];
      Object.entries(rest).forEach(([fieldName, newValue]) => {
        if (currentAtoms?.[fieldName]) {
          set(currentAtoms[fieldName], newValue);
        }
      });
    }
  },
});

export default workReceiptValuesState;
