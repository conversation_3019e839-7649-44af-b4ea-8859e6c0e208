import React from 'react';
import PropTypes from 'prop-types';
import styled, { css } from 'styled-components';
import { Check } from '@styled-icons/boxicons-regular/Check';
import { X } from '@styled-icons/boxicons-regular/X';

const taskIconStyle = css`
  height: 30px;
  margin-bottom: -3px;
  margin-top: -5px;
  cursor: default;
`;

const NotConfirmedIcon = styled(X)`
  ${taskIconStyle}
`;

const ConfirmedIcon = styled(Check)`
  ${taskIconStyle}
`;

const NumberedComponent = styled.div`
  height: 20px;
  margin-bottom: 5px;
  margin-left: 5px;
  margin-top: 3px;
  cursor: default;
  font-size: 18px;
`;

const Confirmation = ({ value }) => {
  let ConfirmationComponent = NotConfirmedIcon;
  let title = 'Not Confirmed';
  let confirmationCount;
  if (value && value !== 'Not Confirmed') {
    if (value === 'Confirmed') {
      ConfirmationComponent = ConfirmedIcon;
      title = value;
    } else {
      const numbersFound = value.match(/^(\d+)/); // starts with one or more digits and saves value
      const numberOfConfirmations = Number((numbersFound && numbersFound[1]) || '0');
      if (numberOfConfirmations > 0 && numberOfConfirmations < 4) {
        ConfirmationComponent = NumberedComponent;
        title = value;
        confirmationCount = numberOfConfirmations;
      }
    }
  }
  return (
    <ConfirmationComponent
      name={title}
      onClick={(event) => {
        event.stopPropagation();
      }}
    >
      {confirmationCount}
    </ConfirmationComponent>
  );
};

Confirmation.propTypes = {
  value: PropTypes.string.isRequired,
};

export default Confirmation;
