import React from 'react';
import PropTypes from 'prop-types';

import DataIntakeFormSection from './DataIntakeFormSection';

const DataIntakeFormPageSections = ({ sections = {} }) => {
  return Object.values(sections).map((section) => (
    <DataIntakeFormSection key={section.name} {...section} />
  ));
};

DataIntakeFormPageSections.propTypes = {
  sections: PropTypes.shape({}),
};

export default DataIntakeFormPageSections;
