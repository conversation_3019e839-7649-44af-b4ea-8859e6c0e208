import { cloneDeep } from 'lodash';
import { roadBlocksMap } from '../FormSchema/formSchema';

/**
 * This roadblocks map is used for merging all disclosure information onto Unit 2.
 * The roadblocks map contains all objects and fields from the roadblock section.
 * However, some values are not related to disclosures in the roadblock section,
 * and we want to exclude those from removal from index 2 and 3.
 *
 * In the following code, we have `reservedKeysForRoadblocks`, which specifies
 * the keys that should not be removed from index 2 and 3.
 */

const roadblocksDisclosuresKeys = Object.keys(roadBlocksMap);
const reserveRoadblocksKeys = [
  'yearHouseBuilt',
  'disclosureOtherDescription',
  'disclosureCSTAmbientCoDescription',
  'disclosureCstHighCoDetail',
  'systemsFailingCoMultiselect',
  'heatingSystemCO',
  'domesticHotWaterCarbonMonoxide',
  'otherCombustionAppliance',
  'otherOvenCombustionAppliance',
  'disclosureCstDraftSpillageDetail',
  'failedDraftDetailMulti',
  'failedSpillageDetailMulti',
  'failedCstOtherDraftSpillage',
];

/**
 * This custom hook is responsible for updating the roadblocks disclosures values
 * on the second object (index 1) of the multi-family work receipt sheet for Marketing Rate Excel Sheet.
 *
 * The hook removes disclosures from index 2 and 3, ensuring that only the disclosures
 * from index 0 and index 1 are sent to the multi-family generation Lambda function.
 *
 * In the multi-family sheet, under Unit 2 roadblocks, all unit disclosures are consolidated.
 * So For Unit 2 roadblocks, Unit 1 Unit 2 Unit 3 Unit 4 disclosures are merged on index 1 of
 * multi family work receipt values.
 * and then we remove indices 2 and 3 disclosures values because all disclosure values
 * are merged on unit 2 index.
 *
 * The Unit 2 roadblocks contain information for other units in the following way:
 *
 * Unit 1        Unit 2
 * disclosure A  -  Unit 1,2,4  (only Unit 3 does not have this disclosure)
 * disclosure B  -  Unit 1,2    (Units 3 and 4 do not have this disclosure)
 * disclosure C  -  Unit 4      (Units 1, 2, and 3 do not have this disclosure)
 * disclosure D  -  Unit 2,4    (Units 1 and 2 do not have this disclosure)
 */

export const useRoadblocksDisclosures = () => {
  const updateRoadblockValuesForExcelSheet = (formValues = {}) => {
    /**
     * This hoook only support multi family sheet generation for Marketing Rate.
     * That's the reason if we have single index we return formValues.
     */
    if (formValues.length === 1) return formValues;
    const updatedFormValues = cloneDeep(formValues);

    formValues.forEach((disclosures) => {
      Object.entries(disclosures).forEach(([key]) => {
        if (roadblocksDisclosuresKeys.includes(key)) {
          let newValue = '';
          // Count the number of "Yes" values for the current key across all indexes
          const mapping = formValues.map((_, index) => formValues[index][key] === 'Yes');
          const yesCount = mapping.filter(Boolean).length;

          // Use a switch statement to determine the new value based on the yesCount
          switch (yesCount) {
            case 4:
              newValue = 'ALL';
              break;
            case 3:
              if (formValues[0][key] === 'Yes') {
                if (formValues[1][key] === 'Yes' && formValues[2][key] === 'Yes')
                  newValue = 'Unit 1,2,3';
                else if (formValues[1][key] === 'Yes' && formValues[3][key] === 'Yes')
                  newValue = 'Unit 1,2,4';
                else if (formValues[2][key] === 'Yes' && formValues[3][key] === 'Yes')
                  newValue = 'Unit 1,3,4';
              } else {
                newValue = 'Unit 2,3,4';
              }
              break;
            case 2:
              if (formValues[0][key] === 'Yes') {
                if (formValues[1][key] === 'Yes') newValue = 'Unit 1,2';
                else if (formValues[2][key] === 'Yes') newValue = 'Unit 1,3';
                else if (formValues[3][key] === 'Yes') newValue = 'Unit 1,4';
              } else if (formValues[1][key] === 'Yes' && formValues[2][key] === 'Yes')
                newValue = 'Unit 2,3';
              else if (formValues[1][key] === 'Yes' && formValues[3][key] === 'Yes')
                newValue = 'Unit 2,4';
              else if (formValues[2][key] === 'Yes' && formValues[3][key] === 'Yes')
                newValue = 'Unit 3,4';

              break;
            case 1:
              if (formValues[0][key] === 'Yes') newValue = 'Unit 1';
              else if (formValues[1][key] === 'Yes') newValue = 'Unit 2';
              else if (formValues[2][key] === 'Yes') newValue = 'Unit 3';
              else if (formValues[3][key] === 'Yes') newValue = 'Unit 4';
              break;
            default:
              newValue = '';
          }

          if (newValue) {
            updatedFormValues[1][key] = newValue;
          }
        }
      });
      return updatedFormValues;
    });

    /**
     * Remove the `disclosuresOnly` values from indices 2 and 3, as these values have
     * already been merged into the Unit 2 object (index 1) in the previous hook.
     * This operation is specifically intended for Multi-Family Work Receipts (WR).
     *
     * If there are more than two form values, iterate through `updatedFormValues`
     * and remove the keys from `roadblocksDisclosuresKeys`, except for those specified
     * in `reserveRoadblocksKeys`.
     *
     * Also this hoook only support multi family sheet generation for Marketing Rate.
     * That's the reason if we have single index we return formValues from start of hook.
     */
    if (formValues.length > 2) {
      updatedFormValues.forEach((_, index) => {
        if (index > 1) {
          roadblocksDisclosuresKeys.forEach((key) => {
            if (reserveRoadblocksKeys.includes(key)) return;
            delete updatedFormValues[index][key];
          });
        }
      });
    }
    return updatedFormValues;
  };
  return updateRoadblockValuesForExcelSheet;
};
