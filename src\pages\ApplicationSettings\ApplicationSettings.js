import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

import { <PERSON><PERSON><PERSON><PERSON>, PageContainer } from '@pages/Components';
import { FormInput } from '@components/global/Form';
import { PrimaryButton } from '@components/global/Buttons';

import { isAuthorized } from '@utils/AuthUtils';
import { SettingsManager } from '@utils/APIManager';

const ErrorMessageContainer = styled.div``;

const ApplicationSettings = () => {
  const [maxMonthlyCAPVisits, setMaxMonthlyCAPVisits] = useState(0);

  useEffect(() => {
    const getMaxVisits = async () => {
      const maxVisits = await SettingsManager.getMaxMonthlyCAPVisits();
      setMaxMonthlyCAPVisits(maxVisits);
    };
    getMaxVisits();
  }, []);

  const maxMonthlyCAPVisitsInput = (
    <FormInput
      type="number"
      title="Update Max Monthly Massachusetts CAP Visits"
      placeholder="Enter a number"
      min={0}
      value={maxMonthlyCAPVisits}
      onChange={(e) => setMaxMonthlyCAPVisits(e.target.value)}
      name="maxMonthlyCAPVisits"
      key="maxMonthlyCAPVisitsInput"
    />
  );

  const authMap = {
    maxMonthlyCAPVisits: {
      Component: maxMonthlyCAPVisitsInput,
      role: 'Super User',
      department: 'HEA',
      state: 'MA',
    },
  };

  const authorizedComponents = Object.keys(authMap).map((key) => {
    const { Component, role, department, state } = authMap[key];
    if (isAuthorized(role, department, undefined, state)) return Component;
    return null;
  });

  return (
    <PageContainer>
      <PageHeader>Application Settings</PageHeader>
      {authorizedComponents}
      {!!authorizedComponents.length && (
        <PrimaryButton
          onClick={() => SettingsManager.updateMaxMonthlyCAPVisits(maxMonthlyCAPVisits)}
        >
          Save
        </PrimaryButton>
      )}
      {!authorizedComponents.length && (
        <ErrorMessageContainer>You are not authorized to view this page</ErrorMessageContainer>
      )}
    </PageContainer>
  );
};

export default ApplicationSettings;
