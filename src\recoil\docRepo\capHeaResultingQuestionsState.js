import { atom, selector, DefaultValue } from 'recoil';

const defaultValues = {
  uniqueId: null,
  visitResult: null,
  ampRun: null,
  electricProvider: null,
  largeAppliances: null,
  atticWork: null,
  knt: null,
  lta: null,
  recommendFridge: null,
  recommendFreezer: null,
  recommendWasher: null,
  recommendDehumidifier: null,
  recommendWindowAcUnit: null,
  displaceGasHeatWithHeatPump: null,
  cstFailForGasSystem: null,
  replaceDelivFuelWithHeatPump: null,
  appliancePerformed: null,
  recommendedHvac: null,
  recommendedDhw: null,
  existingHvac: null,
  existingDhw: null,
  electricalRecommended: null,
};

const capHeaResultingQuestionsState = {};

// Create a separate atom for each field on the selectedPartnerEvent.
// This way, changing the customerName doesn't need to rerender the siteId
// We can just update the customerName atom without effecting the siteId atom
Object.keys(defaultValues).forEach((fieldName) => {
  capHeaResultingQuestionsState[fieldName] = atom({
    key: `capHeaResultingQuestions-${fieldName}Atom`,
    default: defaultValues[fieldName],
  });
});

const capHeaResultingQuestionsSelector = selector({
  key: 'capHeaResultingQuestionsSelector',
  get: ({ get }) => {
    const capHeaResultingQuestions = {};
    const propertyNames = Object.keys(capHeaResultingQuestionsState);

    // Get value of each atom, then return together in an object
    propertyNames.forEach((propertyName) => {
      capHeaResultingQuestions[propertyName] = get(capHeaResultingQuestionsState[propertyName]);
    });

    return capHeaResultingQuestions;
  },
  set: ({ set, reset }, newValue) => {
    // Handle Resetting selected event
    if (newValue instanceof DefaultValue) {
      const propertyNames = Object.keys(capHeaResultingQuestionsState);
      propertyNames.forEach((propertyName) => {
        reset(capHeaResultingQuestionsState[propertyName]);
      });
      return;
    }

    // New values we are trying to update.
    // Don't need to pass the { ...capHeaResultingQuestions, [updateField]: changedValue }
    // Since we only update the properties that are present in the newValue object
    const propertyNames = Object.keys(newValue);
    propertyNames.forEach((propertyName) => {
      // We can only update atoms that we have created above. Each property needs a default value if it should be updated
      // Might also need one just to access the property.
      // Previously, when selecting an event on the calendar, it put the whole object from the backend into state.
      // This means that all properties on that object were automatically accounted for, and set to state even if they were undefined to start.
      // Now, we have no way of setting an unknown property, since there are individual atoms for each.
      // If necessary, we could potentially create a new atom and add it to the capHeaResultingQuestionsState here
      // But I think that it might be better just to enforce that all properties are created to start with
      if (!capHeaResultingQuestionsState[propertyName]) return;
      set(capHeaResultingQuestionsState[propertyName], newValue[propertyName]);
    });
  },
});

export default capHeaResultingQuestionsSelector;
