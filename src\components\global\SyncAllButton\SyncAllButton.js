import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import moment from 'moment';
import { SalesforceManager } from '@utils/APIManager';

const Button = styled.button`
  margin: 20px 20px;
  background: ${({ theme }) => theme.secondary[100]};
  border: 1px solid ${({ theme }) => theme.colors.eventGreen};
  border-radius: 4px;
  color: ${({ theme }) => theme.colors.eventGreen};
`;

const SyncAllButton = ({ startDate }) => {
  const handleClick = async () => {
    const formatedStartDate = moment(startDate)
      .startOf('week')
      .format('MM/DD/YYYY');
    const endDate = moment(startDate).endOf('week');
    const formattedEndDate = endDate.format('MM/DD/YYYY');
    await SalesforceManager.syncAllCalendarEvents(formatedStartDate, formattedEndDate);
  };

  return <Button onClick={handleClick}>SF Sync All</Button>;
};

SyncAllButton.propTypes = {
  startDate: PropTypes.instanceOf(moment).isRequired,
};

export default SyncAllButton;
