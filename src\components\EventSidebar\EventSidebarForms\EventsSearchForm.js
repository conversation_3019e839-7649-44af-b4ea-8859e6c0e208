import React, { Suspense, useState, useEffect } from 'react';
import { useRecoilValue } from 'recoil';

import styled from 'styled-components';

import { PrimaryButton } from '@components/global/Buttons';
import { Row, Col, FormInput } from '@components/global/Form';
import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import EventSidebarHeader, { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';
import { EventsSearchResultsList } from '@components/eventsSearch';
import { calendarTypeAtom } from '@recoil/app';
import { EventsManager } from '@utils/APIManager';
import { getAlphanumericString } from '@utils/functions';

const ErrorMessage = styled.div``;

const EventsSearchForm = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const calendarType = useRecoilValue(calendarTypeAtom);
  const departmentType = calendarType?.slice(0, 4);
  // TODO: This is to hide hvac walkthroughs (we have no sidebar for them)
  // Implement selection for types
  // eslint-disable-next-line no-unused-vars
  const [departmentEventTypes, setDepartmentEventTypes] = useState([departmentType, '9999']);
  const [errorMessage, setErrorMessage] = useState('');
  // TODO: implement field search options
  // eslint-disable-next-line no-unused-vars
  const [fieldsToSearch, setFieldsToSearch] = useState(['type']);
  const [searchResults, setSearchResults] = useState([]);

  // TODO: This logic should probably be broken out into a util function of some sort
  useEffect(() => {
    const listener = (event) => {
      if (event.code === 'Enter' || event.code === 'NumpadEnter') {
        event.preventDefault();
        handleSearch();
      }
    };
    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm, fieldsToSearch, departmentEventTypes]);

  const handleSearch = async () => {
    const searchResults = await EventsManager.searchEvents({
      searchTerm: getAlphanumericString(searchTerm),
      fieldsToSearch,
      departmentEventTypes,
    });

    if (searchResults.length === 0)
      setErrorMessage(`Couldn't find any events for search term '${searchTerm}'`);

    setSearchResults(searchResults);
  };

  const handleSearchTermChange = (e) => {
    const { value: newSearchTerm } = e.target;
    setErrorMessage('');
    setSearchTerm(newSearchTerm);
  };

  return (
    <SidebarForm>
      <Suspense fallback={<div>loading...</div>}>
        <EventSidebarHeader>
          <Row>
            <Col side={2}>
              <HeaderLabel>Search for Events</HeaderLabel>
            </Col>
          </Row>
        </EventSidebarHeader>
        <EventSidebarBody>
          <Row>
            <Col>
              <FormInput
                title="Search term"
                placeholder="Enter search term"
                name="searchTerm"
                value={searchTerm}
                onChange={handleSearchTermChange}
              />
            </Col>
          </Row>
          <Row>
            <Col>
              {errorMessage && <ErrorMessage>{errorMessage}</ErrorMessage>}
              <EventsSearchResultsList searchResults={searchResults} />
            </Col>
          </Row>
        </EventSidebarBody>
        <EventSidebarFooter>
          <PrimaryButton right onClick={handleSearch}>
            Search
          </PrimaryButton>
        </EventSidebarFooter>
      </Suspense>
    </SidebarForm>
  );
};

EventsSearchForm.propTypes = {};

export default EventsSearchForm;
