import React from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import PropTypes from 'prop-types';
import { useLocation } from 'react-router-dom';
import styled from 'styled-components';
import moment from 'moment';
import { Clickable } from '@components/global';
import { selectedEventState } from '@recoil/eventSidebar';
import { getFormattedArrivalWindow } from '@utils/functions';
import { isAuthorized } from '@utils/AuthUtils';
import { formValuesState } from '@recoil/dataIntakeForm';
import { calendarTypeAtom } from '@recoil/app';

const StyledTimeSlot = styled(Clickable)`
  display: flex;
  justify-content: center;
  width: 20%;
  padding: 10px 0;
  border-radius: 4px;
  margin-right: 10px;
  overflow: hidden;
  color: ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.primary[500])};
  border: 1px solid
    ${({ selected, theme }) => (selected ? theme.colors.eventGreen : theme.secondary[500])};
  font-weight: ${({ selected }) => (selected ? 'bold' : 'normal')};
`;

const TimeSlot = ({ slot }) => {
  const location = useLocation();
  const calendarType = useRecoilValue(calendarTypeAtom);
  const formValues = useRecoilValue(formValuesState);
  const [selectedSlot, setSelectedSlot] = useRecoilState(selectedEventState);
  const { type, isHEASchedulingHvacSales = false, isSchedulingVirutal = false } = selectedSlot;
  const { date, startTime, openEnd, oid, agentName } = slot;

  const isCT = calendarType?.slice(0, 4) === '0100' || type.slice(0, 4) === '0100';

  // When the TimeSlot Component is rendered in Lead Intake 2.0, it should display Arrival Windows
  // instead of Start and End Times. This adjustment is for Brand Ambassadors and Customer Intake
  // Associates to ensure that the times and arrival windows align with the BA and CIA scripts.
  const isCIA = isAuthorized('Agent', 'CIA');
  const isBA = isAuthorized('Agent', 'Marketing', true);

  const arrivalWindowTime = getFormattedArrivalWindow({
    dateTimeMoment: moment(`${date} ${startTime}`),
    isHVACSales: formValues?.heaOrHvac === 'HVAC' || isHEASchedulingHvacSales,
    type: selectedSlot.type,
    isSchedulingVirutal,
  });
  const showArrivalWindow =
    isCT ||
    ((isCIA || isBA) && location.pathname.includes('/lead-intake-dev')) ||
    isHEASchedulingHvacSales;

  const handleSelectTime = () => {
    const startEndTimes = [
      {
        start: moment(`${date} ${startTime}`),
        end: moment(`${date} ${openEnd}`),
      },
    ];
    // Have to reset everything after the time selection (oids)
    setSelectedSlot({
      oids: oid ? [oid] : [],
      agentName: oid ? agentName : null,
      startTime,
      endTime: openEnd,
      startEndTimes,
      arrivalWindow: showArrivalWindow ? arrivalWindowTime : null,
    });
  };

  // TODO: should this be based on type instead of calendarType?
  // Had an issue on the Monthly calendar where the calendarType is not accurate
  // and caused the arrival window to not show when it should
  // e.g. calendarType is 0000 but the event type is 0004 (HVAC Install)
  const isHVACInstall = calendarType?.slice(0, 4) === '0004';
  const selected = selectedSlot.date === date && selectedSlot.startTime === startTime;
  const styledStartTime = moment(startTime, 'HH:mm:ss').format('h:mm a');
  const styledEndTime = moment(openEnd, 'HH:mm:ss').format('h:mm a');
  const startEndTime = isHVACInstall ? `${styledStartTime} - ${styledEndTime}` : styledStartTime;

  return (
    <StyledTimeSlot onClick={handleSelectTime} selected={selected}>
      {showArrivalWindow ? arrivalWindowTime : startEndTime}
    </StyledTimeSlot>
  );
};

TimeSlot.propTypes = {
  slot: PropTypes.shape({
    date: PropTypes.string,
    startTime: PropTypes.string,
    openEnd: PropTypes.string,
    oid: PropTypes.string,
    agentName: PropTypes.string,
  }).isRequired,
};

export default TimeSlot;
