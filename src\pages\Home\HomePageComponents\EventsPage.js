import React from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router-dom';
import styled from 'styled-components';
import { useRecoilValue } from 'recoil';

import { Button } from '@components/global/Buttons';
import { getUserCookie, hasRole, isAuthorized } from '@utils/AuthUtils';
import { agentEventsAtom } from '@recoil/home';

import EventsList from './EventsPageComponents/EventsList';

import { StyledCalendarIcon } from '../sharedStyledComponents';

const EventsPageContainer = styled.div`
  margin-left: 12%;
  margin-right: 12%;
  @media (max-width: 1149px) {
    margin-left: 0;
    margin-right: 0;
  }
  @media (max-width: 450px) {
    padding-top: 25px;
  }
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  @media (max-width: 450px) {
    display: flex;
    flex-direction: column-reverse;
  }
`;

const StyledDate = styled.div`
  font-size: 24px;
  font-weight: 500;
  align-self: center;
  @media (max-width: 450px) {
    font-size: 20px;
    padding-bottom: 10px;
    align-self: flex-start;
  }
`;

const StyledCalendarButton = styled(Button)`
  color: #707172;
  border-color: #707172;
  @media (max-width: 450px) {
    height: fit-content;
    margin-bottom: 20px;
  }
`;

const EventsForDay = (props) => {
  const agentEvents = useRecoilValue(agentEventsAtom);
  const isAgent = hasRole('Agent');
  const isCrew = isAuthorized('Agent', 'Crew');
  const isWxAgent = hasRole('Agent', 'Insulation');
  const handlePersonalCalendar = () => {
    const { oid } = getUserCookie();
    const { history } = props;
    history.push(`/row-details/${oid}`);
  };
  return (
    <>
      {(isAgent || isCrew) && (
        <EventsPageContainer>
          <HeaderContainer>
            <StyledDate>{moment().format('MMM Do YYYY')}</StyledDate>
            {!isWxAgent && !isCrew && (
              <StyledCalendarButton onClick={handlePersonalCalendar}>
                <StyledCalendarIcon height={20} width={20} /> Personal Calendar
              </StyledCalendarButton>
            )}
          </HeaderContainer>
          {agentEvents?.length > 0 && <EventsList />}
        </EventsPageContainer>
      )}
    </>
  );
};

EventsForDay.propTypes = {
  history: PropTypes.shape({
    push: PropTypes.func,
  }).isRequired,
};

export default withRouter(EventsForDay);
