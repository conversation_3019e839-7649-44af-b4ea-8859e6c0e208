import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useRecoilState, useSetRecoilState } from 'recoil';

import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarHeader, { HeaderTitle } from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import EventSidebarFooter from '@components/EventSidebar/EventSidebarFooter';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import {
  handleFormFieldChange,
  Row,
  Col,
  FormInput,
  FormSelect,
  BackButton,
} from '@components/global/Form';
import { LoadingIndicator } from '@components/global';
import { SecondaryButton } from '@components/global/Buttons/Buttons';

import { SalesforceManager } from '@utils/APIManager';

import { selectedEventState, isSlotsSearchAtom } from '@recoil/eventSidebar';

const FindHVACSiteEvalSlotsForm = ({ handleFindSlotsClick, onClickBackButton }) => {
  // Calling this 'slotInfo' for ease of reading.
  // Using the same 'selectedEvent' atom as elsewhere in the app
  const [slotInfo, setSlotInfo] = useRecoilState(selectedEventState);
  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (slotInfo.type) {
      const { key: eventTypeName } = eventTypes.find(({ value }) => {
        return value === type;
      });
      handleFieldChange({ target: { name: 'eventTypeName', value: eventTypeName } });
    }
  }, [slotInfo.type]);

  const { sfIds, type, jobLength = 1 } = slotInfo;

  const eventTypes = [
    { key: 'Site Eval', value: '000433' },
    { key: 'Final Site Eval', value: '000434' },
  ];

  const handleOpportunityIdChange = async (e) => {
    const { value: opportunityId } = e.target;
    // If not the correct length, set the id but don't search
    if (![15, 18].includes(opportunityId.length)) {
      setSlotInfo({ ...slotInfo, sfIds: { opportunityId } });
      return;
    }
    setLoading(true);
    const salesforceInfo = await SalesforceManager.getSiteEvalDetails(opportunityId);
    setLoading(false);
    const { sfIds = {} } = salesforceInfo;
    sfIds.opportunityId = opportunityId;
    setSlotInfo({ ...slotInfo, ...salesforceInfo, sfIds, jobLength: 1 });
  };

  // Added second param 'slotInfo' defaulting to the state value.
  // This is for the race condition of setting (for example) the operationsID form field and
  // the other returned information gathered from the operationsID
  const handleFieldChange = (e, updatedEvent = slotInfo) => {
    handleFormFieldChange(e, updatedEvent, setSlotInfo);
  };

  const handleBookSlot = () => {
    setIsSlotsSearch(false);
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col size={2}>
            <HeaderTitle>
              <BackButton onClick={onClickBackButton} />
              Book Appointment
            </HeaderTitle>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            <FormInput
              required
              search
              title="opportunity id"
              placeholder="Enter Opportunity ID"
              name="sfIds.opportunityId"
              value={sfIds.opportunityId}
              onChange={handleOpportunityIdChange}
            />
            {(sfIds.dealId || sfIds.accountId) && (
              <FormSelect
                title="job type"
                name="type"
                value={type}
                options={eventTypes}
                onChange={handleFieldChange}
              />
            )}
            {type && (
              <FormInput
                readOnly
                name="jobLength"
                title="job length"
                value={jobLength}
                type="number"
                min={1}
                max={1}
                onChange={handleFieldChange}
                placeholder="Select a job to schedule"
              />
            )}
            <AvailableSlots />
            <BookSlotsButton handleBookSlots={() => handleBookSlot()} />
          </Col>
        </Row>
      </EventSidebarBody>
      <LoadingIndicator loading={loading} message="Loading..." fullscreen={false} />
      <EventSidebarFooter>
        {type && jobLength ? (
          <>
            <SecondaryButton left onClick={() => handleFindSlotsClick()}>
              View Available Slots
            </SecondaryButton>
          </>
        ) : null}
      </EventSidebarFooter>
    </SidebarForm>
  );
};

FindHVACSiteEvalSlotsForm.propTypes = {
  handleFindSlotsClick: PropTypes.func.isRequired,
  onClickBackButton: PropTypes.func.isRequired,
};

export default FindHVACSiteEvalSlotsForm;
