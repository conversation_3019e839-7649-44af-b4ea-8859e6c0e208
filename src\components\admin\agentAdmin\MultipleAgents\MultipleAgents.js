import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import styled from 'styled-components';
import { useRecoilValue, useSetRecoilState, useResetRecoilState, useRecoilState } from 'recoil';

import { ButtonContainer } from '@components/global/Form';
import { CancelButton, PrimaryButton } from '@components/global/Buttons';
import Tabs from '@components/global/Tabs/Tabs';
import { activeTabIndexAtom, calendarTypeAtom } from '@recoil/app';
import adminValidation from '@utils/adminValidation';
import { SlotsManager } from '@utils/APIManager';
import { agentAvailabilityInfoState } from '@recoil/admin/agents';
import { adminInfoHasChangedState } from '@recoil/admin';

import EditGroupAvailabilities from './EditGroupAvailabilities';

const MultipleAgentsContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow-y: auto;
`;

const Body = styled.div`
  height: 90%;
  width: 100%;
  background-color: white;
  overflow: auto;
`;

const Footer = styled.div`
  display: flex;
  align-items: center;
  flex: 0 1 60px;
  padding: 6px 12px;
  background-color: ${({ theme }) => theme.secondary[200]};
`;

const MultipleAgents = ({ cancel }) => {
  const agentInfoHasChanged = useRecoilValue(adminInfoHasChangedState);
  const agentAvailabilityInfo = useRecoilValue(agentAvailabilityInfoState);
  const resetAgentAvailabilityInfo = useResetRecoilState(agentAvailabilityInfoState);
  const setCalendarType = useSetRecoilState(calendarTypeAtom);
  const [activeTab, setActiveTab] = useRecoilState(activeTabIndexAtom(['tabs']));

  const {
    days,
    department,
    oids,
    overrideHoliday,
    startDate,
    endDate,
    startTime,
    endTime,
    action,
  } = agentAvailabilityInfo;

  useEffect(() => {
    setActiveTab(0);
    setCalendarType(null);
    resetAgentAvailabilityInfo();
  }, [setActiveTab, setCalendarType, resetAgentAvailabilityInfo]);

  const tabs = [
    {
      name: 'availability',
      title: 'Group Availabilities',
      component: <EditGroupAvailabilities />,
    },
  ];

  const openDays = async () => {
    const params = {
      days,
      startDate: moment(startDate),
      endDate: moment(endDate),
      startTime: startTime.format('hh:mm'),
      endTime: endTime.format('HH:mm'),
      oids,
      overrideHoliday,
      maxAppt: department === 6 ? 2 : 3, //  wx = 2, hvac = 3
    };
    await SlotsManager.openAgents(params);
  };

  const closeDays = async () => {
    const params = {
      days,
      startDate: moment(startDate),
      endDate: moment(endDate),
      oids,
    };
    await SlotsManager.closeAgents(params);
  };

  const saveAvailability = action === 'open' ? openDays : closeDays;

  const onSaveButtonClick = async () => {
    const editCrew = adminValidation.group[tabs[activeTab].name](agentAvailabilityInfo);
    if (editCrew && days) await saveAvailability();
    resetAgentAvailabilityInfo();
  };

  const onCancelButtonClick = () => {
    cancel(false);
    resetAgentAvailabilityInfo();
  };

  return (
    <MultipleAgentsContainer>
      <Tabs tabs={tabs} />
      <Body>{tabs[activeTab]?.component}</Body>
      <Footer>
        <ButtonContainer marginDirections={['right']}>
          <PrimaryButton left disabled={!agentInfoHasChanged} onClick={onSaveButtonClick}>
            Save
          </PrimaryButton>
          <CancelButton left onClick={onCancelButtonClick}>
            Cancel
          </CancelButton>
        </ButtonContainer>
      </Footer>
    </MultipleAgentsContainer>
  );
};

MultipleAgents.propTypes = {
  cancel: PropTypes.func.isRequired,
};

export default MultipleAgents;
