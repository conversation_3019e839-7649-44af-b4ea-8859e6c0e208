import { atom, selector, DefaultValue } from 'recoil';
import { leadIntakeFields } from '@pages/LeadIntake/FormSchema/leadIntakeMap';
import { leadIntakeFieldsCT } from '@pages/LeadIntake/FormSchema/formSchema';
import {
  addKeyNameToObject,
  getDataIntakeFormValues,
} from '@components/DataIntakeForm/dataIntakeFormHelpers';
import stateAtom from './stateAtom';

const leadIntakeFieldStates = {};

const leadIntakeFieldsByState = {
  MA: leadIntakeFields,
  CT: addKeyNameToObject({ ...leadIntakeFields, ...leadIntakeFieldsCT }),
};

const setLeadIntakeFieldStates = (state) => {
  const defaultValues = getDataIntakeFormValues(leadIntakeFieldsByState[state]);
  // Create a separate atom for each field on the leadIntakeFields.
  // This way, changing the customerName doesn't need to rerender the siteId
  // We can just update the customerName atom without effecting the siteId atom
  Object.keys(defaultValues).forEach((fieldName) => {
    if (!leadIntakeFieldStates[fieldName])
      leadIntakeFieldStates[fieldName] = atom({
        key: `leadIntake-${fieldName}Atom`,
        default: defaultValues[fieldName],
      });
  });
};

const leadIntakeValuesState = selector({
  key: 'leadIntakeValuesSelector',
  get: ({ get }) => {
    const leadIntakeFields = {};
    const state = get(stateAtom);
    setLeadIntakeFieldStates(state);
    const propertyNames = Object.keys(leadIntakeFieldStates);

    // Get value of each atom, then return together in an object
    propertyNames.forEach((propertyName) => {
      leadIntakeFields[propertyName] = get(leadIntakeFieldStates[propertyName]);
    });
    return leadIntakeFields;
  },
  set: ({ set, reset }, newValue) => {
    // Handle Resetting selected event
    if (newValue instanceof DefaultValue) {
      const propertyNames = Object.keys(leadIntakeFieldStates);
      propertyNames.forEach((propertyName) => reset(leadIntakeFieldStates[propertyName]));
      return;
    }

    // New values we are trying to update.
    // Don't need to pass the { ...leadIntakeFields, [updateField]: changedValue }
    // Since we only update the properties that are present in the newValue object
    const propertyNames = Object.keys(newValue);
    propertyNames.forEach((propertyName) => {
      // We can only update atoms that we have created above. Each property needs a default value if it should be updated
      // Might also need one just to access the property.
      // Previously, when selecting an event on the calendar, it put the whole object from the backend into state.
      // This means that all properties on that object were automatically accounted for, and set to state even if they were undefined to start.
      // Now, we have no way of setting an unknown property, since there are individual atoms for each.
      // If necessary, we could potentially create a new atom and add it to the leadIntakeFieldStates here
      // But I think that it might be better just to enforce that all properties are created to start with
      if (!leadIntakeFieldStates[propertyName]) return;
      set(leadIntakeFieldStates[propertyName], newValue[propertyName]);
    });
  },
});

export { leadIntakeFieldStates };

export default leadIntakeValuesState;
