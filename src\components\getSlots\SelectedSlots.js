import React, { useEffect } from 'react';
import moment from 'moment';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';

import { X as xIcon } from '@styled-icons/boxicons-regular/X';

import { selectedSlotsState } from '@recoil/eventSidebar';
import { Header, Clickable } from '@components/global';

const SelectedSlotsContainer = styled.div``;

const SelectedSlotContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 30px;
`;

const StyledDeleteIcon = styled(xIcon)`
  color: ${({ theme }) => theme.colors.red};
  height: 30px;
`;

const SelectedSlot = ({ slot = { oids: [], displayName: '' }, index }) => {
  const { agentName, date, startTime } = slot;
  const [selectedSlots, setSelectedSlots] = useRecoilState(selectedSlotsState);

  const removeSlot = () => {
    const newSlots = selectedSlots.filter((s, i) => i !== index);
    setSelectedSlots(newSlots);
  };

  const displayDate = moment(date, 'MM/DD/YYYY').format('ddd, MMM D');
  const displayStartTime = moment(startTime, 'HH:mm:SS').format('h:mm a');

  return (
    <SelectedSlotContainer>
      <Header h5>
        {agentName} - {displayDate}, {displayStartTime}
      </Header>
      <Clickable>
        <StyledDeleteIcon onClick={removeSlot} />
      </Clickable>
    </SelectedSlotContainer>
  );
};

SelectedSlot.propTypes = {
  slot: PropTypes.shape({
    oids: PropTypes.arrayOf(PropTypes.string),
    agentName: PropTypes.string,
    date: PropTypes.string.isRequired,
    startTime: PropTypes.string.isRequired,
    endTime: PropTypes.string.isRequired,
  }),
  index: PropTypes.number.isRequired,
};

const SelectedSlots = () => {
  // TODO: create fallback for no slots available
  const [selectedSlots, setSelectedSlots] = useRecoilState(selectedSlotsState);

  useEffect(() => {
    const resetSlots = () => setSelectedSlots([]);
    resetSlots();
  }, [setSelectedSlots]);

  if (!selectedSlots.length) return null;

  return (
    <SelectedSlotsContainer>
      <Header h4>Selected Slots:</Header>
      {selectedSlots.map((slot, index) => (
        <SelectedSlot key={`${slot.oid}${slot.date}${slot.startTime}`} index={index} slot={slot} />
      ))}
    </SelectedSlotsContainer>
  );
};

SelectedSlots.propTypes = {};

export default SelectedSlots;
