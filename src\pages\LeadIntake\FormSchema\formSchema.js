import _ from 'lodash';
import moment from 'moment';
import { hesAgentFormOptionsSelector } from '@recoil/agents';
import { isAuthorized } from '@utils/AuthUtils';
import { resetValuesBasedOnSameUnitInfo } from './resetters';
import {
  electricProvidersOptions,
  heatingFuelOptions,
  gasProviderOptions,
  leadSourceOptionsCT,
  leadSourceDetailOptionsCT,
  leadSourceOptionsMA,
  leadTypeOptions,
  marketRateIncomeEligibleOptions,
  initialIEApplicationSentViaOptions,
  helpOfferedForApplicationOptions,
  incomeFollowUpStatusOptions,
} from '../utils/consts';

const yesNoOptions = ['Yes', 'No'];
const numUnitOptions = ['1', '2', '3', '4', '5+'];
const multiFamilyConditional = (values) => values.singleOrMultiFamily === 'Multi Family';
const gasHeatConditional = (values, unitNum) => {
  return (
    values.heatingFuel?.[unitNum] === 'Gas' || values.heatingFuelCT?.[unitNum] === 'Natural Gas'
  );
};

// Helper function to check if customr is pre-approved for IE
const isCustomerPreApproved = (values) => {
  return Array.isArray(values.customerIEPreApproved)
    ? values.customerIEPreApproved[0] === true
    : values.customerIEPreApproved === true;
};

const handleLTAAgreementWarning = (values, perUnitIndex) => {
  const updateObject = _.cloneDeep(values);
  // For Market Rate tenants, display a warning that they must have the signed LTA agreement
  if (
    updateObject.incomeEligibleOrMarketRate[perUnitIndex] === 'Market Rate' &&
    updateObject.occupantType[perUnitIndex] === 'Tenant'
  )
    updateObject.formFieldErrors.incomeEligibleOrMarketRate =
      'TENANT MUST HAVE A SIGNED LANDLORD AGREEMENT';
  else delete updateObject.formFieldErrors.incomeEligibleOrMarketRate;
  return updateObject;
};

// Based on 3 questions, we determine if the customer is IE or not
const handleIncomeEligibleCT = (values, newValue, perUnitIndex, fieldName) => {
  let updateObject = _.cloneDeep(values);

  // Update the form value with the new value they selected
  updateObject[fieldName][perUnitIndex] = newValue;

  if (fieldName === 'customerIEPreApproved') {
    if (newValue) {
      // If the customer is already pre-approved, skip "Application Sent To Customer" and set as "Income Eligible"
      updateObject.incomeEligibleOrMarketRate[perUnitIndex] = 'Income Eligible';

      // Clear household size and income fields
      if (updateObject.houseHoldSize && updateObject.houseHoldSize[perUnitIndex]) {
        updateObject.houseHoldSize[perUnitIndex] = '';
      }
      if (
        updateObject.houseHoldMaxAnnualIncome &&
        updateObject.houseHoldMaxAnnualIncome[perUnitIndex]
      ) {
        updateObject.houseHoldMaxAnnualIncome[perUnitIndex] = '';
      }

      // If they are already pre approved, we don't need to check the other fields
      updateObject = handleLTAAgreementWarning(updateObject, perUnitIndex);
      return updateObject;
    }
    // If unchecking pre-approval, re-evaluate income eligibility based on other fields
    // let the code below re-check the other fields
    updateObject.incomeEligibleOrMarketRate[perUnitIndex] = '';
  }

  // These are the values that make a customer market rate. any other values are IE
  const ieIneligibleValuesMap = {
    // If customerIEPreApproved is "True", we'll never get here.
    // Adding "false" allows the checks for other income eligibility to go through if they uncheck the box
    customerIEPreApproved: false,
    receivedFinancialAssistanceFromUtility: 'None',
    financialAssistanceFromState: 'None',
    houseHoldMaxAnnualIncome: 'No',
  };

  // If the new value is not one of the ineligible values for that field, set to IE
  if (ieIneligibleValuesMap[fieldName] !== newValue) {
    updateObject.incomeEligibleOrMarketRate[perUnitIndex] = 'Application Sent To Customer';
  } else {
    // When selecting a value that we know makes them ineligible
    // check here if any of the other values make them eligible before setting back to market rate
    const ieFields = Object.keys(ieIneligibleValuesMap);

    const isEligibleFromOtherField = ieFields.some((otherFieldName) => {
      // Don't check the field that we're currently updating
      if (fieldName === otherFieldName) return false;
      // Don't check any that aren't filled in yet
      if (!values[otherFieldName][perUnitIndex]) return false;
      return values[otherFieldName][perUnitIndex] !== ieIneligibleValuesMap[otherFieldName];
    });

    if (!isEligibleFromOtherField) {
      updateObject.incomeEligibleOrMarketRate[perUnitIndex] = 'Market Rate';
    }
  }

  updateObject = handleLTAAgreementWarning(updateObject, perUnitIndex);

  const numUnits = updateObject.incomeEligibleOrMarketRate.length;
  // 50% rule, if half of the units are IE, then the whole building is IE
  if (numUnits > 1) {
    // Get total number IE
    const numIE = updateObject.incomeEligibleOrMarketRate.filter((item) =>
      ['Application Sent To Customer', 'Income Eligible'].includes(item),
    ).length;

    // If half or more are IE
    if (numIE >= numUnits / 2) {
      // No units should be market rate due to 50% rule. Preserve "Income Eligible" values for pre approved units
      updateObject.incomeEligibleOrMarketRate = updateObject.incomeEligibleOrMarketRate.map(
        (unitValue) => (unitValue === 'Market Rate' ? 'Application Sent To Customer' : unitValue),
      );
    }
  }
  const isCustomerPreApproved =
    [
      updateObject.receivedFinancialAssistanceFromUtility[perUnitIndex],
      updateObject.financialAssistanceFromState[perUnitIndex],
    ].filter((value) => {
      return value.length > 0 && value !== 'None';
    }).length > 0;
  if (Array.isArray(updateObject.customerIEPreApproved))
    updateObject.customerIEPreApproved[0] = isCustomerPreApproved;
  else updateObject.customerIEPreApproved = isCustomerPreApproved;
  return updateObject;
};

const disableHeatingFuelIfAllUnitsHaveSameInfo = (values, unitNum) => {
  if (unitNum > 0) {
    return (
      values?.allUnitsSameInfo &&
      (values?.heatingFuel[0] || values?.heatingFuelCT[0]) &&
      values?.numUnitsSchedulingToday > 1
    );
  }
  return false;
};

const setUnitSectionValues = (values, newValue) => {
  if (values?.allUnitsSameInfo && Number(values.numUnitsSchedulingToday) > 1) {
    return new Array(Number(values.numUnitsSchedulingToday)).fill(newValue);
  }
  return false;
};

const disableAccountNumberOrProvider = (values, fieldName, unitNum) => {
  if (unitNum > 0 && values?.allUnitsSameInfo) {
    return values[fieldName]?.[0] && values?.numUnitsSchedulingToday > 1;
  }
  return false;
};

const disableHasAlternateBillName = (values, fieldName, unitNum) => {
  if (!values?.allUnitsSameInfo) {
    return false;
  }

  if (unitNum > 0 && values?.allUnitsSameInfo && values?.[fieldName][0] === false) {
    return true;
  }

  if (unitNum > 0 && values?.allUnitsSameInfo) {
    return values?.[fieldName][0] && values?.numUnitsSchedulingToday > 1;
  }
  return false;
};

const disableBillName = (values, fieldName, alternateName, unitNum) => {
  if (unitNum > 0 && values?.allUnitsSameInfo) {
    return values?.[fieldName][0] && values?.numUnitsSchedulingToday > 1;
  }
  return !values[alternateName][unitNum];
};

const setBillNames = (values, fieldName, newValue) => {
  if (
    values?.allUnitsSameInfo &&
    Number(values.numUnitsSchedulingToday) > 1 &&
    values?.[fieldName].some((item) => item)
  ) {
    return new Array(Number(values.numUnitsSchedulingToday)).fill(newValue);
  }
  return false;
};

const formSchema = {
  atAnEvent: {
    text: 'Are you at an event?',
    type: 'radio',
    default: 'No',
    options: yesNoOptions,
    required: true,
    conditional: () => isAuthorized('Agent', 'Marketing', true),
  },
  campaignId: {
    conditional: (values) => values.atAnEvent === 'Yes' && isAuthorized('Agent', 'Marketing', true),
    text: 'Campaign Id',
    type: 'campaignId',
    default: '',
    required: true,
  },
  preferredLanguage: {
    text: 'Preferred Language?',
    type: 'select',
    default: 'English',
    options: [
      'English',
      'Spanish',
      'Portuguese',
      'Arabic',
      'Vietnamese',
      'Haitian Creole',
      'Chinese(Cantonese)',
      'Chinese(Mandarin)',
      'Other',
    ],
    required: true,
  },
  preferredLanguageCT: {
    text: 'Preferred Language?',
    type: 'select',
    default: 'English',
    options: ['English', 'Spanish'],
    required: true,
    perUnit: true,
  },
  heaOrHvac: {
    text: 'Are you calling about Home Energy Assessments or about HVAC Sales?',
    type: 'radio',
    default: '',
    options: ['HEA', 'HVAC'],
    required: true,
    conditionalSetter: (values, fieldValue) => {
      const valuesToBeUpdated = {};
      valuesToBeUpdated.heaOrHvac = fieldValue;
      valuesToBeUpdated.type = fieldValue === 'HEA' ? '000000' : '000100';
      return valuesToBeUpdated;
    },
  },
  hadPreviousAssessment: {
    text: 'Have you ever had a Mass Save energy assessment before at this property?',
    type: 'radio',
    default: 'No',
    options: yesNoOptions,
    required: true,
    conditional: (values) => values.heaOrHvac === 'HEA',
  },
  didYouMoveForwardWithInsulation: {
    text: 'Did you move forward with any insulation work ?',
    type: 'radio',
    default: 'No',
    options: yesNoOptions,
    required: true,
    conditional: (values) => values.hadPreviousAssessment === 'Yes',
  },
  hadPreviousAssessmentCT: {
    text: 'Have you ever had an Energy Assessment within your home before?',
    type: 'radio',
    default: 'No',
    options: yesNoOptions,
    required: true,
  },
  howLongAgoEnergyAssesmentConducted: {
    text: 'How long ago?',
    type: 'select',
    default: '',
    options: ['1 Year', '2 Year', '3 Year', '4 Year', '5 Year', '6 Year', '6+ Years'],
    required: true,
    conditional: (values) => values.hadPreviousAssessmentCT === 'Yes',
  },
  receivedFinancialAssistanceFromUtility: {
    text: 'Did you receive any financial assistance from the utilities?',
    type: 'select',
    default: '',
    options: [
      'None',
      'Eversource - Discount Rate, Matching Payment, New Start',
      'UI/SCG/CNG - Low Income Discount Rate, Matching Payment, Bill Forgiveness Program',
    ],
    required: true,
    conditional: (values, perUnitIndex) =>
      values.incomeEligibleOrMarketRate[perUnitIndex] !== 'Income Eligible',
    conditionalSetter: (values, newValue, perUnitIndex) => {
      return handleIncomeEligibleCT(
        values,
        newValue,
        perUnitIndex,
        'receivedFinancialAssistanceFromUtility',
      );
    },
  },
  financialAssistanceFromState: {
    text: 'Do you receive assistance from the state?',
    type: 'select',
    default: '',
    options: ['None', 'Energy Assistance Award Letter', 'Section 8 Housing', 'EBT/SNAP - WIC'],
    required: true,
    conditional: (values, perUnitIndex) =>
      values.incomeEligibleOrMarketRate[perUnitIndex] !== 'Income Eligible',
    conditionalSetter: (values, newValue, perUnitIndex) => {
      return handleIncomeEligibleCT(values, newValue, perUnitIndex, 'financialAssistanceFromState');
    },
  },
  houseBuilt: {
    text: 'Year Built',
    type: 'number',
    default: '',
    required: true,
  },
  squareFeet: {
    text: 'Square Feet',
    type: 'number',
    default: '',
    required: true,
  },
  houseHoldSize: {
    text: 'Household Size',
    type: 'select',
    default: '',
    options: ['1', '2', '3', '4', '5', '6', '7', '8'],
    required: true,
    conditional: (values, perUnitIndex) => {
      // If customer is pre-approved, don't show household size
      if (isCustomerPreApproved(values)) {
        return false;
      }

      // If either of these values are already set, they are already qualified for IE
      // So we don't need to show the household size or income questions
      return ![
        values.receivedFinancialAssistanceFromUtility[perUnitIndex],
        values.financialAssistanceFromState[perUnitIndex],
      ].filter((value) => !value || (value && value !== 'None')).length;
    },
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  houseHoldMaxAnnualIncome: {
    conditionalText: ({ houseHoldSize }, perUnitIndex) => {
      const maxIncomeMap = {
        1: '$47,764',
        2: '$62,460',
        3: '$77,157',
        4: '$91,854',
        5: '$106,550',
        6: '$121,247',
        7: '$124,002',
        8: '$126,758',
      };
      return `Do you make less than ${maxIncomeMap[houseHoldSize[perUnitIndex]]} per year?`;
    },
    type: 'select',
    options: yesNoOptions,
    default: '',
    required: true,
    description:
      'EXAMPLES OF INCOME INFORMATION: 1. Most recent weekly or biweekly pay stubs.\n2. Alimony, child support, pention/retirement check stub.\n3. Recently quaterly self-employment tax statement(s).\n4. Proof of Social Security or Supplemental Security Income (SSI) benefit award letter.\n5. Current unemployment letter.\n6. Zero income affidavit for anyone age 18 or older without any income.',
    conditionalSetter: (values, newValue, perUnitIndex) => {
      return handleIncomeEligibleCT(values, newValue, perUnitIndex, 'houseHoldMaxAnnualIncome');
    },
    conditional: (values, perUnitIndex) => {
      // If customer is pre-approved, don't show the income question
      if (isCustomerPreApproved(values)) {
        return false;
      }

      return !!values.houseHoldSize[perUnitIndex];
    },
  },
  incomeEligibleOrMarketRate: {
    type: 'readonly',
    style: 'bold',
    default: [],
    conditionalText: (values, perUnitIndex) => {
      if (!values.incomeEligibleOrMarketRate?.[perUnitIndex]) return '';
      return `Based on these answers, this customer is ${
        values.incomeEligibleOrMarketRate[perUnitIndex] === 'Market Rate' ? 'not ' : ''
      }income eligible.`;
    },
  },
  customerIEPreApproved: {
    text: 'Customer is pre-approved for income eligible by utility',
    type: 'checkbox',
    default: false,
    conditionalSetter: (values, newValue, perUnitIndex) =>
      handleIncomeEligibleCT(values, newValue, perUnitIndex, 'customerIEPreApproved'),
  },
  openConstructionRemodeling: {
    text: 'Is there any Open Construction or Remodeling?',
    type: 'radio',
    default: 'No',
    options: yesNoOptions,
    required: true,
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  anyMoldAsbestosORVermiculitePresent: {
    text: 'Is there any mold, asbestos, or vermiculite present in the home that they are aware of?',
    type: 'radio',
    default: 'No',
    options: yesNoOptions,
    required: true,
    conditional: (value) => value.howLongAgoEnergyAssesmentConducted === '6+ Years',
  },
  singleOrMultiFamily: {
    text: 'Is this a single family home or multi family home?',
    type: 'radio',
    default: 'Single Family',
    options: ['Single Family', 'Multi Family'],
    required: true,
    conditionalSetter: (values, fieldValue) => {
      const valuesToBeUpdated = {};
      valuesToBeUpdated.singleOrMultiFamily = fieldValue;
      valuesToBeUpdated.numUnitsSchedulingToday = '1';
      return { ...values, ...valuesToBeUpdated };
    },
  },
  numUnitsInBuilding: {
    conditional: multiFamilyConditional,
    text: 'How many units are in the building?',
    type: 'select',
    default: '1',
    options: numUnitOptions,
    required: true,
  },
  numUnitsSchedulingToday: {
    conditional: multiFamilyConditional,
    text: 'How many units are we scheduling today?',
    type: 'select',
    default: '1',
    conditionalOptions: ({ numUnitsInBuilding }) => {
      return numUnitOptions.slice(0, numUnitOptions.indexOf(numUnitsInBuilding) + 1);
    },
    required: true,
    conditionalSetter: (values, fieldValue) => {
      const schedulingToday = { numUnitsSchedulingToday: fieldValue };
      const resetValues = resetValuesBasedOnSameUnitInfo(
        { ...values, ...schedulingToday },
        fieldValue,
      );
      resetValues.numUnitsSchedulingToday = fieldValue;

      // If it's a MA HEA, update to MultiFam MA HEA
      if (fieldValue > 1 && values.type === '000000') resetValues.type = '000001';
      else if (fieldValue === 1 && values.type === '000001') resetValues.type = '000000';

      return resetValues;
    },
  },
  allUnitsSameInfo: {
    text: 'Information is the same for all units',
    type: 'checkbox',
    default: () => isAuthorized('Agent', 'CIA'),
    conditional: (values) => values.singleOrMultiFamily === 'Multi Family',
    conditionalSetter: (values, fieldValue) => {
      const resetValues = resetValuesBasedOnSameUnitInfo(values, fieldValue);
      resetValues.allUnitsSameInfo = fieldValue;
      return resetValues;
    },
  },
  occupantType: {
    text: 'Occupant Type?',
    type: 'radio',
    default: '',
    options: ['Owner Occupied', 'Tenant'],
    conditionalSetter: (values, newValue, perUnitIndex) => {
      const updateObject = _.cloneDeep(values);
      updateObject.occupantType[perUnitIndex] = newValue;
      return handleLTAAgreementWarning(updateObject, perUnitIndex);
    },
    required: true,
  },
  unitNumber: {
    text: 'Unit Number',
    type: 'input',
    default: '',
    conditionalRequire: (values) => values?.singleOrMultiFamily === 'Multi Family',
  },
  heatingFuel: {
    text: 'Heating Fuel',
    type: 'select',
    default: '',
    options: heatingFuelOptions,
    required: true,
    conditionalDisable: (values, unitNum) =>
      disableHeatingFuelIfAllUnitsHaveSameInfo(values, unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  heatingFuelCT: {
    text: 'Heating Fuel',
    type: 'select',
    default: '',
    options: ['Electric', 'Oil', 'Propane', 'Natural Gas'],
    required: true,
    conditionalDisable: (values, unitNum) =>
      disableHeatingFuelIfAllUnitsHaveSameInfo(values, unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  electricProvider: {
    text: 'Electric Provider',
    type: 'select',
    default: '',
    options: electricProvidersOptions,
    required: true,
    conditionalDisable: (values, unitNum) =>
      disableAccountNumberOrProvider(values, 'electricProvider', unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  electricProviderCT: {
    text: 'Electric Provider',
    type: 'select',
    default: '',
    options: ['Eversource', 'UI', 'Other (Unqualified)'],
    required: true,
    conditionalDisable: (values, unitNum) =>
      disableAccountNumberOrProvider(values, 'electricProviderCT', unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  electricProjectNumber: {
    text: 'Electric Project Number',
    type: 'input',
    default: '',
  },
  hasAlternateElectricBillName: {
    text:
      "Does another name appear on your electric bill or could there be other names on the electric bill? For example: maiden names, other family members whose name the utility bill is in, or legal names you do not go by? If we don't have the correct name, we cannot run your approval to do your energy assessment!",
    type: 'checkbox',
    default: false,
    conditionalDisable: (values, unitNum) =>
      disableHasAlternateBillName(values, 'hasAlternateElectricBillName', unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  electricBillName: {
    conditional: (values, unitNum = false) => values.hasAlternateElectricBillName[unitNum] === true,
    text: 'Name on electric bill:',
    type: 'input',
    required: true,
    conditionalDisable: (values, unitNum) =>
      disableBillName(values, 'electricBillName', 'hasAlternateElectricBillName', unitNum),
    conditionalSetter: (values, newValue) =>
      setBillNames(values, 'hasAlternateElectricBillName', newValue),
  },
  electricAccountNumber: {
    text: 'Electric Account Number',
    type: 'input',
    default: '',
    conditionalDisable: (values, unitNum) =>
      disableAccountNumberOrProvider(values, 'electricAccountNumber', unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  marketRateIncomeEligible: {
    text: 'Market Rate/Income Eligible',
    type: 'select',
    default: '',
    options: marketRateIncomeEligibleOptions,
    required: true,
  },
  initialIEApplicationSentVia: {
    text: 'Initial IE application sent via',
    type: 'select',
    default: '',
    options: initialIEApplicationSentViaOptions,
    conditionalRequire: (value) =>
      Array.isArray(value.marketRateIncomeEligible) &&
      [
        'Income Eligible Waiting On Application',
        'Income Eligible - Waiting on Approval',
        'Income Eligible',
      ].includes(value.marketRateIncomeEligible[0]) &&
      !isCustomerPreApproved(value),
  },
  helpOfferedForApplication: {
    text: 'Help Offered for Application?',
    type: 'select',
    default: '',
    options: helpOfferedForApplicationOptions,
    conditionalRequire: (value) =>
      Array.isArray(value.marketRateIncomeEligible) &&
      [
        'Income Eligible Waiting On Application',
        'Income Eligible - Waiting on Approval',
        'Income Eligible',
      ].includes(value.marketRateIncomeEligible[0]) &&
      !isCustomerPreApproved(value),
  },
  reasonOfNonAssistance: {
    text: 'Reason of Non-Assistance',
    type: 'textarea',
    default: '',
    conditionalRequire: (value) =>
      Array.isArray(value.marketRateIncomeEligible) &&
      [
        'Income Eligible Waiting On Application',
        'Income Eligible - Waiting on Approval',
        'Income Eligible',
      ].includes(value.marketRateIncomeEligible[0]) &&
      !isCustomerPreApproved(value),
  },
  incomeFollowUpStatus: {
    text: 'Income Follow-Up Status',
    type: 'select',
    default: '',
    options: incomeFollowUpStatusOptions,
    conditionalRequire: (value) =>
      Array.isArray(value.marketRateIncomeEligible) &&
      [
        'Income Eligible Waiting On Application',
        'Application Sent To Customer',
        'Moderate Income',
      ].includes(value.marketRateIncomeEligible[0]),
    conditionalDisable: (value) => {
      return ![
        'Income Eligible Waiting On Application',
        'Application Sent To Customer',
        'Moderate Income',
      ].includes(value.marketRateIncomeEligible[0]);
    },
  },
  incomeFollowUpDate: {
    text: 'Income Follow-Up Date',
    type: 'date',
    default: [moment()],
    useSyntheticEvent: true,
    conditionalRequire: (value) =>
      Array.isArray(value.marketRateIncomeEligible) &&
      [
        'Income Eligible Waiting On Application',
        'Income Eligible - Waiting on Approval',
        'Income Eligible',
      ].includes(value.marketRateIncomeEligible[0]),
  },
  gasProjectNumber: {
    text: 'Gas Project Number',
    type: 'input',
    default: '',
  },
  gasProvider: {
    conditional: (values, unitNum) => gasHeatConditional(values, unitNum),
    text: 'Gas Provider',
    type: 'select',
    default: '',
    options: gasProviderOptions,
    required: true,
    conditionalDisable: (values, unitNum) =>
      disableAccountNumberOrProvider(values, 'gasProvider', unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  gasProviderCT: {
    conditional: (values, unitNum) => gasHeatConditional(values, unitNum),
    text: 'Gas Provider',
    type: 'select',
    default: '',
    options: ['Yankee', 'SCG', 'CNG'],
    required: true,
    conditionalDisable: (values, unitNum) =>
      disableAccountNumberOrProvider(values, 'gasProviderCT', unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  hasAlternateGasBillName: {
    conditional: (values, unitNum) => gasHeatConditional(values, unitNum),
    text:
      "Does another name appear on your Gas bill or could there be other names on the gas bill? For example: maiden names, other family members whose name the utility bill is in, or legal names you do not go by? If we don't have the correct name, we cannot run your approval to do your energy assessment!",
    type: 'checkbox',
    default: false,
    conditionalDisable: (values, unitNum) =>
      disableHasAlternateBillName(values, 'hasAlternateGasBillName', unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  gasBillName: {
    conditional: (values, unitNum = false) =>
      gasHeatConditional(values, unitNum) && values.hasAlternateGasBillName[unitNum] === true,
    text: 'Name on gas bill:',
    type: 'input',
    default: '',
    required: true,
    conditionalDisable: (values, unitNum) =>
      disableBillName(values, 'gasBillName', 'hasAlternateGasBillName', unitNum),
    conditionalSetter: (values, newValue) =>
      setBillNames(values, 'hasAlternateGasBillName', newValue),
  },
  gasAccountNumber: {
    conditional: (values, unitNum) => gasHeatConditional(values, unitNum),
    text: 'Gas Account Number',
    type: 'input',
    default: '',
    conditionalDisable: (values, unitNum) =>
      disableAccountNumberOrProvider(values, 'gasAccountNumber', unitNum),
    conditionalSetter: (values, newValue) => setUnitSectionValues(values, newValue),
  },
  discountedRateCode: {
    conditional: (values) => values.heaOrHvac === 'HEA' && isAuthorized('Agent', 'Marketing', true),
    text: 'Does the customer have a discounted rate code?',
    type: 'select',
    default: 'No',
    options: yesNoOptions,
    required: true,
    conditionalSetter: (values, newValue) => {
      const newValues = { discountedRateCode: newValue };

      // For MA HEA, if they have a discounted rate code, set the type to 000006
      if (newValue === 'Yes') {
        if (['000000', '000001'].includes(values.type)) newValues.type = '000006';
      } else newValues.type = values.numUnitsSchedulingToday > 1 ? '000001' : '000000';

      return newValues;
    },
  },
  discountedRateCodeForCIA: {
    conditional: (values) => values.heaOrHvac === 'HEA' && isAuthorized('Agent', 'CIA'),
    text: 'Does the customer have a discounted rate code?',
    type: 'select',
    default: '',
    options: yesNoOptions,
    required: true,
    conditionalSetter: (values, newValue) => {
      const newValues = { discountedRateCodeForCIA: newValue };

      // For MA HEA, if they have a discounted rate code, set the type to 000006
      if (newValue === 'Yes') {
        if (['000000', '000001'].includes(values.type)) newValues.type = '000006';
      } else newValues.type = values.numUnitsSchedulingToday > 1 ? '000001' : '000000';

      return newValues;
    },
  },
  isWorkingWithLocalCapAgency: {
    conditional: (values) =>
      values.heaOrHvac === 'HEA' && values.discountedRateCodeForCIA === 'Yes',
    text: 'Is the customer already working with a local CAP agency?',
    type: 'select',
    default: '',
    options: yesNoOptions,
    required: true,
  },
  doYouHaveHeat: {
    conditional: (values) => values.discountedRateCodeForCIA === 'Yes',
    text: 'Do you currently have heat?',
    type: 'radio',
    default: '',
    options: yesNoOptions,
    required: true,
  },
  recentMove: {
    text: 'Have you moved in the last 2 months?',
    type: 'radio',
    default: '',
    options: yesNoOptions,
    required: true,
  },
  majorRenovations: {
    text: 'Are you doing any major renovations currently?',
    type: 'radio',
    default: 'No',
    options: yesNoOptions,
    required: true,
    description: 'For example, will any projects involve opening exterior walls or your roof?',
  },
  majorRenovationsTimeframe: {
    conditional: (values) => values.majorRenovations === 'Yes',
    text: 'When will the renovations be finished?',
    type: 'radio',
    default: 'Less than 2 months',
    options: ['Less than 2 months', 'More than 2 months'],
    required: true,
  },
  condoAssociation: {
    text: 'Are you part of a condo association?',
    type: 'radio',
    default: '',
    options: yesNoOptions,
    required: true,
    description:
      'If the customer is in a condo association, we can only serve them if there are 4 or less units in the entire association.',
  },
  howManyUnitsAreInAssociation: {
    conditional: ({ condoAssociation }) =>
      condoAssociation === 'Yes' && isAuthorized('Agent', 'CIA'),
    text: 'How many units are in the association?',
    type: 'select',
    options: numUnitOptions,
    default: '1',
  },
  facilitatorNotice: {
    conditional: multiFamilyConditional,
    text: 'The facilitator is person who can authorize work on the entire building.',
    type: 'readonly',
    color: 'red', // TODO: determine best way to change color of readonly fields
  },
  facilitatorFirstName: {
    conditional: multiFamilyConditional,
    text: 'Facilitator First Name',
    type: 'input',
    default: '',
  },
  facilitatorLastName: {
    conditional: multiFamilyConditional,
    text: 'Facilitator Last Name',
    type: 'input',
    default: '',
  },
  facilitatorPhoneNumber: {
    conditional: multiFamilyConditional,
    text: 'Facilitator Phone Number',
    type: 'phone',
    default: '',
  },
  facilitatorEmail: {
    conditional: multiFamilyConditional,
    text: 'Facilitator Email',
    type: 'input', // TODO: how should we handle email inputs here?
    default: '',
  },
  facilitatorRole: {
    conditional: multiFamilyConditional,
    text: 'Facilitator Role',
    type: 'radio',
    default: '',
    options: ['Landlord', 'Tenant', 'Relative of Landlord'],
  },
  removeFromMailingLists: {
    text: 'Remove from all mailing lists',
    type: 'checkbox',
    default: false,
  },
  customerNameScript: {
    text: 'What is your first and last name?',
    secondaryText: '(Check spelling)',
    type: 'readonly',
  },
  customerFirstName: {
    text: 'First Name',
    type: 'input',
    default: '',
    required: true,
  },
  customerLastName: {
    text: 'Last Name',
    type: 'input',
    default: '',
    required: true,
  },
  phoneScript: {
    text: 'What is the best phone number to reach you at?',
    secondaryText: '(Check number, check type of phone.)',
    type: 'readonly',
  },
  customerPrimaryPhoneNumber: {
    text: 'Primary Phone Number',
    type: 'phone',
    default: '',
    required: true,
  },
  customerPrimaryPhoneNumberType: {
    text: 'Phone Type',
    type: 'radio',
    default: '',
    options: ['Cell Phone', 'Landline'],
    required: true,
  },
  secondaryPhoneScript: {
    text: 'Do you have a second phone number you would like to add?',
    type: 'readonly',
  },
  customerSecondaryPhoneNumber: {
    text: 'Secondary Phone Number',
    type: 'phone',
    default: '',
  },
  customerSecondaryPhoneNumberType: {
    text: 'Phone Type',
    type: 'radio',
    default: '',
    options: ['Cell Phone', 'Landline'],
  },
  emailOptOut: {
    text: 'I do not have an email address.',
    type: 'checkbox',
    default: false,
    conditionalSetter: (values, newValue) => {
      if (newValue) {
        return {
          emailOptOut: true,
          customerEmail: '<EMAIL>',
        };
      }
      return {
        emailOptOut: false,
        customerEmail: values?.customerEmail !== '<EMAIL>' ? values?.customerEmail : '',
      };
    },
  },
  emailScript: {
    text: 'What is your email address?',
    secondaryText: `(If the customer is hesitant, try saying “We will not sell
          your personal information to anyone else. Your email is just for us to contact you with
          anything regarding your project.”)`,
    type: 'readonly',
  },
  customerEmail: {
    text: 'Email Address',
    type: 'input',
    default: '',
    conditionalRequire: (values) => !values.emailOptOut,
    conditional: (values) => !values.emailOptOut,
  },
  textMessageNotice: {
    text: "We'll send you a text reminder a few days before your appointment",
    type: 'readonly',
    style: 'bold',
    conditional: (values) => values.textMessageOptIn === 'Yes',
  },
  textMessageOptIn: {
    text: 'Do you consent to receive text communications regarding to your assessment?',
    type: 'select',
    options: yesNoOptions,
    default: false,
    required: true,
  },
  interestedInWaitlist: {
    text:
      'Are you interested in being added to a waitlist in case something becomes available sooner?',
    type: 'select',
    options: yesNoOptions,
    default: 'No',
    required: true,
    conditionalSetter: (values, newValue) => {
      let { waitlistDays } = values.waitlistDays;
      if (newValue === 'No') waitlistDays = [];

      return { interestedInWaitlist: newValue, waitlistDays };
    },
  },
  waitlistDays: {
    text: 'What days of the week are you interested in being added to the waitlist for?',
    type: 'multiselect',
    default: [],
    options: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    required: true,
    conditional: (values) => values.interestedInWaitlist === 'Yes',
  },
  leadSentToCompany: {
    text: 'What company should this customer be sent to?',
    type: 'select',
    options: ['NESE', 'CT Weatherproof', 'Other - Competitive Quote'],
    default: '',
    required: true,
  },
  competitiveQuoteType: {
    text: 'What type of services are you interested in?',
    type: 'multiselect',
    default: [],
    options: ['Windows', 'Insulation', 'HVAC'],
    required: true,
    conditional: (values) => values.leadSentToCompany === 'Other - Competitive Quote',
    conditionalSetter: (values, newValue) => {
      const updateObject = _.cloneDeep(values);
      updateObject.competitiveQuoteType = newValue;
      // If not insulation quote:
      if (!newValue.includes('Insulation')) return updateObject;
      // For insulation competitive quote visits
      updateObject.type = '010001';
      return updateObject;
    },
  },
  addressScript: {
    text: 'What is your address?',
    type: 'readonly',
  },
  customerAddress: {
    text: 'Address',
    type: 'address',
    default: '',
    required: true,
  },
  siteId: {
    conditional: (values) => values.heaOrHvac === 'HEA',
    text: 'Site ID',
    type: 'input',
    default: '',
    required: true,
    perUnit: true,
    style: {
      display: 'flex',
      flexDirection: 'row',
      gap: '8px',
      sectionHeader: '0 0 100%',
      flexWrap: 'wrap',
    },
  },
  leadSource: {
    text: 'How did you hear about us?',
    type: 'select',
    options: leadSourceOptionsMA,
    default: '',
    required: true,
  },
  leadSourceCT: {
    text: 'How did you hear about us?',
    type: 'select',
    options: leadSourceOptionsCT,
    default: '',
    required: true,
  },
  leadSourceDetail: {
    text: 'Lead Source Detail',
    type: 'select',
    conditionalOptions: ({ leadSourceCT }) => leadSourceDetailOptionsCT[leadSourceCT] || [],
    default: '',
    required: true,
    conditional: ({ leadSourceCT }) =>
      ['Congregation Partner', 'Solar', 'Referral', 'Direct Mail', 'Partner'].includes(
        leadSourceCT,
      ),
  },
  otherLeadSource: {
    text: 'Other Lead Source Description',
    type: 'input',
  },
  leadType: {
    text: 'Lead Type',
    type: 'select',
    options: leadTypeOptions,
    default: '',
    required: true,
  },
  referredByEmployee: {
    conditional: (values) => values.leadSource === 'Employee Referral',
    text: 'Employee Referral Code',
    type: 'input',
    default: '',
    required: true,
  },
  referredByEmployeeCT: {
    conditional: (values) => values.leadSourceCT === 'Employee Referral',
    text: 'Employee Referral',
    type: 'autocomplete',
    recoilOptions: hesAgentFormOptionsSelector,
    required: true,
  },
  referredByAuditor: {
    conditional: (values) => values.leadSource === 'Auditor Referral',
    text: 'Auditor name',
    type: 'autocomplete',
    recoilOptions: hesAgentFormOptionsSelector,
    required: true,
  },
  referredByCustomer: {
    conditional: (values) => values.leadSource === 'Customer Referral',
    text: 'Customer name',
    type: 'select',
    options: [],
    required: true,
  },
  outreachProgramInfo: {
    conditional: (values) => values.leadSource === 'Outreach Program',
    text: 'Type in additional Outreach Program information',
    type: 'input',
    required: true,
  },
  partnersInfo: {
    conditional: (values) => values.leadSource === 'Partners',
    text: 'Type in additional Partners information',
    type: 'input',
    required: true,
  },
  referralCode: {
    text: 'Referral Code(Optional)',
    type: 'input',
    default: '',
  },
  callSource: {
    text: 'Did the customer call us or did we call them?',
    type: 'select',
    options: ['They called us', 'We called them'],
    default: '',
    required: true,
  },
  isCampaignIdValid: {
    default: false,
  },
  campaign: {
    default: '',
  },
  dealId: {
    default: null,
  },
  accountId: {
    default: null,
  },
  dealName: {
    default: '',
  },
  operationsId: {
    default: null,
  },
  program: {
    default: 'NG',
  },
  leadVendor: {
    default: 'Abode',
  },
  fuelProvider: {
    default: 'National Grid',
  },
  dealId2: {
    default: [],
  },
  accountId2: {
    default: [],
  },
  operationsId2: {
    default: [],
  },
  dealId3: {
    default: [],
  },
  accountId3: {
    default: [],
  },
  operationsId3: {
    default: [],
  },
  dealId4: {
    default: [],
  },
  accountId4: {
    default: [],
  },
  operationsId4: {
    default: [],
  },
  approvalSoftware: {
    default: 'Hancock',
  },
  capApprovalLeadVendor: {
    default: '',
  },
  status: {
    default: 'Qualified',
  },
  isFormSubmitted: {
    default: false,
  },
  typeOfSystem: {
    text: 'What are you looking to replace or add regarding to your heating and cooling system?',
    default: '',
    type: 'select',
    options: ['Heating', 'Cooling', 'Hot Water Tank'],
  },
  systemLocated: {
    text: 'Where is your system located?',
    default: [],
    type: 'multiselect',
    options: [
      { key: 'Basement', value: 'Basement' },
      { key: 'Attic', value: 'Attic' },
      { key: 'Crawlspace', value: 'Crawlspace' },
    ],
  },
  basementDescription: {
    conditional: (value, perUnitIndex) => {
      return value.systemLocated?.[perUnitIndex]?.some((item) => item?.value === 'Basement');
    },
    text:
      'Can you tell me what the basement is like (Finished basement, dirt floor, etc.)? (Max Character Limit Of 100)',
    default: '',
    type: 'input',
  },
  atticDescription: {
    conditional: (value, perUnitIndex) =>
      value.systemLocated?.[perUnitIndex]?.some((item) => item?.value === 'Attic'),
    text:
      'Can you tell me what the attic is like (Pull down, attic hatch, etc.)? (Max Character Limit Of 100)',
    default: '',
    type: 'input',
  },
  crawlspaceDescription: {
    conditional: (value, perUnitIndex) =>
      value.systemLocated?.[perUnitIndex]?.some((item) => item?.value === 'Crawlspace'),
    text:
      "Can you tell me roughly how big that crawl space is (under 4 feet ceiling height can't be done)? (Max Character Limit Of 100)",
    default: '',
    type: 'input',
  },
  optionsInBasement: {
    conditional: (value, perUnitIndex) =>
      value.systemLocated?.[perUnitIndex]?.some((item) => item?.value === 'Basement'),
    text: 'Do you have any of the following in your basement?',
    type: 'multiselect',
    default: '',
    options: [
      { key: 'Slop Sink', value: 'Slop Sink' },
      { key: 'Laundry Area', value: 'Laundry Area' },
      { key: 'Sump Pump', value: 'Sump Pump' },
    ],
  },
  noOfBathrooms: {
    text: 'How many bathrooms do you have?',
    type: 'select',
    default: '',
    options: ['0', '1', '2', '3', '4+'],
  },
  typeOfHeatingEquipment: {
    text: 'Current Heating System',
    default: [],
    type: 'multiselect',
    options: [
      { key: 'Geo-Thermal', value: 'Geo-Thermal' },
      { key: 'Ductless Mini Split(s)', value: 'Ductless Mini Split(s)' },
      { key: 'Vents (Furnace)', value: 'Vents (Furnace)' },
      { key: 'Electric Base Board (Electric Heat)', value: 'Electric Base Board (Electric Heat)' },
      { key: 'Radiators/Baseboards (Boiler)', value: 'Radiators/Baseboards (Boiler)' },
    ],
  },
  ifRadiatorSteamOrHotWater: {
    conditional: (value, perUnitIndex) =>
      value.typeOfHeatingEquipment?.[perUnitIndex]?.some(
        (item) => item?.value === 'Radiators/Baseboards (Boiler)',
      ),
    text: 'Steam or Hot Water?',
    default: '',
    type: 'select',
    options: ['N/A', 'Steam (Hissing Sound)', 'Hot Water (Clanking Sound)', 'Unsure'],
  },
  heatingSystemAge: {
    text: 'Heating System Age?',
    default: '',
    type: 'select',
    options: ['0-10', '10-20', '20-30', '30+', 'Unsure'],
  },
  howOldIsYourHotWaterSystem: {
    text: 'Hot Water System Age?',
    default: '',
    type: 'select',
    options: ['Less than 4 years old', 'Greater than 4 years old', 'Unsure'],
  },
  howManyZonesThermostatsDoYouHave: {
    text: 'How many zones/thermostats do you have?',
    type: 'select',
    default: '',
    options: ['1', '2', '3', '4', '5+', 'Unsure'],
  },
  doYouHaveAc: {
    text: 'Do you have A/C?',
    type: 'select',
    default: '',
    options: ['Yes', 'No', 'Unknown'],
  },
  ifYesWhatKindOfACDoYouHave: {
    conditional: (value, perUnitIndex) => value.doYouHaveAc?.[perUnitIndex] === 'Yes',
    text: 'What kind of A/C do you have?',
    type: 'select',
    default: '',
    options: ['Central', 'Window Units', 'Ductless Minisplits', 'I dont know'],
  },
  scheduleCallBackNotes: {
    default: 'Call after renovations are completed.',
  },
  scheduleCallBackDate: {
    default: null,
  },
  isAuditorValueOnSf: {
    default: false,
  },
  type: {
    default: '000000',
  },
  formFieldErrors: {
    default: {
      anyMoldAsbestosORVermiculitePresent:
        'If any are present there is a chance we may need to cancel the appointment until remediated, or the tech will not be able to run the blower door test',
      openConstructionRemodeling:
        'Will need to push appointments out until the job will be completed. This is if there are any walls or ceilings open to insulation or missing sheetrock.',
      financialAssistanceFromState: 'NEED A COPY OF FORM TO SHOW.',
      houseHoldMaxAnnualIncome:
        'COPIES MUST BE PROVIDED TO SHOW THE INCOME FOR ANYONE WHO COLLECTS.',
    },
  },
};

// TODO: This should be refactored
const leadIntakeFieldsCT = {
  sfIds: {
    default: {},
  },
  leadId: {
    default: '',
  },
  type: {
    default: '010000',
  },
  includeAgents: {
    default: [],
  },
};

export { formSchema, leadIntakeFieldsCT };
