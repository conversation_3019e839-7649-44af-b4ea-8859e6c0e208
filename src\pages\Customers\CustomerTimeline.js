import React from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import styled, { useTheme } from 'styled-components';
import { uniqueId } from 'lodash';
import { decodeEventType } from '@homeworksenergy/utility-service';
import getEventColorForEventType from '@utils/colorDictWithEventType';
import { HorizontalLine, Header as Title } from '@components/global';
import { FlexColumn, FlexRow, InformationIcon } from './styles';

const TimelineContainer = styled.div`
  width: 100%;
  max-width: 800px;
`;
const TimelineItem = styled.div`
  position: relative;
  display: flex;
  margin-bottom: 30px;
  width: 100%;
  padding: 20px;
  @media (max-width: 575px) {
    padding: 0px;
  }
  &:last-child {
    margin-bottom: 0;
  }
  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }
`;
const LeftSide = styled.div`
  border-right: 12px solid ${(props) => props.barColor || '#ccc'};
  text-align: center;
  width: 50%;
  @media (max-width: 575px) {
    border-right: 4px solid ${(props) => props.barColor || '#ccc'};
  }
`;
const RightSide = styled.div`
  width: ${(props) => props.width || '50%'};
  text-align: ${(props) => props.textAlign || 'unset'};
`;
const EventType = styled.div`
  font-weight: bold;
  margin-bottom: 8px;
`;
const TimeDetails = styled.div`
  margin-bottom: 8px;
`;
const TimelineContent = styled.div`
  text-align: center;
`;
const TimelineTitle = styled.h4`
  font-size: 18px;
  margin-bottom: 8px;
`;
const TimelineDescription = styled.p`
  font-size: 14px;
  color: ${({ theme }) => theme.secondary[600]};
`;

export const Timeline = ({ events = [], isMobile = false, handleIdClick = () => {} }) => {
  const theme = useTheme();
  const informativeText = events?.length
    ? 'Clicking on "Timeline Card" redirects to the Salesforce Deal page.'
    : 'The customer currently does not have any events records.';

  return (
    <>
      <FlexColumn gap="0px">
        <Title h3 weight="bold">
          Timeline
        </Title>
        <Title h5>
          <InformationIcon /> {informativeText}
        </Title>
      </FlexColumn>
      <HorizontalLine />
      <TimelineContainer>
        {events?.map((event) => {
          const eventColor = getEventColorForEventType(event?.type, theme) || '';
          const { dealId = null } = event?.sfIds;
          return (
            <TimelineItem key={uniqueId()} onClick={() => handleIdClick(dealId, 'Deal__c')}>
              {isMobile ? (
                <RightSide textAlign="center" width="100%">
                  <EventType>
                    {decodeEventType(event?.type)?.businessEvent || 'Invalid Event Type'}
                  </EventType>
                  <TimelineContent>
                    <FlexRow justifyContent="center">
                      <TimelineDescription>
                        {moment(event?.startTime, 'HH:mm:ss').format('hh:mm A')}
                      </TimelineDescription>
                      <TimelineDescription> - </TimelineDescription>
                      <TimelineDescription>
                        {moment(event?.endTime, 'HH:mm:ss').format('hh:mm A')}
                      </TimelineDescription>
                    </FlexRow>
                    <TimelineTitle>Unit: {event?.numUnit || 'Not Provided'}</TimelineTitle>
                    <TimelineDescription>{event?.address}</TimelineDescription>
                  </TimelineContent>
                </RightSide>
              ) : (
                <>
                  <LeftSide barColor={eventColor}>
                    <EventType>
                      {decodeEventType(event?.type)?.businessEvent || 'Invalid Event Type'}
                    </EventType>
                    <TimeDetails>
                      <TimelineDescription> Scheduled By: {event?.scheduledBy}</TimelineDescription>
                      <FlexRow justifyContent="center">
                        <TimelineDescription>
                          {moment(event?.startTime, 'HH:mm:ss').format('hh:mm A')}
                        </TimelineDescription>
                        <TimelineDescription> - </TimelineDescription>
                        <TimelineDescription>
                          {moment(event?.endTime, 'HH:mm:ss').format('hh:mm A')}
                        </TimelineDescription>
                      </FlexRow>
                    </TimeDetails>
                  </LeftSide>
                  <RightSide>
                    <TimelineContent>
                      <EventType>
                        Scheduled Date:{' '}
                        {moment(event?.scheduledDate).format('MMMM D, YYYY hh:mm A')}
                      </EventType>
                      <TimelineDescription>
                        Total Units: {event?.numUnit || 'Not Provided'}
                      </TimelineDescription>
                      <TimelineTitle>
                        Lead Vendor: {event?.leadVendor || 'Not Provided'}
                      </TimelineTitle>
                      <TimelineDescription>{event?.address}</TimelineDescription>
                    </TimelineContent>
                  </RightSide>
                </>
              )}
            </TimelineItem>
          );
        })}
      </TimelineContainer>
    </>
  );
};

Timeline.propTypes = {
  events: PropTypes.oneOfType([
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      accountId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      customerName: PropTypes.string,
      phoneNumber: PropTypes.string,
      email: PropTypes.string,
      secondaryPhoneNumber: PropTypes.string,
      address: PropTypes.string.isRequired,
      scheduledBy: PropTypes.string.isRequired,
      scheduledDate: PropTypes.string.isRequired,
      startTime: PropTypes.string.isRequired,
      endTime: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      eventName: PropTypes.string,
      sfIds: PropTypes.shape({
        dealId: PropTypes.string.isRequired,
        eventId: PropTypes.string.isRequired,
        accountId: PropTypes.string.isRequired,
      }),
    }),
    PropTypes.array,
  ]),
  isMobile: PropTypes.bool,
  handleIdClick: PropTypes.func,
};
