# Do not change version. This is the version of aws buildspec, not the version of your buldspec file.
version: 0.2
env:
  variables:
    APP_ENV: ''
phases:
  pre_build:
    commands:
      - echo Installing source NPM dependencies...
      - npm install
  build:
    commands:
      - echo Build started on `date`
      - echo Compiling the dist folder
      - npm run build
  post_build:
    commands:
      - echo
      - |
        case "$CODEBUILD_INITIATOR" in
          "codepipeline/Scheduler2-FE-Staging")
            APP_ENV="staging";;
          "codepipeline/scheduler2-fe-ts01")
            APP_ENV="development";;
          "codepipeline/scheduler2-fe-PROD")
            APP_ENV="production";;
        esac
      - echo Current App Environment ${APP_ENV}
      - echo Removing the previous build files
      - aws s3 rm --recursive s3://hwe-static-assets/scheduler2/${APP_ENV}/
      - echo Build completed on `date`

artifacts:
  files:
    - dist/*
  discard-paths: yes
