import React, { memo } from 'react';
import { ScriptText, List, ListItem } from './styles';

export const SourceInfoScript = memo(function SourceInfoScript() {
  return (
    <List paddingInlineStart="0px">
      <ScriptText>Congratulations, MassSave Approved the visit!</ScriptText>
      <ListItem>If the customer receives a discounted rate :</ListItem>
      <ScriptText>
        Congratulations, it turns out you are income eligible! You are qualified for much better
        incentives through the Mass Save program. For income eligible customers, all work that is
        recommended by the auditor (insulation, HVAC, appliance upgrades) is covered by the program
        at no cost to the homeowner or tenants. However, landlords and tenants will both need to be
        present at the start of the assessment to sign a Landlord/Tenant Agreement.
      </ScriptText>
    </List>
  );
});
