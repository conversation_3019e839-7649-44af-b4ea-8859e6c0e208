import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

import { ChevronLeftCircle } from '@styled-icons/boxicons-regular/ChevronLeftCircle';
import { ChevronRightCircle } from '@styled-icons/boxicons-regular/ChevronRightCircle';

import { Clickable } from '@components/global';

const StyledScrollingSlotsList = styled.div`
  display: flex;
  margin-bottom: 20px;
`;

const Arrow = styled(Clickable)`
  display: flex;
  margin: 0 10px;
`;

const arrowIconStyles = `
    height: 20px;
    width: 20px;
    color: ${({ theme }) => theme.secondary[400]};
    align-self: center;
    margin: 0 auto;
`;

const LeftIcon = styled(ChevronLeftCircle)`
  ${arrowIconStyles}
`;

const RightIcon = styled(ChevronRightCircle)`
  ${arrowIconStyles}
`;

const ListContainer = styled.div`
  display: flex;
  overflow: hidden;
  flex-grow: 1;
`;

const ScrollingSlotsList = ({ children, numSlotsToDisplay = 6 }) => {
  const [startIndex, setStartIndex] = useState(0);
  const [visibleSlots, setVisibleSlots] = useState(
    children.slice(startIndex, startIndex + numSlotsToDisplay),
  );

  useEffect(() => {
    setVisibleSlots(children.slice(startIndex, startIndex + numSlotsToDisplay));
  }, [startIndex, children, numSlotsToDisplay]);

  const handleArrowClick = (direction) => {
    if (direction === '+') setStartIndex(startIndex + numSlotsToDisplay);
    if (direction === '-') setStartIndex(startIndex - numSlotsToDisplay);
  };

  const hasBefore = startIndex > 0;
  const hasAfter = startIndex + numSlotsToDisplay < children.length;

  return (
    <StyledScrollingSlotsList>
      {hasBefore && (
        <Arrow onClick={() => handleArrowClick('-')}>
          <LeftIcon />
        </Arrow>
      )}
      <ListContainer>{visibleSlots}</ListContainer>
      {hasAfter && (
        <Arrow onClick={() => handleArrowClick('+')}>
          <RightIcon />
        </Arrow>
      )}
    </StyledScrollingSlotsList>
  );
};

ScrollingSlotsList.propTypes = {
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.arrayOf(PropTypes.node)]).isRequired,
  numSlotsToDisplay: PropTypes.number,
};

export default ScrollingSlotsList;
