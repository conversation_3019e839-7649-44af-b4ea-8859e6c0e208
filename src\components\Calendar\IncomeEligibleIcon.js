import React from 'react';
import styled from 'styled-components';

// TODO: stole from HancockIcon, can create a "StyledTextIcon" component or something to share styles
const StyledIncomeEligibleIcon = styled.div`
  height: 20px;
  align-self: center;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid white;
  border-radius: 50%;
  padding: 2px;
`;

const IncomeEligibleIcon = () => {
  return <StyledIncomeEligibleIcon>IE</StyledIncomeEligibleIcon>;
};

export default IncomeEligibleIcon;
