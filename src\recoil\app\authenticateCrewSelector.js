import { selectorFamily } from 'recoil';
import { UsersManager } from '@utils/APIManager';

const authenticateCrewSelector = selectorFamily({
  key: 'authenticateCrewSelector',
  get: (params) => async () => {
    const { oid, department } = params;
    const roles = oid?.length ? await UsersManager.getUserRoles(oid) : [];

    const filteredRoles = roles.filter(({ roleId }) => {
      // 6 = Agent
      return roleId === 6;
    });
    const agentDepartmentIds = filteredRoles.map(({ departmentId }) => {
      return departmentId;
    });

    return agentDepartmentIds.includes(department);
  },
});

export default authenticateCrewSelector;
