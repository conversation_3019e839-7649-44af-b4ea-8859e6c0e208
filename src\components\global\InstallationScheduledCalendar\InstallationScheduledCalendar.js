import React, { useState, useEffect, useMemo, Suspense } from 'react';
import { useSetRecoilState, useResetRecoilState } from 'recoil';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import moment from 'moment';
import { useLocation } from 'react-router-dom';

import utilityService from '@homeworksenergy/utility-service';

// eslint-disable-next-line import/no-cycle
import { Calendar } from '@components/Calendar';
// eslint-disable-next-line import/no-cycle
import { PageHeader } from '@pages/Components';

import { calendarTypeAtom, calendarIntentionAtom } from '@recoil/app';
import { isSlotsSearchAtom, selectedEventState, showSidebarState } from '@recoil/eventSidebar';

import { addReloadCalendarListener } from '@utils/EventEmitter';
import { EventsManager } from '@utils/APIManager';
import { getUserCookie, hasRole } from '@utils/AuthUtils';
import { getSalesforceObjectTypeFromId } from '@utils/functions';

import { BookAppointmentButton, SearchVisitsButton } from './components';

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  overflow: auto;
`;

const InstallationScheduledCalendar = ({
  title = 'Install Calendar',
  type,
  includeRegionHeaders = true,
  includeAgentTypeHeaders = false,
  noButtons = false,
  showAllUsersEvents = true,
  showFindAvailableSlots = true,
  showSearchVisit = true,
  onMapViewClick = null,
}) => {
  const [rows, setRows] = useState([]);

  const { oid: userOid } = getUserCookie();

  // For some reason this was giving me an infinite loop in the useEffect if i didn't useMemo
  const oids = useMemo(() => {
    return !showAllUsersEvents ? [userOid] : null;
  }, [showAllUsersEvents, userOid]);

  const { business: department } = utilityService.decodeEventType(type);
  const hasExternalSchedulerRole = hasRole('External Scheduler', department);

  const setSelectedEvent = useSetRecoilState(selectedEventState);
  const resetSelectedEvent = useResetRecoilState(selectedEventState);
  const setCalendarType = useSetRecoilState(calendarTypeAtom);
  const setShowSidebar = useSetRecoilState(showSidebarState);
  const setIsSlotsSearch = useSetRecoilState(isSlotsSearchAtom);
  const setCalendarIntention = useSetRecoilState(calendarIntentionAtom);

  useEffect(() => {
    setCalendarIntention('view');
  }, [setCalendarIntention]);

  useEffect(() => {
    setCalendarType(type);
  }, [setCalendarType, type]);

  useEffect(() => {
    // Resets the event when switching calendars.
    // otherwise the last one shows up
    resetSelectedEvent();
    const loadEvents = async (startDate = moment().startOf('week')) => {
      const endDate = moment(startDate).add(7, 'days');
      const rows = await EventsManager.loadEvents(startDate, endDate, type, oids);
      if (!rows) return; // handle case where loadevents throws an error
      setRows(rows);
    };
    loadEvents();
    const removeReloadCalendarListener = addReloadCalendarListener(loadEvents);
    return () => removeReloadCalendarListener();
  }, [oids, resetSelectedEvent, type]);

  // Handle "find-slots" URL functionality, autopopulate IDs and open find-slots sidebar
  const urlParams = useLocation().pathname.split('/find-slots/')[1];

  useEffect(() => {
    const handleURLParams = () => {
      if (!urlParams) return;

      // Open Sidebar
      resetSelectedEvent();
      setIsSlotsSearch(true);
      setShowSidebar(true);

      // Handle salesforceId and eventType
      const [salesforceId, urlEventType] = urlParams.split('/');

      // If just the "Find Slots" portion of the URL is there, sidebar opens but there is no ID to populate
      if (!salesforceId) return;

      // Get the object type based on the 3 digit prefix of the salesforceId
      const salesforceObjectType = getSalesforceObjectTypeFromId(salesforceId) || 'account';

      // Update the selected event with the salesforce ID from the URL so that it populates when they open the form
      const updateEvent = { sfIds: { [`${salesforceObjectType}Id`]: salesforceId } };

      // If the event type in the URL is different from the one in the calendar, update it
      // This allows us to search for slots with a specific type, such as CT Sealing Service,
      // but still works with the old single parameter url functionality
      if (urlEventType && urlEventType !== type) updateEvent.type = urlEventType;
      setSelectedEvent(updateEvent);
    };
    handleURLParams();
  }, [urlParams, resetSelectedEvent, setIsSlotsSearch, setSelectedEvent, setShowSidebar, type]);

  const topButtons = [];

  if (!noButtons && showSearchVisit)
    topButtons.push(<SearchVisitsButton key="SearchVisitsButton" />);
  if (!noButtons && showFindAvailableSlots)
    topButtons.push(<BookAppointmentButton key="BookAppointmentButton" />);

  return (
    <Suspense fallback={<div>loading...</div>}>
      <Wrapper data-testid="insulation-schedule-calendar">
        <PageHeader buttons={topButtons}>{title}</PageHeader>
        {!hasExternalSchedulerRole && (
          <Calendar
            rows={rows}
            includeRegionHeaders={includeRegionHeaders}
            includeAgentTypeHeaders={includeAgentTypeHeaders}
            onMapViewClick={onMapViewClick}
          />
        )}
      </Wrapper>
    </Suspense>
  );
};

InstallationScheduledCalendar.propTypes = {
  title: PropTypes.string,
  type: PropTypes.string.isRequired,
  includeRegionHeaders: PropTypes.bool,
  includeAgentTypeHeaders: PropTypes.bool,
  noButtons: PropTypes.bool,
  showAllUsersEvents: PropTypes.bool,
  showFindAvailableSlots: PropTypes.bool,
  showSearchVisit: PropTypes.bool,
  onMapViewClick: PropTypes.func,
};

export default InstallationScheduledCalendar;
