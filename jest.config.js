const config = {
  verbose: true,
  roots: ['./'],
  testRegex: '\\.test\\.jsx?$',
  setupFiles: ['./src/setupTest.js'],
  setupFilesAfterEnv: ['./src/setupTest.js'],
  transformIgnorePatterns: ['node_modules/(?!@ngrx|(?!deck.gl)|ng-dynamic)'],
  moduleNameMapper: {
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      '<rootDir>/__mocks__/fileMock.js',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '^@(.*)$': ['<rootDir>/node_modules/$1', '<rootDir>/src/$1', '<rootDir>/node_modules/@$1'],
  },
};

module.exports = config;
