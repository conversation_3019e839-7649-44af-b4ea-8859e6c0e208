// eslint-disable-next-line import/no-extraneous-dependencies
import 'cypress-localstorage-commands';

// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
Cypress.Commands.add('login', () => {
  cy.setLocalStorage(
    'user',
    '{"oid":"c657ba90-e577-4c84-a930-964b7a6f09ed","name":"<PERSON>","roles":[{"role":"Super User","roleId":1,"department":"HEA","departmentId":1},{"role":"Super User","roleId":1,"department":"HVAC-Install","departmentId":3},{"role":"Super User","roleId":1,"department":"Insulation","departmentId":6},{"role":"Super User","roleId":1,"department":"Payroll","departmentId":7},{"role":"Super User","roleId":1,"department":"Software","departmentId":5}],"company":"HWE"}',
  );
  cy.saveLocalStorage();
});

Cypress.Commands.add('authenticateUsingToken', (window) => {
  const clientId = 'clientid';
  const tenantId = 'common';
  // eslint-disable-next-line camelcase
  const my_client_secret = 'secret';

  cy.request({
    // Given: I send auth request
    method: 'POST',
    url: `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/token`,
    header: {
      'cache-control': 'no-cache',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    form: true,
    body: {
      client_id: clientId,
      username: 'email',
      password: 'password',
      grant_type: 'password',
      client_secret: my_client_secret,
      scope: 'api://myappapi/Users.Read',
    },
  }).then((response) => {
    // When: I get a token
    const token = response.body.access_token;

    // Then: I set a token
    window.sessionStorage.setItem('my-token-name', token);
  });
});
