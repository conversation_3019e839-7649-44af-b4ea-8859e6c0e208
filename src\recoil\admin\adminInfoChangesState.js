import { atom, selector, DefaultValue } from 'recoil';

import adminInfoHasChangedState from './adminInfoHasChangedState';

const adminInfoChangesAtom = atom({
  key: 'adminInfoChangesAtom',
  default: {},
});

// set's adminInfoHasChangedState to true as the field is changed
const adminInfoChangesSelector = selector({
  key: 'adminInfoChangesSelector',
  get: ({ get }) => {
    return get(adminInfoChangesAtom);
  },
  set: ({ get, set, reset }, newField) => {
    // reset the atoms to its default value
    if (newField instanceof DefaultValue) {
      reset(adminInfoHasChangedState);
      reset(adminInfoChangesAtom);
      return;
    }

    const { name, value } = newField;
    let adminInfoChanges = get(adminInfoChangesAtom);

    adminInfoChanges = { ...adminInfoChanges, [name]: value };

    set(adminInfoChanges<PERSON>tom, adminInfoChanges);
    set(adminInfoHasChangedState, true);
  },
});

export default adminInfoChangesSelector;
