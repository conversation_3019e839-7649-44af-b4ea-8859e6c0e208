import styled from 'styled-components';
import React from 'react';
import PropTypes from 'prop-types';

const BodyContainer = styled.div`
  overflow: auto;
  flex-grow: 1;
  padding: 1.5em;
  margin-bottom: ${({ noFooter }) => (noFooter ? '0px' : '60px')};
  width: 100%;
  background: ${({ theme }) => theme.secondary[100]};
`;

// TODO: not a big fan of this noFooter stuff.
// But it's the only way i could get the whole form to show on the InsulationInstallForm
// And not have a weird gap on the FindInsulationSlotsForm
const EventSidebarBody = ({ children, noFooter = false }) => {
  return <BodyContainer noFooter={noFooter}>{children}</BodyContainer>;
};

EventSidebarBody.propTypes = {
  children: PropTypes.node.isRequired,
  noFooter: PropTypes.bool,
};

export default EventSidebarBody;
