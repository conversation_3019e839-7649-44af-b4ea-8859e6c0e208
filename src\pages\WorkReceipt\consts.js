const workReceiptTabs = [
  {
    name: 'customerAuditorInfo',
    title: 'Customer/Auditor Info',
  },
  {
    name: 'hvacInfo',
    title: 'HVAC Info',
  },
  {
    name: 'roadBlocksInfo',
    title: 'Roadblocks',
  },
  {
    name: 'proposalTerms',
    title: 'Proposal Terms',
  },
  {
    name: 'beyondMassSave',
    title: 'Beyond Mass Save',
  },
  {
    name: 'incentiveEligibilityAndAmounts',
    title: 'Incentive Eligibility and Amounts',
  },
  {
    name: 'massSaveWorkAmounts',
    title: 'Mass Save Work Amounts',
  },
  {
    name: 'basInformation',
    title: 'Bas Information',
  },
  {
    name: 'inHouseChecks',
    title: 'In-House Checks',
  },
];

const offlineWorkReceiptTabs = [
  {
    name: 'customerAuditorInfo',
    title: 'Customer/Auditor Info',
  },
  {
    name: 'hvacInfo',
    title: 'HVAC Info',
  },
  {
    name: 'roadBlocksInfo',
    title: 'Roadblocks',
  },
  {
    name: 'incentiveEligibilityAndAmounts',
    title: 'Incentive Eligibility and Amounts',
  },
  {
    name: 'basInformation',
    title: 'Bas Information',
  },
];

const capWorkReceiptTabs = [
  {
    name: 'customerAuditorInfo',
    title: 'Customer/Auditor Info',
  },
  {
    name: 'propertyOwner',
    title: 'Property Owner',
  },
  {
    name: 'hvacInfo',
    title: 'HVAC Info',
  },
  {
    name: 'roadBlocksInfo',
    title: 'Roadblocks',
  },
  {
    name: 'ampIsm',
    title: 'AMP ISM',
  },
  {
    name: 'readOnlyFields',
    title: 'Read Only',
  },
];

const ventingOptions = {
  highVenting: [
    {
      name: 'Ridge Vent (LF)',
      price: 0.13,
      fieldName: 'ridgeVent',
      parent: 'highVentingTotal',
    },
    {
      name: '8" RV',
      price: 0.35,
      fieldName: 'eightRv',
      parent: 'highVentingTotal',
    },
    {
      name: '12" RV',
      price: 1,
      fieldName: 'twelveRv',
      parent: 'highVentingTotal',
    },
    {
      name: '12" Turbine Vent',
      price: 4,
      fieldName: 'twelveTurbineVent',
      parent: 'highVentingTotal',
    },
    {
      name: 'Custom GV sqft',
      price: 0.5,
      fieldName: 'customGvSqft',
      parent: 'highVentingTotal',
    },
    {
      name: '12" x 12" GV',
      price: 0.5,
      fieldName: 'twelveByTwelve',
      parent: 'highVentingTotal',
    },
    {
      name: '12"x 18" GV',
      price: 0.75,
      fieldName: 'twelveByEighteen',
      parent: 'highVentingTotal',
    },
    {
      name: '12"x 24" GV',
      price: 1,
      fieldName: 'twelveByTwentyfour',
      parent: 'highVentingTotal',
    },
  ],

  lowVenting: [
    {
      name: '4" x 16" SV',
      price: 0.22,
      fieldName: 'fourBySixteen',
      parent: 'lowVentingTotal',
    },
    {
      name: '6" x 16" SV',
      price: 0.33,
      fieldName: 'sixBySixteen',
      parent: 'lowVentingTotal',
    },
    {
      name: '8" x 16" SV',
      price: 0.44,
      fieldName: 'eightBySixteen',
      parent: 'lowVentingTotal',
    },
    {
      name: 'Cont. SV (LF)',
      price: 0.12,
      fieldName: 'contSv',
      parent: 'lowVentingTotal',
    },
    {
      name: 'Dripedge SV',
      price: 0.06,
      fieldName: 'dripedgeSv',
      parent: 'lowVentingTotal',
    },
    {
      name: 'SV Pucks',
      price: 0.01,
      fieldName: 'svPucks',
      parent: 'lowVentingTotal',
    },
    {
      name: ">3' (Lo) GV sqft",
      price: 0.5,
      fieldName: 'lowGvSqft',
      parent: 'lowVentingTotal',
    },
  ],
  highVentingOptions: [
    {
      name: 'High 50%',
      color: 'rgba(0, 100, 0, .6)',
      hover: 'hover-green',
      calcs: [
        { color: 'rgba(0, 128, 0, .2)', price: 0.13, ratio: 0.5 },
        { color: 'rgba(0, 128, 0, .2)', price: 1, ratio: 0.5 },
        { color: 'rgba(0, 128, 0, .2)', price: 0.35, ratio: 0.5 },
        { price: 0.75, ratio: 0.5 },
        { price: 4, ratio: 0.5 },
      ],
    },
    {
      name: 'High 60%',
      color: 'rgba(50, 205, 50, .8)',
      hover: 'hover-lightgreen',
      calcs: [
        { color: 'rgba(0, 128, 0, .2)', price: 0.13, ratio: 0.6 },
        { color: 'rgba(0, 128, 0, .2)', price: 1, ratio: 0.6 },
        { color: 'rgba(0, 128, 0, .2)', price: 0.35, ratio: 0.6 },
        { price: 0.75, ratio: 0.6 },
        { price: 4, ratio: 0.6 },
      ],
    },
    {
      name: 'High 40%',
      color: 'rgba(50, 205, 50, .8)',
      hover: 'hover-lightgreen',
      calcs: [
        { color: 'rgba(0, 128, 0, .2)', price: 0.13, ratio: 0.4 },
        { color: 'rgba(0, 128, 0, .2)', price: 1, ratio: 0.4 },
        { color: 'rgba(0, 128, 0, .2)', price: 0.35, ratio: 0.4 },
        { price: 0.75, ratio: 0.4 },
        { price: 4, ratio: 0.4 },
      ],
    },
    {
      name: 'High 75%',
      color: 'rgba(255, 217, 0, .8)',
      hover: 'hover-gold',
      calcs: [
        { color: 'rgba(0, 128, 0, .2)', price: 0.13, ratio: 0.75 },
        { color: 'rgba(0, 128, 0, .2)', price: 1, ratio: 0.75 },
        { color: 'rgba(0, 128, 0, .2)', price: 0.35, ratio: 0.75 },
        { price: 0.75, ratio: 0.75 },
        { price: 4, ratio: 0.75 },
      ],
    },
    {
      name: 'High 25%',
      color: 'rgba(255, 217, 0, .8)',
      hover: 'hover-gold',
      calcs: [
        { color: 'rgba(0, 128, 0, .2)', price: 0.13, ratio: 0.25 },
        { color: 'rgba(0, 128, 0, .2)', price: 1, ratio: 0.25 },
        { color: 'rgba(0, 128, 0, .2)', price: 0.35, ratio: 0.25 },
        { price: 0.75, ratio: 0.25 },
        { price: 4, ratio: 0.25 },
      ],
    },
    {
      name: 'High 100%',
      color: 'rgba(255, 140, 0, .8)',
      hover: 'hover-orange',
      calcs: [
        { color: 'rgba(0, 128, 0, .2)', price: 0.13, ratio: 1 * 2 },
        { color: 'rgba(0, 128, 0, .2)', price: 1, ratio: 1 * 2 },
        { color: 'rgba(0, 128, 0, .2)', price: 0.35, ratio: 1 * 2 },
        { price: 0.75, ratio: 1 * 2 },
        { price: 4, ratio: 1 * 2 },
      ],
    },
  ],
  lowVentingOptions: [
    {
      name: 'Low 50%',
      color: 'rgba(0, 100, 0, .6)',
      hover: 'hover-green',
      calcs: [
        {
          color: 'rgba(0, 128, 0, .2)',
          price: 0.22,
          ratio: 0.5,
          key: 'lowkey1',
        },
        { price: 0.33, ratio: 0.5, key: 'lowkey2' },
        { price: 0.44, ratio: 0.5, key: 'lowkey3' },
        { price: 0.75, ratio: 0.5, key: 'lowkey4' },
        {
          color: 'rgba(0, 100, 0, .6)',
          value: 'Best',
          size: '10px',
          key: 'lowkey5',
        },
      ],
    },
    {
      name: 'Low 40%',
      color: 'rgba(50, 205, 50, .8)',
      hover: 'hover-lightgreen',
      calcs: [
        {
          color: 'rgba(0, 128, 0, .2)',
          price: 0.22,
          ratio: 0.4,
          key: 'lowkey1a',
        },
        { price: 0.33, ratio: 0.4, key: 'lowkey2a' },
        { price: 0.44, ratio: 0.4, key: 'lowkey3a' },
        { price: 0.75, ratio: 0.4, key: 'lowkey4a' },
        {
          color: 'rgba(50, 205, 50, .8)',
          value: 'Preferred',
          size: '10px',
          key: 'lowkey5a',
        },
      ],
    },
    {
      name: 'Low 60%',
      color: 'rgba(50, 205, 50, .8)',
      hover: 'hover-lightgreen',
      calcs: [
        {
          color: 'rgba(0, 128, 0, .2)',
          price: 0.22,
          ratio: 0.6,
          key: 'lowkey1b',
        },
        { price: 0.33, ratio: 0.6, key: 'lowkey2b' },
        { price: 0.44, ratio: 0.6, key: 'lowkey3b' },
        { price: 0.75, ratio: 0.6, key: 'lowkey4b' },
        {
          color: 'rgba(50, 205, 50, .8)',
          value: 'Preferred',
          size: '10px',
          key: 'lowkey5b',
        },
      ],
    },
    {
      name: 'Low 25%',
      color: 'rgba(255, 217, 0, .8)',
      hover: 'hover-gold',
      calcs: [
        {
          color: 'rgba(0, 128, 0, .2)',
          price: 0.22,
          ratio: 0.25,
          key: 'lowkey1c',
        },
        { price: 0.33, ratio: 0.25, key: 'lowkey2c' },
        { price: 0.44, ratio: 0.25, key: 'lowkey3c' },
        { price: 0.75, ratio: 0.25, key: 'lowkey4c' },
        {
          color: 'rgba(255, 217, 0, .8)',
          value: 'Last Resort',
          size: '10px',
          key: 'lowkey5c',
        },
      ],
    },
    {
      name: 'Low 75%',
      color: 'rgba(255, 217, 0, .8)',
      hover: 'hover-gold',
      calcs: [
        {
          color: 'rgba(0, 128, 0, .2)',
          price: 0.22,
          ratio: 0.75,
          key: 'lowkey1d',
        },
        { price: 0.33, ratio: 0.75, key: 'lowkey2d' },
        { price: 0.44, ratio: 0.75, key: 'lowkey3d' },
        { price: 0.75, ratio: 0.75, key: 'lowkey4d' },
        {
          color: 'rgba(255, 217, 0, .8)',
          value: 'Last Resort',
          size: '10px',
          key: 'lowkey5d',
        },
      ],
    },
    {
      name: 'Low 0%',
      color: 'rgba(255, 140, 0, .8)',
      hover: 'hover-orange',
      calcs: [
        {
          color: 'rgba(0, 128, 0, .2)',
          price: 0.22,
          ratio: 0,
          key: 'lowkey1e',
        },
        { price: 0.33, ratio: 0, key: 'lowkey2e' },
        { price: 0.44, ratio: 0, key: 'lowkey3e' },
        { price: 0.75, ratio: 0, key: 'lowkey4e' },
        {
          color: 'rgba(255, 140, 0, .8)',
          value: 'Need Perm.',
          size: '10px',
          key: 'lowkey5e',
        },
      ],
    },
    {
      name: 'Low 100%',
      color: 'rgba(255, 0, 0, .8)',
      hover: 'hover-red',
      calcs: [
        {
          color: 'rgba(0, 128, 0, .2)',
          price: 0.22,
          ratio: 1 * 2,
          key: 'lowkey1f',
        },
        { price: 0.33, ratio: 1 * 2, key: 'lowkey2f' },
        { price: 0.44, ratio: 1 * 2, key: 'lowkey3f' },
        { price: 0.75, ratio: 1 * 2, key: 'lowkey4f' },
        {
          color: 'rgba(255, 0, 0, .8)',
          value: 'Need Perm',
          size: '10px',
          key: 'lowkey5f',
        },
      ],
    },
  ],
};

const bmsOptions = {
  '': '',
  'Above Grade Basement Wall Polyiso': 5.77,
  'Attic Floor R60 - Additional 3" Open Blow Cellulose': 0.78,
  'Attic Floor R60 - 3" Larger FGB': 1.1,
  'Bath Fan Vent to Roof': 243.67,
  'Attic Floor 6" Open Blow Cellulose': 2.79,
  'Attic Floor 7" Open Blow Cellulose': 2.89,
  'Attic Floor 8" Open Blow Cellulose': 2.99,
  'Attic Floor - Polyiso': 6.0,
  'Basement Air Sealing': 0.5,
  'Basement Ceiling - 3" Unfaced Fiberglass Batt': 2.51,
  'Basement Ceiling - 6" Unfaced Fiberglass Batt': 2.82,
  'Basement Ceiling - 9" Unfaced Fiberglass Batt': 3.2,
  'Co Detector': 60.0,
  'Custom Item - ': 0,
  'Door Sweep': 52.22,
  'Door Weather Stripping (Exterior)': 63.62,
  'Drop Ceiling - Install Polyiso': 3.58,
  'Drop Ceiling - Install Polyiso (with multiplier)': 3.82,
  'Flex Duct Insulation - R8': 9.55,
  'Flip/Slash Insulation': 0.54,
  'Floor - Attic Floor Build Up 2X10’S Plus 19/32Th Inch Osb': 20.31,
  'Floor - Attic Floor Build Up with Original Plywood': 15.33,
  'Floor - Install New Flooring 19/32Th Osb [No Build Up]': 4.5,
  'Floor - Pull Up Flooring and Reinstall': 1.57,
  'Floor - Relocate Flooring In Attic [Create a Flooring stack in attic]': 1.37,
  'Floor - Relocate Flooring Out Of Attic': 2.39,
  'Floor - Removal With Disposal': 2.89,
  'Garage Ceiling Sheetrock': 3.92,
  'Hatch R60': 20.0,
  'Hot Water Pipe Wrap': 40.0,
  'Install Basement Door To Bulkhead - Custom, Z Back Plywood': 1993.0,
  'Install Basement Door To Bulkhead - Insulated, Metal': 1993.0,
  'Install New Pull Down Stair': 1734.0,
  'Install Fire-Resistant Cellulose in Interior Walls (Sound Proofing)': 3.15,
  'Lights - Recessed Box w/ Mass Save Damming': 56.89,
  'Lights - Recessed Box w/out Mass Save Damming': 56.89,
  'Pipe Tenting - PolyIso': 5.77,
  'Plaster Repair - Interior Drill & Blow': 0.81,
  'Plaster Repair - Temporary Access': 235.0,
  'Polyiso Foam Board': 5.77,
  'Pull Down Stair - Electrical Adder': 300.0,
  'Replace Existing Pull Down Stair': 950.0,
  'Ridge Vent: Shingle Color _________________': 50.0,
  'Separate Unconditioned Spaces (with thin Polyiso)': 3.45,
  'Standard Plywood Removal - Non-Flooring (Create Stack)': 1.37,
  'Standard Plywood Removal - Non-Flooring (With Disposal Fee)': 3.87,
  'Storage Moving 1-way (minimum 100 sqft)': 0.9,
  'Storage Moving 2-way (minimum 50 sqft)': 1.65,
  'Storage Moving Multiple Floor Multiplier': 0.25,
  'Storage Removal with Disposal (maximum 200 sqft)': 3.25,
  'Thermal Tent': 271.98,
  'Turbine Vent in Inaccessible Attic': 206.95,
  'Unfinished Plywood Hatch': 27.87,
  'Unvented Stack Pipe - Install Air Admittance Valve': 149.31,
  'Vapor Barrier Removal and Disposal': 1.85,
  'Vent Dryer - Cut Hole In Finished Ceiling': 33.22,
  'Vent Dryer - Convert Basement Window to Wood': 325.2,
  'Vent Dryer - Convert Basement Window to Plexiglass': 375.2,
  'Vent Dryer - Through Attic Roof': 375.2,
  'Vent Dryer - Through Rim Joist': 268.75,
  'Vent Dryer - Replace Broken Hose': 8.2,
  'Vent Kitchen Fan to Roof (Shape=_________;Size=_________)': 330.25,
  'Wall Insulation - 4" Dense Pack Cellulose': 3.38,
  'WHF from Living Space Side': 82.0,
};

const accuracyIssuesOptions = [
  '',
  'None',
  'Customer Not Home',
  "Doesn't know how to use phone/computer",
  'No Smart Phone',
  'Not Approved',
  'Not Expecting a Virtual',
  'Scheduled for Wrong Date/Time',
  'Time Expectations',
  'Wrong Address',
  'Wrong Lead Vendor',
  'Wrong Phone Number',
  'Other',
];

const workScopeOptions = [
  'Attic Floor',
  'Attic Wall',
  'Attic Slope',
  'Exterior Wall',
  'Basement',
  'Crawlspace',
  'Garage Ceiling',
  'Kneewall',
];

const electricityProviderOptions = ['', 'Eversource', 'National Grid', 'Municipal'];

const hvacQualityOptions = ['', 'Open', 'Excellent', 'Good', 'Fair', 'Poor'];

const hvacStatusOptions = [
  'Open',
  'Very Interested',
  'Interested',
  'Undecided',
  'Not Interested',
  'Not Interested - Great Candidate',
  'Yes - Out of Territory',
  'No - Out of Territory',
  'Quote Developed In Home',
];

const hvacInterestOptions = [
  '',
  'Open',
  'Converting Oil to Gas',
  'Boiler',
  'Furnace',
  'Air Conditioning',
  'On Demand',
  'Heat Pump - Supplemental',
  'Heat Pump - Whole Home Displacement',
  'Heat Pump - Partial',
  'Heat Pump - Other',
];

const contractOptions = [
  '',
  "Customer's Own Contractor",
  'Customer Not Interested',
  'Requests HWE Subcontractor',
];

const contractorChoiceOptions = [...contractOptions, 'Quote Developed in Home'];

const ktRemediationContractorChoiceOptions = [...contractOptions, 'K+T Scheduled In-House'];

const ktDetailOptions = ['Likely Live', 'Likely Dead', 'Unable to Estimate'];

const gasAvailableOptions = ['', 'Gas in Home', 'Gas on Street', 'None'];

const flooringTypeOptions = ['', 'Plywood', 'Planks'];

const flooringNailedOrUnsecuredOptions = ['', 'Nailed Down', 'Unsecured'];

const hvacAgeOptions = ['', '30+', '< 5-year old', '5 to 11-year old', '12 to 29-year old'];

const existingDHWoptions = [
  '',
  'Electric',
  'Indirect',
  'Tankless Coil',
  'Other',
  'Combination Boiler',
  'Sealed Combustion Tank',
  'Heat Pump',
  'On Demand',
  'Condensing Storage',
  'Atmospheric Tank',
];

const confirmSystemTypeOptions = [
  '',
  'Furnace',
  'Air Conditioning',
  'Electric Baseboard',
  'Stove',
  'Pellet Stove/Boiler',
  'Modern Stove/Indoor Boiler',
  'Heat Pump with Gas Backup',
  'Heat Pump with Propane Backup',
  'Steam Boiler',
  'Hot Water Boiler',
  'Condensing Furnace',
  'Heat Pump',
  'Open',
  'Heat Pump with Oil Backup',
  'Outdoor Boiler',
];

const aboveAmbientCo35Options = ['', 'Boiler', 'DHW', 'Furnace'];

const systemsFailingCoOptions = ['Heating System', 'DHW', 'Other'];

const failedCSTHighCoDetailOptions = [
  '',
  'Furnace 10 years or younger - Likely',
  'Boiler 20 years or younger - Likely',
  'Furnace 11 years or older - Needs Replacement',
  'Boiler 21 years or older - Needs Replacement',
  'Older System <300 ppm - Maybe Fix',
  'DHW',
];

const failedSpillageDetailsOptions = ['Heating System', 'DHW', 'Other'];

const kAndTSignOptions = [
  'Push Button Switches',
  'Ungrounded Outlets',
  'Home Built Pre-1950',
  'Baseboard Outlets',
  'Disconnected Ceramic Knob or Tube',
  'Single Outlet per Room',
  'Room(s) Missing Outlets',
  'Connected K&T Wiring',
  'Aged Gas Lamps',
  'Homeowner Confirmation - Home Rewired',
  'Homeowner Confirmation - K&T LIve',
];

const kAndTLocationsOptions = [
  'Basement',
  'Kitchen',
  'Bathroom',
  'Attic',
  'Living Area(s)',
  'Bedroom(s)',
  'Throughout Home',
  'Knee Wall',
];

const heatingSystemTypeOptions = ['', 'Electric', 'Oil', 'Gas', 'Propane', 'Wood', 'Kerosene'];

const returnVisitOptions = [
  '',
  'Scheduled',
  'Rescheduled',
  'Performed',
  'Review Complete',
  'Return ISM Review Fail',
  'Return ISM Review Fixed Pending Review',
];

const returnVisitCstStatusOptions = [
  '',
  'Scheduled',
  'Rescheduled',
  'Performed',
  'Reviewed - Eligible',
  'Reviewed - Ineligible',
  'Return CST Review Fail',
  'Return CST Review Fixed Pending Review',
];

const returnVisitCstDetailOptions = [
  '',
  'Abode Fail',
  'Abode Pass',
  'CMA Revisit CST - Fail',
  'CMA Revisit CST - Pass',
  'EM Revisit CST - Fail',
  'EM Revisit CST - Pass',
];

const yesNoOptions = ['Yes', 'No'];

const paperworkSentEmailOptions = ['', 'Yes', 'No', 'Sent Manually'];

const hesVisitResultDetailsOptions = [
  '',
  'Closed Won',
  'PreWeatherization Barrier',
  'High Probability',
  'Qualified Out',
  'Not in EM Home',
  'Incorrectly Closed Won',
  'ICW Fixed- Pending Review',
];

const workReceiptModeOptions = ['CAP', 'Offline', 'Offline-Multi-Family'];

const unitOptions = [1, 2, 3, 4];

const failedDraftMap = {
  'Heating System': 'Heating_System_Draft',
  DHW: 'DHW_Draft',
  Other: 'Other_Draft',
};

const failedSpillageMap = {
  'Heating System': 'Heating_System_Spillage',
  DHW: 'DHW_Spillage',
  Other: 'Other_Spillage',
};

export {
  capWorkReceiptTabs,
  offlineWorkReceiptTabs,
  workReceiptTabs,
  ventingOptions,
  yesNoOptions,
  kAndTLocationsOptions,
  kAndTSignOptions,
  failedSpillageDetailsOptions,
  failedCSTHighCoDetailOptions,
  systemsFailingCoOptions,
  aboveAmbientCo35Options,
  confirmSystemTypeOptions,
  existingDHWoptions,
  hvacAgeOptions,
  flooringNailedOrUnsecuredOptions,
  flooringTypeOptions,
  gasAvailableOptions,
  contractorChoiceOptions,
  ktRemediationContractorChoiceOptions,
  ktDetailOptions,
  hvacInterestOptions,
  hvacStatusOptions,
  accuracyIssuesOptions,
  hvacQualityOptions,
  electricityProviderOptions,
  workScopeOptions,
  bmsOptions,
  heatingSystemTypeOptions,
  returnVisitOptions,
  returnVisitCstStatusOptions,
  returnVisitCstDetailOptions,
  hesVisitResultDetailsOptions,
  paperworkSentEmailOptions,
  workReceiptModeOptions,
  unitOptions,
  failedDraftMap,
  failedSpillageMap,
};
