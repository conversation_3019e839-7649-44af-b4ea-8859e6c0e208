import React from 'react';
import PropTypes from 'prop-types';
import { useTheme } from 'styled-components';

import { chopString, getCityAndZipcodeFromAddress, renderStartAndEndTimes } from '@utils/functions';

import Event from './Event';

const HVACInstallEvent = ({ event = { sfIds: {}, address: {}, numUnit: null }, ...otherProps }) => {
  const theme = useTheme();
  const { address, eventTypeName, notes, needsDocs, lock, equipmentOrderStatus, type } = event;

  let { customerName } = event;
  if (!customerName) customerName = 'No customer found';

  const cityAndZipcode = getCityAndZipcodeFromAddress(address);

  const eventColorMap = {
    'Not Ordered': theme.colors.eventG,
    Backordered: theme.colors.eventG,
    'Partially Delivered': theme.colors.eventG,
    Ordered: theme.colors.eventH,
    'Order Confirmed': theme.colors.eventH,
    Delivered: theme.colors.eventI,
    '000433': theme.colors.eventA, // Site Eval
    '000434': theme.colors.shadowBlock, // Final Site Eval
  };

  const eventColor = eventColorMap[equipmentOrderStatus || type] || theme.colors.eventG;

  const isSiteEval = ['000433', '000434'].includes(type);

  return (
    <Event
      event={event}
      backgroundColor={eventColor}
      tooltip={notes.officeNotes}
      headerText={cityAndZipcode || 'No Address Found'}
      bodyHeader={chopString(customerName, 30)}
      bodyText={[eventTypeName, renderStartAndEndTimes(event), !isSiteEval && equipmentOrderStatus]}
      needsDocs={!isSiteEval && needsDocs}
      pinnable
      lock={lock}
      {...otherProps}
    />
  );
};

HVACInstallEvent.propTypes = {
  event: PropTypes.shape({
    id: PropTypes.string.isRequired,
    sfIds: PropTypes.shape({
      contractId: PropTypes.string,
    }),
    lock: PropTypes.bool.isRequired,
    customerName: PropTypes.string.isRequired,
    eventTypeName: PropTypes.string.isRequired,
    address: PropTypes.shape({
      city: PropTypes.string,
      postalCode: PropTypes.string,
    }),
    numUnit: PropTypes.number,
    startTime: PropTypes.string,
    endTime: PropTypes.string,
    type: PropTypes.string,
    notes: PropTypes.shape({
      officeNotes: PropTypes.string,
      fieldNotes: PropTypes.string,
    }),
    needsDocs: PropTypes.bool,
    equipmentOrderStatus: PropTypes.string,
  }),
};

export default HVACInstallEvent;
