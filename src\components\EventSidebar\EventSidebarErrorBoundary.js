import React from 'react';
import styled from 'styled-components';
import { SidebarForm } from './EventSidebarForms';

const StyledErrorBoundary = styled(SidebarForm)`
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  text-align: center;
  width: 100%;
  height: 100%;
`;

const ErrorHeader = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.h1};
  color: ${({ theme }) => theme.colors.red};
`;
const ErrorDescription = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.h3};
  color: ${({ theme }) => theme.secondary[500]};
`;

const EventSidebarErrorBoundary = () => {
  return (
    <StyledErrorBoundary>
      <ErrorHeader>We&apos;re sorry!</ErrorHeader>
      <ErrorDescription>
        An error occurred. Please contact Software with any information that may help reproduce the
        issue.
      </ErrorDescription>
    </StyledErrorBoundary>
  );
};

export default EventSidebarErrorBoundary;
