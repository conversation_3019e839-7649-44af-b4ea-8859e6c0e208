import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { Printer } from '@styled-icons/boxicons-regular/Printer';

import { DocRepoManager } from '@utils/APIManager';
import { startLoading, stopLoading } from '@utils/EventEmitter';

const PrinterIcon = styled(Printer)`
  height: 26px;
  background-color: ${({ theme }) => theme.secondary[100]};
  border-radius: 4px;
  margin-top: auto;
  & :hover {
    color: ${({ theme }) => theme.secondary[500]};
    cursor: pointer;
  }
`;

const PrintDocsButton = ({ department, dealId }) => {
  const printDocs = async () => {
    const response = await DocRepoManager.printEventDocs(department, dealId);
    if (!response) return;
    const bytes = new Uint8Array(response.length);
    response.forEach((element, index) => {
      bytes[index] = element;
    });
    startLoading('Loading Docs...');
    const file = new Blob([bytes], { type: 'application/pdf' });
    const fileURL = window.URL.createObjectURL(file);
    stopLoading();
    window.open(fileURL);
  };

  return <PrinterIcon onClick={printDocs} />;
};

PrintDocsButton.propTypes = {
  department: PropTypes.string.isRequired,
  dealId: PropTypes.string.isRequired,
};

export default PrintDocsButton;
