import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { Header as Title } from '@components/global';
import { InfoCircle } from '@styled-icons/boxicons-solid/InfoCircle';
import Swal from 'sweetalert2/dist/sweetalert2';

import { FormSelect, handleFormFieldChange, FormInput, FormTextBox } from '@components/global/Form';
import ProductManager from '@utils/APIManager/ProductManager';
import { useRecoilState, useRecoilValue, useSetRecoilState, useResetRecoilState } from 'recoil';
import productOptionsSelector from '@recoil/products/activeProductsSelector';
import { productSelector } from '@recoil/products';
import { activeProductsAtom } from '@recoil/products/activeProductsAtom';
import { PrimaryButton } from '@components/global/Buttons';
import { throwError } from '@utils/EventEmitter';
import { HvacProductList } from './HvacProductsList';

const Container = styled.div`
  padding: 20px;
`;

const ProductList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
`;

const AddProductButton = styled.div`
  margin: 20px 0;
  text-align: center;
  font-size: 16px;
  color: #007bff;
  cursor: pointer;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
`;

const InformationMessage = styled.div`
  display: flex;
  justify-content: center;
`;

export const InformationIcon = styled(InfoCircle)`
  color: ${({ theme }) => theme.primary[400]};
  height: 20px;
  margin: 0;
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
`;

const TableRowContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
`;

const ModalContent = styled.div``;

const ModalContainer = styled.div`
  background: white;
  border-radius: 8px;
  position: relative;
  padding: 20px;
  width: 400px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
`;

const ActionContainer = styled.div`
  display: flex;
  justify-content: center;
`;

const CloseButton = styled.button`
  position: absolute;
  opacity: 1;
  border: none;
  width: 35px;
  height: 35px;
  left: 30em;
  top: -16px;
  border-radius: 50%;
  color: ${({ theme }) => theme.secondary[400]};
  background: ${({ theme }) => theme.secondary[100]};
  box-shadow: 0px 7px 12px ${({ theme }) => theme.colors.greyShadow};
`;

export const HvacProducts = ({ hvacInstallOpportunityId = '' }) => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = React.useState(false);
  const [formState, setFormState] = useRecoilState(productSelector);
  const setProductsOptions = useSetRecoilState(activeProductsAtom);
  const productOptions = useRecoilValue(productOptionsSelector);
  const resetForm = useResetRecoilState(productSelector);
  const [productsList, setProductList] = React.useState(false);
  const {
    id,
    quantity,
    unitPrice,
    manufacturer,
    series,
    systemSize,
    indoorModelNumber,
    outdoorModelNumbers,
    numberOfZonesHeads,
    description,
    opportunityLineItemId,
  } = formState;

  const getProductsForOppId = async () => {
    if (hvacInstallOpportunityId) {
      const response = await ProductManager.getAllProductsByOpportunityId(hvacInstallOpportunityId);
      setProductList(response);
    }
  };

  const getProductOptions = async () => {
    const options = await ProductManager.getProductsOptions();
    getProductsForOppId();
    setProductsOptions(options);
  };

  const handleEdit = (selectedProduct) => {
    setIsEditModalOpen(true);
    const updatedValues = {
      ...selectedProduct,
      id: selectedProduct.pricebookEntryId,
      opportunityLineItemId: selectedProduct.id,
    };

    setFormState(updatedValues);
  };
  const showModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsEditModalOpen(false);
    resetForm();
  };

  const handleDelete = async (productId, name) => {
    const { value: confirmed } = await Swal.fire({
      icon: 'warning',
      title: 'Important: Delete Action',
      text: `Are you sure you want to delete ${name}?`,
      confirmButtonText: 'Yes',
      showCancelButton: true,
      cancelButtonText: 'No',
    });
    if (!confirmed) return false;

    await ProductManager.deleteProductById({
      productId,
      name,
    });
    return getProductsForOppId();
  };

  const handleFieldChange = (e) => {
    return handleFormFieldChange(e, formState, setFormState);
  };

  const validateForm = () => {
    if (!quantity || !unitPrice || !manufacturer || !series || !indoorModelNumber) {
      Swal.fire({
        icon: 'error',
        title: 'Invalid: Fields',
        text:
          'Please complete all required fields: Quantity, Sales Price, Manufacturer, Series, and Indoor Model Number.',
      });
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    try {
      const isFormValid = validateForm();
      if (!isFormValid) return false;
      let response = null;
      const updatedFormState = {
        ...formState,
        quantity: Number(quantity),
        salesPrice: Number(unitPrice),
        numberOfZonesHeads: Number(numberOfZonesHeads),
      };
      delete updatedFormState.unitPrice;
      if (isEditModalOpen) {
        response = await ProductManager.updateHvacProduct({
          ...updatedFormState,
          opportunityLineItemId,
        });
      } else {
        response = await ProductManager.createHvacProduct({
          ...updatedFormState,
          opportunityId: hvacInstallOpportunityId,
        });
      }

      if (response) {
        await getProductsForOppId();
      }
      return closeModal();
    } catch (error) {
      console.log(error);
      return throwError(error);
    }
  };

  useEffect(() => {
    getProductOptions();
  }, []);

  return (
    <Container>
      <HeaderContainer>
        <AddProductButton onClick={showModal}>+ Add Product</AddProductButton>
      </HeaderContainer>

      {productsList && (
        <ProductList>
          <HvacProductList
            list={productsList}
            handleDelete={handleDelete}
            handleEdit={handleEdit}
          />
        </ProductList>
      )}

      {productsList.length === 0 && (
        <InformationMessage h5>
          <InformationIcon /> No products have been added for this opportunity. Please use the
          &quot;Add Product&quot; button to add products.
        </InformationMessage>
      )}

      {(isModalOpen || isEditModalOpen) && (
        <ModalOverlay>
          <ModalContainer>
            <CloseButton onClick={closeModal}>x</CloseButton>
            <ModalContent>
              <Title h2 weight={500}>
                {isEditModalOpen ? 'Edit Product' : 'Add Product'}
              </Title>

              <FormSelect
                name="id"
                value={id}
                title="Product"
                options={productOptions}
                readOnly={isEditModalOpen}
                onChange={handleFieldChange}
              />
              <TableRowContainer>
                <FormInput
                  required
                  title="Quantity"
                  name="quantity"
                  value={quantity}
                  onChange={handleFieldChange}
                  type="number"
                />
                <FormInput
                  required
                  title="Sales Price"
                  name="unitPrice"
                  value={unitPrice}
                  onChange={handleFieldChange}
                  type="number"
                />
                <FormInput
                  required
                  title="Manufacturer"
                  name="manufacturer"
                  value={manufacturer}
                  onChange={handleFieldChange}
                />
                <FormInput
                  required
                  name="series"
                  title="Series"
                  value={series}
                  onChange={handleFieldChange}
                />
                <FormInput
                  name="systemSize"
                  title="System Size"
                  value={systemSize}
                  onChange={handleFieldChange}
                />
                <FormInput
                  required
                  name="indoorModelNumber"
                  title="Indoor Model Number"
                  value={indoorModelNumber}
                  onChange={handleFieldChange}
                />
                <FormInput
                  name="outdoorModelNumbers"
                  title="Outdoor Model Number"
                  value={outdoorModelNumbers}
                  onChange={handleFieldChange}
                />
                <FormInput
                  type="number"
                  name="numberOfZonesHeads"
                  title="Number Of Zones Heads"
                  value={numberOfZonesHeads}
                  onChange={handleFieldChange}
                />
              </TableRowContainer>
              <FormTextBox
                name="description"
                title="Description"
                type="textarea"
                value={description}
                onChange={handleFieldChange}
              />

              <ActionContainer>
                <PrimaryButton onClick={handleSubmit}>Submit</PrimaryButton>
              </ActionContainer>
            </ModalContent>
          </ModalContainer>
        </ModalOverlay>
      )}
    </Container>
  );
};

HvacProducts.propTypes = {
  hvacInstallOpportunityId: PropTypes.string,
};
