/* eslint-disable react/destructuring-assignment */
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { Clickable } from '@components/global';
import { ErrorModal } from './Modals';

const StyledClickable = styled(Clickable)`
  align-self: stretch;
  display: flex;
  flex-direction: column;
  border: 1px solid;
  margin: 5px;
  background-color: #87cefa;
  &.error {
    background-color: ${({ theme }) => theme.colors.red};
  }
`;

const StyledMessage = styled.div`
  font-weight: bold;
`;

const ErrorEvent = (props) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <StyledClickable conClick={() => setShowModal(true)}>
        <StyledMessage>Error in event!</StyledMessage>
        Notify software.
      </StyledClickable>
      <ErrorModal
        showModal={showModal}
        closeModal={() => setShowModal(false)}
        event={props.event}
      />
    </>
  );
};

ErrorEvent.propTypes = { event: PropTypes.shape({ id: PropTypes.string }).isRequired };

export default ErrorEvent;
