import { useEffect } from 'react';
import { addSuccessListener, stopLoading } from '@utils/EventEmitter';
import swal from 'sweetalert2/dist/sweetalert2';

const SuccessMessage = () => {
  useEffect(() => {
    const removeSuccessListener = addSuccessListener(showSuccessMessage);
    return () => {
      removeSuccessListener();
    };
  }, []);

  const showSuccessMessage = (message) => {
    stopLoading();
    return swal.fire({
      title: message,
      confirmButtonText: 'OK',
      icon: 'success',
    });
  };
  return null;
};

export default SuccessMessage;
