import moment from 'moment';
import { atom, selector, DefaultValue } from 'recoil';

const selectedPatternState = selector({
  key: 'selectedPatternSelector',
  get: ({ get }) => {
    const selectedPattern = get(selectedPatternAtom);
    return selectedPattern;
  },
  set: ({ set, reset }, newSelectedPattern) => {
    if (newSelectedPattern instanceof DefaultValue) {
      return reset(selectedPatternAtom);
    }
    const { startDate, endDate } = newSelectedPattern;

    // Change these to moment after they come back from database
    return set(selectedPatternAtom, {
      ...newSelectedPattern,
      startDate: moment(startDate),
      endDate: moment(endDate),
    });
  },
});

const selectedPatternAtom = atom({
  key: 'selectedPatternAtom',
  default: {
    patternId: null,
    departmentId: 1,
    patternName: '',
    startDate: moment(),
    endDate: moment().add(1, 'week'),
    startTime: '08:00:00',
    endTime: '08:30:00',
    repeatInterval: '7 days',
    eventType: '999999',
    oids: [],
    address: null,
  },
});

export default selectedPatternState;
