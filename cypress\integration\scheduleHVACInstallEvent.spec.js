before(() => {
  cy.login();
  cy.saveLocalStorage();
});

beforeEach(() => {
  cy.restoreLocalStorage();
});

describe('HVAC Install Schedule | schedule event workflow', () => {
  /*
   * ********** ********** **********
   * Schedule a HVAC Install visit
   * ********** ********** **********
   */

  const mockSalesForceObject = {
    dealId: 'a0h770000004OecAAE',
    lastName: 'Alice Wonderland',
  };

  it('can navigate to the HVAC Install scheduling page', () => {
    cy.visit('/');
    cy.contains('Create Visit').click();
    cy.contains('HVAC Install Visit').click();
    cy.contains('Schedule New HVAC Install Visit');
    cy.get('[data-testid=hvac-install-deal-id-input]').should('be.visible');
    cy.get('[data-testid=hvac-install-account-id-input]').should('be.visible');
  });

  it('enters a deal id', () => {
    cy.get('[data-testid=hvac-install-deal-id-input]').type(mockSalesForceObject.dealId);
  });

  it('selects a job to schedule', () => {
    cy.get('select').select(1);
  });

  it('changes job length', () => {
    cy.get('[data-testid=work-form]')
      .children('input')
      .type('{uparrow}')
      .type('{downarrow}');
  });

  it('searches for openings', () => {
    cy.contains('Search for Openings').click();
  });

  it('selects a time slot', () => {
    cy.contains('Available time:')
      .first()
      .click();
  });

  it('clicks submit', () => {
    cy.contains('Submit').click();
  });

  it('confirms submission', () => {
    cy.contains('Yes').click();
  });

  /*
   * ********** ********** **********
   * Cancel the HVAC Install visit from above test
   * ********** ********** **********
   */

  it('switches to HVAC Install Schedule', () => {
    cy.visit('/view-hvac-install-schedule');
  });

  it('locates correct visit', () => {
    cy.contains(mockSalesForceObject.lastName).click();
  });

  it('cancels HVAC Install visit', () => {
    cy.contains('Cancel Event').click();
  });

  it('confirms cancelling HVAC Install visit', () => {
    cy.contains('Yes').click();
  });

  it('confirms HVAC Install visit was cancelled', () => {
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(8000);
    cy.contains('OK').click();
  });
});
