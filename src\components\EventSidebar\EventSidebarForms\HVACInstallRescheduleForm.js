import React, { useState, useEffect } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import { useRecoilValue, useSetRecoilState } from 'recoil';
import { SlotsManager } from '@utils/APIManager';

import SidebarForm from '@components/EventSidebar/EventSidebarForms/SidebarForm';
import EventSidebarHeader, { HeaderLabel } from '@components/EventSidebar/EventSidebarHeader';
import EventSidebarBody from '@components/EventSidebar/EventSidebarBody';
import AvailableSlots from '@components/getSlots/AvailableSlots';
import BookSlotsButton from '@components/getSlots/BookSlotsButton';
import { Row, Col, FormInput, FormDateTimePicker } from '@components/global/Form';
import { selectedEventState, availableSlotsAtom } from '@recoil/eventSidebar';

const HVACInstallRescheduleForm = ({ handleRescheduleClick }) => {
  const [startDate, setStartDate] = useState(moment());
  const [numDays, setNumDays] = useState(14);

  const setAvailableSlots = useSetRecoilState(availableSlotsAtom);
  const event = useRecoilValue(selectedEventState);

  useEffect(() => {
    getSlots();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startDate, numDays]);

  const getSlots = async () => {
    const { type, address, numUnit, jobLength, oid, oids } = event;

    const params = {
      type,
      jobLength,
      numUnit,
      includeAgents: [oid || oids[0]],
      address,
      date: moment(startDate),
      numDays,
    };

    const openingsResponse = await SlotsManager.getNextAvailableSlots(params);
    setAvailableSlots(openingsResponse);
  };

  const handleDateChange = async (date) => {
    setStartDate(moment(date));
  };

  const handleNumDaysChange = (event) => {
    const numDays = event.target.value;
    setNumDays(numDays);
  };

  return (
    <SidebarForm>
      <EventSidebarHeader>
        <Row>
          <Col side={2}>
            <HeaderLabel>Reschedule HVAC Install:</HeaderLabel>
          </Col>
        </Row>
      </EventSidebarHeader>
      <EventSidebarBody>
        <Row>
          <Col>
            <FormDateTimePicker
              title="Start Search Date:"
              name="startDate"
              value={startDate}
              onChange={handleDateChange}
              dateFormat="MM/dd/yyyy"
            />
            <FormInput
              title="# of Days to Show"
              name="numDays"
              value={numDays}
              onChange={handleNumDaysChange}
              type="number"
              min={0}
              max={5}
            />
            <AvailableSlots />
            <BookSlotsButton handleBookSlots={() => handleRescheduleClick()} />
          </Col>
        </Row>
      </EventSidebarBody>
    </SidebarForm>
  );
};

HVACInstallRescheduleForm.propTypes = {
  handleRescheduleClick: PropTypes.func.isRequired,
};

export default HVACInstallRescheduleForm;
