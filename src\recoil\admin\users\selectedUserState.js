import getComplexObjectRecoilState from '@recoil/utils/getComplexObjectRecoilState';

const defaultValues = {
  attributes: [],
  assignedCrew: '',
  company: '',
  companyName: '',
  userLead: null,
  departments: [],
  department: '',
  departmentName: '',
  displayName: undefined, // Used for create crew, so we can display "enter crew info" instead
  email: '',
  personalEmail: '',
  firstname: '',
  home: null,
  lastname: '',
  manager: null,
  number: null,
  oid: '',
  phoneNumber: null,
  programs: [],
  region: '',
  regionAbbreviation: '',
  sfId: null,
  sfId2: null,
  state: '',
  homeAddress: '',
  dayStartAddress: '',
  type: 'user',
  active: true,
  eventTypes: [],
  sendNotification: false,
  notificationChannel: [],
};

const [selectedUserFieldStates, selectedUserSelector] = getComplexObjectRecoilState(
  'selectedUser',
  defaultValues,
);

export { selectedUserFieldStates };

export default selectedUserSelector;
