import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { useRecoilState } from 'recoil';
import { selectedFiltersState } from '@recoil/admin';

const FilterDropdownContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 16px;
  border-radius: 6px;
  padding: 16px;
  background-color: ${({ theme }) => theme.secondary[100]};
`;
const Option = styled.div`
  margin: 5px 10px;
  cursor: pointer;
`;
const SelectedOption = styled.div``;
const OptionsContainer = styled.div`
  width: 100%;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  flex-direction: column;
  display: ${({ isOpen }) => (isOpen ? 'flex' : 'none')};
  background-color: ${({ theme }) => theme.secondary[100]};
  border: 1px solid ${({ theme }) => theme.secondary[200]};
  border-radius: 0 6px 6px 6px;
`;

const FilterDropdown = ({ filter = { options: [] } }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { displayName: filterDisplayName, name: filterName, options } = filter;
  const [selectedFilters, setSelectedFilters] = useRecoilState(selectedFiltersState);

  const selectedFilter = selectedFilters[filterName] || { name: 'All' };

  const toggleIsOpen = () => setIsOpen(!isOpen);

  const handleOptionSelect = (optionValue) => {
    setSelectedFilters({ ...selectedFilters, [filterName]: optionValue });
  };

  return (
    <FilterDropdownContainer onClick={toggleIsOpen}>
      <SelectedOption>
        {filterDisplayName}: {selectedFilter.name}
      </SelectedOption>

      <OptionsContainer isOpen={isOpen}>
        {options.map((option) => {
          const { name, value } = option;
          return (
            <Option onClick={() => handleOptionSelect(option)} key={`${name}-${value}`}>
              {name}
            </Option>
          );
        })}
      </OptionsContainer>
    </FilterDropdownContainer>
  );
};

FilterDropdown.propTypes = {
  filter: PropTypes.shape({
    displayName: PropTypes.string,
    name: PropTypes.string,
    key: PropTypes.string,
    options: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string,
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      }),
    ),
  }),
};

export default FilterDropdown;
